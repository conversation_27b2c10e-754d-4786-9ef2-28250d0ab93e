import { z } from 'zod';

/**
 * Interface para serviços de criptografia
 * Abstrai a implementação específica de hash de senhas
 */
export interface CryptoService {
  /**
   * Gera hash de uma senha
   * @param password - Senha em texto plano
   * @param rounds - Número de rounds para o salt (opcional)
   */
  hash(password: string, rounds?: number): Promise<string>;

  /**
   * Compara uma senha com seu hash
   * @param password - Senha em texto plano
   * @param hash - Hash armazenado
   */
  compare(password: string, hash: string): Promise<boolean>;

  /**
   * Gera um salt com o número especificado de rounds
   * @param rounds - Número de rounds
   */
  generateSalt(rounds: number): Promise<string>;

  /**
   * Valida se um hash é válido
   * @param hash - Hash a ser validado
   */
  isValidHash(hash: string): boolean;
}

/**
 * Configurações para o serviço de criptografia
 */
export interface CryptoConfig {
  /** Número padrão de rounds para bcrypt */
  defaultRounds: number;
  /** Rounds mínimos permitidos */
  minRounds: number;
  /** Rounds máximos permitidos */
  maxRounds: number;
}

/**
 * Schema de validação para configuração de criptografia
 */
export const CryptoConfigSchema = z.object({
  defaultRounds: z.number().min(4).max(20).default(12),
  minRounds: z.number().min(4).max(15).default(10),
  maxRounds: z.number().min(10).max(20).default(15),
});

/**
 * Tipo inferido do schema de configuração
 */
export type CryptoConfigType = z.infer<typeof CryptoConfigSchema>;

/**
 * Constantes para validação de hash
 */
export const HASH_PATTERNS = {
  BCRYPT: /^\$2[aby]?\$\d{1,2}\$[./A-Za-z0-9]{53}$/,
  BCRYPT_ROUNDS: /^\$2[aby]?\$(\d{1,2})\$/,
} as const;

/**
 * Utilitários para trabalhar com hashes
 */
export class HashUtils {
  /**
   * Extrai o número de rounds de um hash bcrypt
   * @param hash - Hash bcrypt
   * @returns Número de rounds ou null se inválido
   */
  static extractRounds(hash: string): number | null {
    const match = hash.match(HASH_PATTERNS.BCRYPT_ROUNDS);
    return match ? parseInt(match[1], 10) : null;
  }

  /**
   * Verifica se um hash é do tipo bcrypt
   * @param hash - Hash a ser verificado
   */
  static isBcryptHash(hash: string): boolean {
    return HASH_PATTERNS.BCRYPT.test(hash);
  }

  /**
   * Valida se os rounds estão dentro do range aceitável
   * @param rounds - Número de rounds
   * @param config - Configuração de criptografia
   */
  static validateRounds(rounds: number, config: CryptoConfigType): boolean {
    return rounds >= config.minRounds && rounds <= config.maxRounds;
  }
}