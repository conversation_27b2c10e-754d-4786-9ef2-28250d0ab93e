import { z } from 'zod';

// Account Types
export type AccountType = 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'CASH' | 'ASSETS';

// Base Account Interface
export interface Account {
  id: string;
  name: string;
  type: AccountType;
  currency: string;
  balance: number;
  initialBalance: number;
  description?: string;
  isActive: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// Account Request Types
export interface CreateAccountRequest {
  name: string;
  type: AccountType;
  currency: string;
  initialBalance: number;
  description?: string;
  isActive?: boolean;
}

export interface UpdateAccountRequest extends Partial<CreateAccountRequest> {
  id: string;
}

// Account Balance Types
export interface AccountBalance {
  accountId: string;
  accountName: string;
  balance: number;
  currency: string;
  type: AccountType;
}

// Validation Schemas
export const CreateAccountSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  type: z.enum(['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'CASH', 'ASSETS']),
  currency: z.string().min(3, 'Moeda é obrigatória'),
  initialBalance: z.number(),
  description: z.string().optional(),
  isActive: z.boolean().optional().default(true),
});

export const UpdateAccountSchema = CreateAccountSchema.partial().extend({
  id: z.string().min(1, 'ID é obrigatório'),
});