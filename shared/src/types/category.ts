import { z } from 'zod';

// Category Types
export type CategoryType = 'INCOME' | 'EXPENSE';

// Base Category Interface
export interface Category {
  id: string;
  name: string;
  type: CategoryType;
  color?: string;
  description?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// Category Request Types
export interface CreateCategoryRequest {
  name: string;
  type: CategoryType;
  color?: string;
  description?: string;
  parentId?: string;
}

export interface UpdateCategoryRequest extends Partial<CreateCategoryRequest> {
  id: string;
}

// Category Stats
export interface CategoryStats {
  categoryId: string;
  categoryName: string;
  amount: number;
  percentage: number;
  color?: string;
}

// Validation Schemas
export const CreateCategorySchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  type: z.enum(['INCOME', 'EXPENSE']),
  color: z.string().optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
});

export const UpdateCategorySchema = CreateCategorySchema.partial().extend({
  id: z.string().min(1, 'ID é obrigatório'),
});