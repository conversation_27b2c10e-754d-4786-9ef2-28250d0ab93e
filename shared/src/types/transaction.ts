import { z } from 'zod';

// Transaction Types
export type TransactionType = 'INCOME' | 'EXPENSE' | 'TRANSFER';
export type TransactionStatus = 'PENDING' | 'COMPLETED' | 'CANCELLED' | 'FAILED';

// Base Transaction Interface
export interface Transaction {
  id: string;
  description: string;
  amount: number;
  type: TransactionType;
  transactionDate: string;
  notes?: string;
  accountId: string;
  account?: any;
  destinationAccountId?: string;
  destinationAccount?: any;
  categoryId?: string;
  category?: any;
  userId: string;
  status: TransactionStatus;
  createdAt: string;
  updatedAt: string;
}

// Transaction Request Types
export interface CreateTransactionRequest {
  description: string;
  amount: number;
  type: TransactionType;
  transactionDate: string;
  notes?: string;
  accountId: string;
  destinationAccountId?: string;
  categoryId?: string;
}

export interface UpdateTransactionRequest extends Partial<CreateTransactionRequest> {
  id: string;
}

// Transaction Filters
export interface TransactionFilters {
  startDate?: string;
  endDate?: string;
  accountId?: string;
  categoryId?: string;
  type?: TransactionType;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  page?: number;
  limit?: number;
}

// Future Transaction Types
export interface FutureTransaction extends Omit<Transaction, 'status'> {
  recurrenceRule?: string;
  endDate?: string;
  isProcessed: boolean;
}

// Recurring Transaction Types
export type RecurrenceFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';

export interface RecurringTransaction extends Omit<Transaction, 'status'> {
  frequency: RecurrenceFrequency;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  lastProcessedDate?: string;
  nextProcessDate: string;
}

// Validation Schemas
export const CreateTransactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  amount: z.number().positive('Valor deve ser positivo'),
  type: z.enum(['INCOME', 'EXPENSE', 'TRANSFER']),
  transactionDate: z.string(),
  notes: z.string().optional(),
  accountId: z.string().min(1, 'Conta é obrigatória'),
  destinationAccountId: z.string().optional(),
  categoryId: z.string().optional(),
});

export const UpdateTransactionSchema = CreateTransactionSchema.partial().extend({
  id: z.string().min(1, 'ID é obrigatório'),
});

export const TransactionFiltersSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  accountId: z.string().optional(),
  categoryId: z.string().optional(),
  type: z.enum(['INCOME', 'EXPENSE', 'TRANSFER']).optional(),
  minAmount: z.number().optional(),
  maxAmount: z.number().optional(),
  search: z.string().optional(),
  page: z.number().optional(),
  limit: z.number().optional(),
});