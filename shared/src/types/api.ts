// Pagination Types (ApiResponse and ApiError are in auth.ts)
export interface PaginatedResponse<T = any> {
  success: boolean;
  data: {
    items: T[];
    pagination: Pagination;
  };
  message?: string;
  errors?: string[];
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Date Range
export interface DateRange {
  startDate: string;
  endDate: string;
}

// Dashboard Types
export interface DashboardOverview {
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlyBalance: number;
  accountsCount: number;
  transactionsCount: number;
  categoriesCount: number;
}

export interface MonthlyStats {
  month: string;
  income: number;
  expenses: number;
  balance: number;
}

// Chart Data Types
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface TimeSeriesData {
  date: string;
  income: number;
  expenses: number;
  balance: number;
}

// Generic Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};