# =============================================================================
# CONFIGURAÇÃO DE AMBIENTE - PERSONAL FINANCE MANAGER
# =============================================================================
# Copie este arquivo para .env e configure as variáveis conforme necessário

# =============================================================================
# CONFIGURAÇÃO DO SERVIDOR
# =============================================================================
NODE_ENV=development
PORT=3001
HOST=localhost

# =============================================================================
# CONFIGURAÇÃO DO BANCO DE DADOS
# =============================================================================
# PostgreSQL Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/personal_finance_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=personal_finance_db
DB_USER=username
DB_PASSWORD=password

# Database Pool Configuration
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# =============================================================================
# CONFIGURAÇÃO DE AUTENTICAÇÃO E SEGURANÇA
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Bcrypt Configuration
BCRYPT_ROUNDS=12

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
SESSION_MAX_AGE=86400000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# =============================================================================
# CONFIGURAÇÃO DE EMAIL
# =============================================================================
# SMTP Configuration for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email Templates
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Personal Finance Manager

# =============================================================================
# CONFIGURAÇÃO DE UPLOAD E ARQUIVOS
# =============================================================================
# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DEST=uploads/

# =============================================================================
# CONFIGURAÇÃO DE LOGS
# =============================================================================
# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================================================
# CONFIGURAÇÃO DE CACHE E REDIS (OPCIONAL)
# =============================================================================
# Redis Configuration (if using Redis for caching)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# CONFIGURAÇÃO DE MONITORAMENTO E MÉTRICAS
# =============================================================================
# Application Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# CONFIGURAÇÃO DE DESENVOLVIMENTO
# =============================================================================
# Development Configuration
DEBUG=personal-finance:*
ENABLE_SWAGGER=true
ENABLE_CORS_DEBUG=false

# Hot Reload Configuration
WATCH_FILES=true
IGNORE_WATCH=node_modules,dist,logs

# =============================================================================
# CONFIGURAÇÃO DE PRODUÇÃO
# =============================================================================
# Production-specific settings (uncomment when deploying)
# NODE_ENV=production
# PORT=80
# JWT_SECRET=generate-a-strong-secret-key
# SESSION_SECRET=generate-a-strong-session-key
# DATABASE_URL=your-production-database-url
# CORS_ORIGIN=https://your-domain.com

# SSL Configuration (for production)
# SSL_KEY_PATH=/path/to/ssl/key.pem
# SSL_CERT_PATH=/path/to/ssl/cert.pem

# =============================================================================
# CONFIGURAÇÃO DE TESTES
# =============================================================================
# Test Database Configuration
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/personal_finance_test_db
TEST_JWT_SECRET=test-jwt-secret
TEST_SESSION_SECRET=test-session-secret

# =============================================================================
# CONFIGURAÇÃO DE INTEGRAÇÃO COM APIS EXTERNAS
# =============================================================================
# External API Configuration (if needed)
# BANK_API_KEY=your-bank-api-key
# CURRENCY_API_KEY=your-currency-api-key
# NOTIFICATION_API_KEY=your-notification-service-key

# =============================================================================
# CONFIGURAÇÃO DE BACKUP
# =============================================================================
# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups/