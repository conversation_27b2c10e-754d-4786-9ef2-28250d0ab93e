const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugRecurringTransactions() {
  try {
    console.log('🔍 Verificando transações recorrentes no banco...\n');

    // Buscar todas as transações recorrentes
    const recurringTransactions = await prisma.recurringTransaction.findMany({
      include: {
        account: true,
        category: true
      }
    });

    console.log(`📊 Total de transações recorrentes: ${recurringTransactions.length}\n`);

    if (recurringTransactions.length === 0) {
      console.log('❌ Nenhuma transação recorrente encontrada no banco.');
      return;
    }

    // Mostrar detalhes de cada transação
    recurringTransactions.forEach((transaction, index) => {
      console.log(`--- Transação ${index + 1} ---`);
      console.log(`ID: ${transaction.id}`);
      console.log(`Descrição: ${transaction.description}`);
      console.log(`Valor Fixo: ${transaction.fixedAmount}`);
      console.log(`Tipo do Valor Fixo: ${typeof transaction.fixedAmount}`);
      console.log(`Frequência: ${transaction.frequency}`);
      console.log(`Tipo: ${transaction.type}`);
      console.log(`Conta: ${transaction.account.name}`);
      console.log(`Categoria: ${transaction.category?.name || 'Nenhuma'}`);
      console.log(`Ativa: ${transaction.isActive}`);
      console.log(`Data de Início: ${transaction.startDate}`);
      console.log(`Data de Fim: ${transaction.endDate || 'Não definida'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Erro ao verificar transações recorrentes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugRecurringTransactions();
