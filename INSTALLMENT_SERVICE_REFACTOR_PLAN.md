# PLANO DE REFATORAÇÃO - INSTALLMENT SERVICE

## 📋 RESUMO EXECUTIVO

**Objetivo:** Remover o `InstallmentService` desatualizado e integrar suas funcionalidades no `TransactionService`

**Justificativa:** O `InstallmentService` usa um modelo antigo (múltiplas `Transaction`) enquanto o `TransactionService` já implementa corretamente o modelo atual (1 `Transaction` + N `Installment`)

**Status:** 🔄 Em Execução

---

## 🔍 ANÁLISE DA SITUAÇÃO ATUAL

### Problemas Identificados:
1. **`InstallmentService` completamente desatualizado** - usa modelo antigo (múltiplas `Transaction`)
2. **`TransactionService` já funciona corretamente** - usa modelo atual (1 `Transaction` + N `Installment`)
3. **Duplicação de lógica** - ambos services tentam fazer a mesma coisa
4. **Inconsistência** - diferentes abordagens para o mesmo problema
5. **Testes quebrados** - `InstallmentService` testa funcionalidades que não existem mais

### Arquivos que usam `InstallmentService`:
- `backend/src/scripts/acceptance-tests.ts` (testes de aceitação)
- `backend/src/scripts/seed-advanced-features.ts` (seed de dados)
- `backend/src/tests/installment-transactions.test.ts` (testes unitários)

---

## 🎯 ESTRATÉGIA DE REFATORAÇÃO

**Abordagem:** Remover `InstallmentService` e migrar funcionalidades para `TransactionService`

**Justificativa:**
- `TransactionService` já implementa corretamente o modelo atual
- Evita duplicação de código e lógica
- Mantém consistência arquitetural
- `TransactionService` já é usado pelos controllers

---

## 📝 PLANO DE EXECUÇÃO

### FASE 1: ✅ Análise e Mapeamento de Funcionalidades

**Funcionalidades do `InstallmentService` que precisam ser migradas:**

1. **`createInstallmentTransaction()`** → ✅ Já existe no `TransactionService.create()`
2. **`getInstallmentSummary()`** → 🔄 Criar nova funcionalidade
3. **`updateInstallmentTransaction()`** → ✅ Já existe no `TransactionService.update()`
4. **`cancelRemainingInstallments()`** → 🔄 Criar nova funcionalidade
5. **`getInstallmentsByParent()`** → ✅ Já existe no `TransactionService.findById()`

### FASE 2: 🔄 Extensão do TransactionService

**Novas funcionalidades a adicionar:**

```typescript
// Adicionar ao TransactionService
export class TransactionService {
  // ... métodos existentes ...

  /**
   * Get installment summary for a transaction
   */
  async getInstallmentSummary(transactionId: string): Promise<InstallmentSummary>

  /**
   * Cancel future installments
   */
  async cancelFutureInstallments(transactionId: string): Promise<InstallmentCancellationResult>

  /**
   * Mark specific installment as paid/unpaid
   */
  async markInstallmentAsPaid(installmentId: string, paidAt?: Date): Promise<void>

  /**
   * Get installments by transaction with summary
   */
  async getInstallmentsByTransaction(transactionId: string): Promise<InstallmentsByTransactionResult>
}
```

### FASE 3: ⏳ Atualização dos Arquivos Dependentes

**1. `acceptance-tests.ts`:**
- Remover import do `InstallmentService`
- Substituir chamadas por `TransactionService.create()` com array de `installments`
- Atualizar testes de resumo para usar nova funcionalidade

**2. `seed-advanced-features.ts`:**
- Remover import do `InstallmentService`
- Substituir `createInstallmentTransaction()` por `TransactionService.create()`
- Usar array de `installments` no payload

**3. `installment-transactions.test.ts`:**
- Renomear para `transaction-installments.test.ts`
- Testar funcionalidades de parcelas através do `TransactionService`
- Atualizar mocks para usar modelo atual (`Installment` table)

### FASE 4: ⏳ Interfaces e Tipos

**Interfaces a manter/migrar:**
```typescript
// Manter no transaction.schemas.ts ou criar installment.types.ts
export interface InstallmentSummary {
  transactionId: string;
  totalInstallments: number;
  paidInstallments: number;
  pendingInstallments: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  progressPercentage: number;
  nextInstallmentDate?: Date;
  nextInstallmentAmount?: number;
}

export interface InstallmentCancellationResult {
  cancelledInstallments: number;
  message: string;
}

export interface InstallmentsByTransactionResult {
  transaction: any;
  installments: any[];
  summary: InstallmentSummary;
}
```

### FASE 5: ⏳ Remoção do InstallmentService

1. **Deletar arquivo:** `backend/src/services/installment.service.ts`
2. **Verificar imports:** Garantir que nenhum arquivo ainda importa o service
3. **Limpar testes:** Remover testes obsoletos

---

## 🔧 IMPLEMENTAÇÃO DETALHADA

### 1. Extensões para TransactionService

```typescript
// Adicionar ao TransactionService
async getInstallmentSummary(transactionId: string): Promise<InstallmentSummary> {
  const transaction = await prisma.transaction.findUnique({
    where: { id: transactionId, deletedAt: null },
    include: {
      installments: {
        orderBy: { installmentNumber: 'asc' }
      }
    }
  });

  if (!transaction) {
    throw new Error('Transação não encontrada');
  }

  const installments = transaction.installments;
  const paidInstallments = installments.filter(i => i.isPaid);
  const pendingInstallments = installments.filter(i => !i.isPaid);
  
  const totalAmount = installments.reduce((sum, i) => sum + Number(i.amount), 0);
  const paidAmount = paidInstallments.reduce((sum, i) => sum + Number(i.amount), 0);
  const remainingAmount = totalAmount - paidAmount;
  
  const progressPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
  
  const nextInstallment = pendingInstallments
    .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())[0];

  return {
    transactionId: transaction.id,
    totalInstallments: installments.length,
    paidInstallments: paidInstallments.length,
    pendingInstallments: pendingInstallments.length,
    totalAmount,
    paidAmount,
    remainingAmount,
    progressPercentage: Math.round(progressPercentage * 100) / 100,
    nextInstallmentDate: nextInstallment?.dueDate,
    nextInstallmentAmount: nextInstallment ? Number(nextInstallment.amount) : undefined
  };
}

async cancelFutureInstallments(transactionId: string): Promise<InstallmentCancellationResult> {
  const today = new Date();
  
  const result = await prisma.installment.deleteMany({
    where: {
      transactionId,
      dueDate: { gt: today },
      isPaid: false
    }
  });

  return {
    cancelledInstallments: result.count,
    message: `Canceladas ${result.count} parcelas futuras`
  };
}

async markInstallmentAsPaid(installmentId: string, paidAt?: Date): Promise<void> {
  await prisma.installment.update({
    where: { id: installmentId },
    data: {
      isPaid: true,
      paidAt: paidAt || new Date()
    }
  });
}
```

---

## ✅ CHECKLIST DE EXECUÇÃO

### Preparação
- [x] Backup do código atual
- [x] Análise completa de dependências
- [x] Criação de plano detalhado

### Implementação
- [ ] Adicionar novas funcionalidades ao `TransactionService`
- [ ] Atualizar interfaces e tipos
- [ ] Migrar `acceptance-tests.ts`
- [ ] Migrar `seed-advanced-features.ts`
- [ ] Criar novos testes para funcionalidades migradas

### Limpeza
- [ ] Remover `InstallmentService`
- [ ] Remover testes obsoletos
- [ ] Verificar imports quebrados
- [ ] Atualizar documentação

### Validação
- [ ] Executar todos os testes
- [ ] Executar scripts de seed
- [ ] Executar testes de aceitação
- [ ] Verificar compilação TypeScript
- [ ] Testar endpoints da API

---

## 🎯 RESULTADO ESPERADO

**Após a refatoração:**
1. ✅ **Código unificado** - Toda lógica de parcelas no `TransactionService`
2. ✅ **Modelo consistente** - Uso correto da tabela `Installment`
3. ✅ **Testes funcionando** - Todos os testes passando
4. ✅ **API funcionando** - Endpoints de transação com parcelas operacionais
5. ✅ **Scripts funcionando** - Seed e testes de aceitação operacionais
6. ✅ **Zero duplicação** - Remoção de código duplicado
7. ✅ **Arquitetura limpa** - Responsabilidades bem definidas

**Benefícios:**
- 🔧 **Manutenibilidade** - Código mais fácil de manter
- 🚀 **Performance** - Menos overhead de services desnecessários
- 🧪 **Testabilidade** - Testes mais focados e consistentes
- 📚 **Documentação** - Arquitetura mais clara