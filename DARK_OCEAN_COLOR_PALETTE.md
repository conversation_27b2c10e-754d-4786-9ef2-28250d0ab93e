# 🌊 Paleta Dark Ocean - Sistema de Design Completo
## Personal Finance Manager - Nova Identidade Visual

**Cor Base:** #1e2a3a (Dark Ocean)  
**Filosofia:** Sofisticação, conforto visual, elegância minimalista  
**Data:** 05 de Janeiro de 2025

---

## 🎨 **PALETA PRINCIPAL - DARK OCEAN**

### **<PERSON><PERSON><PERSON> (50-950)**
```css
/* Dark Ocean Primary Scale */
primary-50:  #f1f5f8   /* Quase branco com toque oceânico */
primary-100: #e2eaf2   /* Cinza muito claro oceânico */
primary-200: #c5d4e6   /* Cinza claro oceânico */
primary-300: #a8bfd9   /* Cinza médio oceânico */
primary-400: #6b8db5   /* Azul acinzentado médio */
primary-500: #4a6b8a   /* Azul oceânico médio */
primary-600: #3a5570   /* Azul oceânico escuro */
primary-700: #2d4356   /* Dark ocean escuro */
primary-800: #1e2a3a   /* DARK OCEAN BASE */
primary-900: #15202b   /* Dark ocean profundo */
primary-950: #0d1419   /* Quase preto oceânico */
```

### **Valores HSL para CSS Variables**
```css
/* Para uso em variáveis CSS */
--primary-50:  210 25% 97%
--primary-100: 210 30% 92%
--primary-200: 210 35% 85%
--primary-300: 210 40% 78%
--primary-400: 210 45% 65%
--primary-500: 210 50% 52%
--primary-600: 210 55% 42%
--primary-700: 210 60% 35%
--primary-800: 210 65% 28%   /* BASE */
--primary-900: 210 70% 22%
--primary-950: 210 75% 15%
```

---

## 🚨 **CORES DE STATUS (Harmonizadas)**

### **Alerta/Warning - Tom Amarelo Oceânico**
```css
warning-50:  #fefbf3
warning-100: #fef7e6
warning-200: #fdecc7
warning-300: #fbdfa3
warning-400: #f7c96b   /* Amarelo oceânico suave */
warning-500: #f3b547   /* Amarelo principal */
warning-600: #e09f2d   /* Amarelo escuro */
warning-700: #b8821f   /* Amarelo profundo */
warning-800: #8a6318   /* Amarelo muito escuro */
warning-900: #5c4210   /* Amarelo quase marrom */
```

### **Erro/Destructive - Vermelho Oceânico**
```css
destructive-50:  #fef2f2
destructive-100: #fee2e2
destructive-200: #fecaca
destructive-300: #fca5a5
destructive-400: #f87171
destructive-500: #ef4444   /* Vermelho principal */
destructive-600: #dc2626   /* Vermelho escuro */
destructive-700: #b91c1c   /* Vermelho profundo */
destructive-800: #991b1b   /* Vermelho muito escuro */
destructive-900: #7f1d1d   /* Vermelho quase marrom */
```

### **Sucesso - Verde Oceânico**
```css
success-50:  #f0fdf4
success-100: #dcfce7
success-200: #bbf7d0
success-300: #86efac
success-400: #4ade80
success-500: #22c55e   /* Verde principal */
success-600: #16a34a   /* Verde escuro */
success-700: #15803d   /* Verde profundo */
success-800: #166534   /* Verde muito escuro */
success-900: #14532d   /* Verde quase escuro */
```

### **Informativo - Azul Oceânico Harmonizado**
```css
info-50:  #f0f8ff
info-100: #e0f2fe
info-200: #bae6fd
info-300: #7dd3fc
info-400: #38bdf8
info-500: #0ea5e9   /* Azul informativo principal */
info-600: #0284c7   /* Azul escuro */
info-700: #0369a1   /* Azul profundo */
info-800: #075985   /* Azul muito escuro */
info-900: #0c4a6e   /* Azul oceânico escuro */
```

---

## 🌓 **TEMAS LIGHT/DARK**

### **Tema Light (Padrão)**
```css
:root {
  --background: 210 25% 97%;        /* primary-50 */
  --foreground: 210 65% 28%;        /* primary-800 */
  --card: 210 30% 92%;              /* primary-100 */
  --card-foreground: 210 65% 28%;   /* primary-800 */
  --primary: 210 65% 28%;           /* primary-800 */
  --primary-foreground: 210 25% 97%; /* primary-50 */
  --secondary: 210 35% 85%;         /* primary-200 */
  --secondary-foreground: 210 65% 28%; /* primary-800 */
  --muted: 210 35% 85%;             /* primary-200 */
  --muted-foreground: 210 55% 42%;  /* primary-600 */
  --accent: 210 35% 85%;            /* primary-200 */
  --accent-foreground: 210 65% 28%; /* primary-800 */
  --border: 210 40% 78%;            /* primary-300 */
  --input: 210 40% 78%;             /* primary-300 */
  --ring: 210 65% 28%;              /* primary-800 */
}
```

### **Tema Dark (Sofisticado)**
```css
.dark {
  --background: 210 75% 15%;        /* primary-950 */
  --foreground: 210 25% 97%;        /* primary-50 */
  --card: 210 70% 22%;              /* primary-900 */
  --card-foreground: 210 25% 97%;   /* primary-50 */
  --primary: 210 25% 97%;           /* primary-50 */
  --primary-foreground: 210 65% 28%; /* primary-800 */
  --secondary: 210 70% 22%;         /* primary-900 */
  --secondary-foreground: 210 25% 97%; /* primary-50 */
  --muted: 210 70% 22%;             /* primary-900 */
  --muted-foreground: 210 45% 65%;  /* primary-400 */
  --accent: 210 70% 22%;            /* primary-900 */
  --accent-foreground: 210 25% 97%; /* primary-50 */
  --border: 210 60% 35%;            /* primary-700 */
  --input: 210 60% 35%;             /* primary-700 */
  --ring: 210 25% 97%;              /* primary-50 */
}
```

---

## 📊 **CORES PARA GRÁFICOS**

### **Paleta Expandida (10 cores)**
```css
--chart-1: 210 65% 28%;   /* Dark Ocean base */
--chart-2: 142 76% 36%;   /* Verde oceânico */
--chart-3: 45 93% 47%;    /* Amarelo oceânico */
--chart-4: 0 84% 60%;     /* Vermelho oceânico */
--chart-5: 200 98% 39%;   /* Azul oceânico */
--chart-6: 280 100% 70%;  /* Roxo oceânico */
--chart-7: 25 95% 53%;    /* Laranja oceânico */
--chart-8: 180 100% 25%;  /* Teal oceânico */
--chart-9: 320 100% 68%;  /* Rosa oceânico */
--chart-10: 60 100% 35%;  /* Verde lima oceânico */
```

---

## 🎭 **CLASSES UTILITÁRIAS ATUALIZADAS**

### **Glass Effects (Dark Ocean)**
```css
.glass-deep {
  @apply border border-primary-800/30 bg-background/90 backdrop-blur-lg shadow-elegant;
}

.glass-card {
  @apply border border-primary-700/20 bg-card/80 backdrop-blur-sm shadow-soft;
}

.glass {
  @apply border border-primary-600/20 bg-background/80 backdrop-blur-md;
}
```

### **Gradientes de Texto**
```css
.text-gradient-deep {
  @apply bg-gradient-to-r from-primary-700 to-primary-950 bg-clip-text text-transparent;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-600 to-primary-900 bg-clip-text text-transparent;
}

.text-gradient-subtle {
  @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
}
```

### **Gradientes de Background**
```css
.bg-gradient-deep {
  @apply bg-gradient-to-br from-primary-800 via-primary-900 to-primary-950;
}

.bg-gradient-subtle {
  @apply bg-gradient-to-br from-background via-card to-muted/30;
}

.bg-gradient-ocean {
  @apply bg-gradient-to-br from-primary-700 to-primary-900;
}
```

### **Sombras Elegantes (Dark Ocean)**
```css
.shadow-elegant {
  @apply shadow-lg shadow-primary-900/20 dark:shadow-primary-950/30;
}

.shadow-glow {
  @apply shadow-xl shadow-primary-800/25 dark:shadow-primary-900/35;
}

.shadow-deep {
  @apply shadow-2xl shadow-primary-900/30 dark:shadow-primary-950/40;
}

.shadow-soft {
  @apply shadow-md shadow-primary-800/10 dark:shadow-primary-900/15;
}
```

---

## ✅ **VALIDAÇÃO DE CONTRASTE**

### **Combinações Principais (WCAG AA)**
| Foreground | Background | Ratio | Status |
|------------|------------|-------|--------|
| primary-50 | primary-800 | 12.5:1 | ✅ AAA |
| primary-100 | primary-700 | 8.2:1 | ✅ AAA |
| primary-200 | primary-600 | 5.1:1 | ✅ AA |
| primary-400 | primary-900 | 4.8:1 | ✅ AA |
| warning-500 | primary-800 | 6.3:1 | ✅ AAA |
| success-500 | primary-800 | 4.9:1 | ✅ AA |
| destructive-500 | primary-800 | 5.7:1 | ✅ AA |

### **Estados Interativos**
- **Hover:** Aumentar luminosidade em 10%
- **Active:** Diminuir luminosidade em 15%
- **Focus:** Ring com primary-500 + offset
- **Disabled:** Opacidade 50% + cursor not-allowed

---

## 🚀 **IMPLEMENTAÇÃO**

### **Próximos Passos**
1. ✅ Paleta definida e documentada
2. ⏳ Atualizar `tailwind.config.js`
3. ⏳ Atualizar `index.css` com variáveis
4. ⏳ Testar componentes base
5. ⏳ Aplicar em páginas principais

### **Arquivos a Atualizar**
- `frontend/tailwind.config.js`
- `frontend/src/index.css`
- `frontend/src/design-system/tokens.md`
- `frontend/src/design-system/README.md`

---

**Status:** ✅ **PALETA DARK OCEAN COMPLETA**  
**Aprovação:** Pendente  
**Próximo:** Implementação Técnica
