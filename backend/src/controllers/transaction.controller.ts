import { Request, Response } from 'express';
import { z } from 'zod';
import { Prisma, Transaction, Installment, TransactionTag, TransactionMember } from '@prisma/client';
import { TransactionService } from '../services/transaction.service';
import {
  CreateTransactionSchema,
  UpdateTransactionSchema,
  TransactionFiltersSchema
} from '../schemas/transaction.schemas';

// Define a type for the populated transaction
type PopulatedTransaction = Transaction & {
  installments: Installment[];
  account: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
  category: {
    id: string;
    name: string;
    color: string | null;
  } | null;
  destinationAccount: {
    id: string;
    name: string;
    type: string;
    currency: string;
  } | null;
  tags: (TransactionTag & { tag: { id: string; name: string; color: string } })[];
  members: (TransactionMember & { familyMember: { id: string; name: string; color: string } })[];
};

export class TransactionController {
  private transactionService = new TransactionService();

  /**
   * @swagger
   * /transactions:
   *   post:
   *     summary: Criar nova transação
   *     description: Cria uma nova transação com parcelas automáticas baseadas nos parâmetros fornecidos
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateTransactionRequest'
   *           examples:
   *             despesa_parcelada:
   *               summary: Despesa parcelada
   *               value:
   *                 description: "Compra no cartão de crédito"
   *                 amount: 1200.00
   *                 transactionDate: "2024-01-15"
   *                 type: "EXPENSE"
   *                 accountId: "550e8400-e29b-41d4-a716-************"
   *                 categoryId: "550e8400-e29b-41d4-a716-************"
   *                 totalInstallments: 12
   *                 familyMemberIds: ["550e8400-e29b-41d4-a716-************"]
   *                 tagIds: ["550e8400-e29b-41d4-a716-************"]
   *             receita:
   *               summary: Receita
   *               value:
   *                 description: "Salário mensal"
   *                 amount: 5000.00
   *                 transactionDate: "2024-01-01"
   *                 type: "INCOME"
   *                 accountId: "550e8400-e29b-41d4-a716-************"
   *                 categoryId: "550e8400-e29b-41d4-a716-************"
   *             transferencia:
   *               summary: Transferência entre contas
   *               value:
   *                 description: "Transferência para poupança"
   *                 amount: 1000.00
   *                 transactionDate: "2024-01-15"
   *                 type: "TRANSFER"
   *                 accountId: "550e8400-e29b-41d4-a716-************"
   *                 destinationAccountId: "550e8400-e29b-41d4-a716-************"
   *                 sourceCurrency: "BRL"
   *                 destinationCurrency: "USD"
   *     responses:
   *       201:
   *         description: Transação criada com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Transaction'
   *                     message:
   *                       type: string
   *                       example: "Transação criada com sucesso"
   *       400:
   *         description: Dados inválidos
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             examples:
   *               validation_error:
   *                 summary: Erro de validação
   *                 value:
   *                   success: false
   *                   error:
   *                     message: "Dados inválidos"
   *                     code: "VALIDATION_ERROR"
   *                     details:
   *                       - field: "amount"
   *                         message: "Valor deve ser maior que 0"
   *                       - field: "accountId"
   *                         message: "ID da conta é obrigatório"
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const result = CreateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Create transaction with installments
      const transaction = await this.transactionService.create(result.data as any);

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'Transação criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * @swagger
   * /transactions/{id}:
   *   get:
   *     summary: Buscar transação por ID
   *     description: Retorna uma transação específica com todas as parcelas e dados relacionados
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: ID único da transação
   *         example: "550e8400-e29b-41d4-a716-************"
   *     responses:
   *       200:
   *         description: Transação encontrada com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Transaction'
   *                     message:
   *                       type: string
   *                       example: "Transação obtida com sucesso"
   *       404:
   *         description: Transação não encontrada
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               error:
   *                 message: "Transação não encontrada"
   *                 code: "TRANSACTION_NOT_FOUND"
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Get transaction with installments
      const transaction = await this.transactionService.findById(id);

      if (!transaction) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Transação não encontrada',
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * @swagger
   * /transactions/{id}:
   *   put:
   *     summary: Atualizar transação
   *     description: Atualiza uma transação existente e recria as parcelas automaticamente
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: ID único da transação
   *         example: "550e8400-e29b-41d4-a716-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateTransactionRequest'
   *           examples:
   *             update_description:
   *               summary: Atualizar descrição
   *               value:
   *                 description: "Nova descrição da transação"
   *             update_amount:
   *               summary: Atualizar valor
   *               value:
   *                 amount: 250.75
   *             update_multiple:
   *               summary: Atualizar múltiplos campos
   *               value:
   *                 description: "Compra atualizada"
   *                 amount: 180.50
   *                 transactionDate: "2024-01-20"
   *                 categoryId: "550e8400-e29b-41d4-a716-************"
   *     responses:
   *       200:
   *         description: Transação atualizada com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Transaction'
   *                     message:
   *                       type: string
   *                       example: "Transação atualizada com sucesso"
   *       400:
   *         description: Dados inválidos ou nenhum dado fornecido
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             examples:
   *               no_data:
   *                 summary: Nenhum dado fornecido
   *                 value:
   *                   success: false
   *                   error:
   *                     message: "Nenhum dado fornecido para atualização"
   *                     code: "NO_UPDATE_DATA"
   *               validation_error:
   *                 summary: Erro de validação
   *                 value:
   *                   success: false
   *                   error:
   *                     message: "Dados inválidos"
   *                     code: "VALIDATION_ERROR"
   *                     details:
   *                       - field: "amount"
   *                         message: "Valor deve ser maior que 0"
   *       404:
   *         description: Transação não encontrada
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = UpdateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Check if there's data to update
      if (Object.keys(result.data).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update transaction with installments
      const transaction = await this.transactionService.update(id, result.data as any);

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * @swagger
   * /transactions/{id}:
   *   delete:
   *     summary: Excluir transação
   *     description: Exclui uma transação e todas as suas parcelas permanentemente
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: ID único da transação
   *         example: "550e8400-e29b-41d4-a716-************"
   *     responses:
   *       200:
   *         description: Transação excluída com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "Transação deletada com sucesso"
   *       404:
   *         description: Transação não encontrada
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Delete transaction and installments
      await this.transactionService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Transação deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * @swagger
   * /transactions/{id}/installments/{installmentNumber}:
   *   patch:
   *     summary: Atualizar status de parcela
   *     description: Marca uma parcela específica como paga ou não paga
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: ID único da transação
   *         example: "550e8400-e29b-41d4-a716-************"
   *       - in: path
   *         name: installmentNumber
   *         required: true
   *         schema:
   *           type: integer
   *           minimum: 1
   *         description: Número da parcela
   *         example: 3
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               isPaid:
   *                 type: boolean
   *                 description: Status de pagamento da parcela
   *                 example: true
   *               paidAt:
   *                 type: string
   *                 format: date-time
   *                 description: Data e hora do pagamento (opcional, usado quando isPaid=true)
   *                 example: "2024-01-15T10:30:00Z"
   *             required: [isPaid]
   *           examples:
   *             marcar_como_paga:
   *               summary: Marcar parcela como paga
   *               value:
   *                 isPaid: true
   *                 paidAt: "2024-01-15T10:30:00Z"
   *             marcar_como_nao_paga:
   *               summary: Marcar parcela como não paga
   *               value:
   *                 isPaid: false
   *     responses:
   *       200:
   *         description: Status da parcela atualizado com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Transaction'
   *                     message:
   *                       type: string
   *                       example: "Status da parcela atualizado com sucesso"
   *       400:
   *         description: Dados inválidos
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             examples:
   *               invalid_installment:
   *                 summary: Número de parcela inválido
   *                 value:
   *                   success: false
   *                   error:
   *                     message: "Número da parcela inválido"
   *                     code: "INVALID_INSTALLMENT_NUMBER"
   *               invalid_status:
   *                 summary: Status de pagamento inválido
   *                 value:
   *                   success: false
   *                   error:
   *                     message: "Status de pagamento deve ser verdadeiro ou falso"
   *                     code: "INVALID_PAYMENT_STATUS"
   *       404:
   *         description: Transação ou parcela não encontrada
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async updateInstallmentStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id, installmentNumber } = req.params;
      const { isPaid, paidAt } = req.body;

      // Validate installment number
      const installmentNum = parseInt(installmentNumber);
      if (isNaN(installmentNum) || installmentNum < 1) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Número da parcela inválido',
            code: 'INVALID_INSTALLMENT_NUMBER'
          }
        });
        return;
      }

      // Validate isPaid
      if (typeof isPaid !== 'boolean') {
        res.status(400).json({
          success: false,
          error: {
            message: 'Status de pagamento deve ser verdadeiro ou falso',
            code: 'INVALID_PAYMENT_STATUS'
          }
        });
        return;
      }

      // Update installment status
      const transaction = await this.transactionService.updateInstallmentStatus(
        id,
        installmentNum,
        isPaid,
        paidAt
      );

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Status da parcela atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * @swagger
   * /transactions:
   *   get:
   *     summary: Listar transações com filtros
   *     description: Retorna uma lista paginada de transações com filtros opcionais e resumo financeiro
   *     tags: [Transações]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Número da página
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 100
   *         description: Número de itens por página
   *       - in: query
   *         name: description
   *         schema:
   *           type: string
   *         description: Filtrar por descrição (busca parcial, case-insensitive)
   *         example: "supermercado"
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [INCOME, EXPENSE, TRANSFER]
   *         description: Filtrar por tipo de transação
   *       - in: query
   *         name: accountId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filtrar por conta (origem ou destino)
   *       - in: query
   *         name: categoryId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filtrar por categoria
   *       - in: query
   *         name: startDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Data inicial do filtro (considera datas de vencimento das parcelas)
   *         example: "2024-01-01"
   *       - in: query
   *         name: endDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Data final do filtro (considera datas de vencimento das parcelas)
   *         example: "2024-12-31"
   *       - in: query
   *         name: minAmount
   *         schema:
   *           type: number
   *           minimum: 0
   *         description: Valor mínimo das parcelas
   *       - in: query
   *         name: maxAmount
   *         schema:
   *           type: number
   *           minimum: 0
   *         description: Valor máximo das parcelas
   *       - in: query
   *         name: includeRecurring
   *         schema:
   *           type: string
   *           enum: ["true", "false"]
   *           default: "false"
   *         description: Incluir transações recorrentes pendentes
   *     responses:
   *       200:
   *         description: Lista de transações obtida com sucesso
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         data:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/Transaction'
   *                         pagination:
   *                           type: object
   *                           properties:
   *                             page:
   *                               type: integer
   *                               example: 1
   *                             limit:
   *                               type: integer
   *                               example: 100
   *                             total:
   *                               type: integer
   *                               example: 250
   *                             totalPages:
   *                               type: integer
   *                               example: 3
   *                         summary:
   *                           type: object
   *                           properties:
   *                             totalIncome:
   *                               type: number
   *                               format: decimal
   *                               description: Total de receitas pagas no período
   *                               example: 15000.00
   *                             totalExpenses:
   *                               type: number
   *                               format: decimal
   *                               description: Total de despesas pagas no período
   *                               example: 8500.50
   *                             totalTransfers:
   *                               type: number
   *                               format: decimal
   *                               description: Total de transferências no período
   *                               example: 2000.00
   *                             netAmount:
   *                               type: number
   *                               format: decimal
   *                               description: Saldo líquido (receitas - despesas)
   *                               example: 6499.50
   *                     message:
   *                       type: string
   *                       example: "Transações obtidas com sucesso"
   *       400:
   *         description: Parâmetros de consulta inválidos
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               error:
   *                 message: "Parâmetros de consulta inválidos"
   *                 code: "VALIDATION_ERROR"
   *                 details:
   *                   - field: "startDate"
   *                     message: "Data deve estar no formato YYYY-MM-DD"
   *       401:
   *         description: Token de autenticação inválido ou ausente
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *       500:
   *         description: Erro interno do servidor
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const result = TransactionFiltersSchema.safeParse(req.query);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Parâmetros de consulta inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Get filtered transactions with installments
      const { transactions, summary } = await this.getAllTransactionsWithFilters(result.data, req.user!.id);

      // Return in expected format
      res.status(200).json({
        success: true,
        data: {
          data: transactions,
          pagination: {
            page: result.data.page || 1,
            limit: result.data.limit || 100,
            total: transactions.length,
            totalPages: Math.ceil(transactions.length / (result.data.limit || 100))
          },
          summary
        },
        message: 'Transações obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all transactions with filters considering installment dates
   */
  private async getAllTransactionsWithFilters(filters: any, userId: string) {
    // Import prisma here to avoid circular dependency
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      // Build where clause for transactions
      const transactionWhere: any = { deletedAt: null };

      // Apply basic filters
      if (filters.description) {
        transactionWhere.description = {
          contains: filters.description,
          mode: 'insensitive'
        };
      }

      if (filters.type) {
        transactionWhere.type = filters.type;
      }

      if (filters.accountId) {
        transactionWhere.OR = [
          { accountId: filters.accountId },
          { destinationAccountId: filters.accountId }
        ];
      }

      if (filters.categoryId) {
        transactionWhere.categoryId = filters.categoryId;
      }

      // For date filters, we need to include transactions that have installments in the date range
      if (filters.startDate || filters.endDate) {
        transactionWhere.installments = {
          some: {}
        };

        if (filters.startDate) {
          transactionWhere.installments.some.dueDate = {
            gte: new Date(filters.startDate)
          };
        }

        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          endDate.setHours(23, 59, 59, 999); // End of day

          if (transactionWhere.installments.some.dueDate) {
            transactionWhere.installments.some.dueDate.lte = endDate;
          } else {
            transactionWhere.installments.some.dueDate = { lte: endDate };
          }
        }
      }

      // Get transactions with all related data
      const transactions = await prisma.transaction.findMany({
        where: transactionWhere,
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          account: true,
          category: true,
          destinationAccount: true,
          tags: {
            include: { tag: true }
          },
          members: {
            include: { familyMember: true }
          }
        },
        orderBy: { transactionDate: 'desc' }
      });

      // Calculate summary based on installments in the filtered period
      const summary = {
        totalIncome: 0,
        totalExpenses: 0,
        totalTransfers: 0,
        netAmount: 0
      };

      // Format transactions and calculate summary
      const formattedTransactions = transactions.map((transaction: PopulatedTransaction) => {
        // Filter installments by date range if specified
        let relevantInstallments = transaction.installments;

        if (filters.startDate || filters.endDate) {
          relevantInstallments = transaction.installments.filter((installment: Installment) => {
            const dueDate = new Date(installment.dueDate);

            if (filters.startDate && dueDate < new Date(filters.startDate)) {
              return false;
            }

            if (filters.endDate) {
              const endDate = new Date(filters.endDate);
              endDate.setHours(23, 59, 59, 999);
              if (dueDate > endDate) {
                return false;
              }
            }

            return true;
          });
        }

        // Apply amount filters to relevant installments
        if (filters.minAmount || filters.maxAmount) {
          relevantInstallments = relevantInstallments.filter((installment: Installment) => {
            const amount = Number(installment.amount);

            if (filters.minAmount && amount < filters.minAmount) {
              return false;
            }

            if (filters.maxAmount && amount > filters.maxAmount) {
              return false;
            }

            return true;
          });
        }

        // Calculate summary for relevant installments (only paid ones)
        relevantInstallments.forEach((installment: Installment) => {
          // Only count paid installments in the summary
          if (installment.isPaid) {
            const amount = Number(installment.amount);
            switch (transaction.type) {
              case 'INCOME':
                summary.totalIncome += amount;
                break;
              case 'EXPENSE':
                summary.totalExpenses += amount;
                break;
              case 'TRANSFER':
                summary.totalTransfers += amount;
                break;
            }
          }
        });

        // Format transaction (same as before)
        return {
          id: transaction.id,
          description: transaction.description,
          totalAmount: Number(transaction.totalAmount),
          totalInstallments: transaction.totalInstallments,
          transactionDate: transaction.transactionDate.toISOString().split('T')[0],
          type: transaction.type,
          accountId: transaction.accountId,
          categoryId: transaction.categoryId,
          destinationAccountId: transaction.destinationAccountId,
          exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
          isFuture: transaction.isFuture,
          sourceCurrency: transaction.sourceCurrency,
          destinationCurrency: transaction.destinationCurrency,
          sourceAmount: transaction.sourceAmount ? Number(transaction.sourceAmount) : undefined,
          destinationAmount: transaction.destinationAmount ? Number(transaction.destinationAmount) : undefined,
          transferReference: transaction.transferReference,
          createdAt: transaction.createdAt.toISOString(),
          updatedAt: transaction.updatedAt.toISOString(),

          // Format installments
          installments: transaction.installments.map((installment: Installment) => ({
            id: installment.id,
            installmentNumber: installment.installmentNumber,
            amount: Number(installment.amount),
            dueDate: installment.dueDate.toISOString().split('T')[0],
            isPaid: installment.isPaid,
            paidAt: installment.paidAt?.toISOString(),
            description: installment.description
          })),

          // Format related data (same as before)
          account: {
            id: transaction.account.id,
            name: transaction.account.name,
            type: transaction.account.type,
            currency: transaction.account.currency
          },

          category: transaction.category ? {
            id: transaction.category.id,
            name: transaction.category.name,
            color: transaction.category.color
          } : undefined,

          destinationAccount: transaction.destinationAccount ? {
            id: transaction.destinationAccount.id,
            name: transaction.destinationAccount.name,
            type: transaction.destinationAccount.type,
            currency: transaction.destinationAccount.currency
          } : undefined,

          tags: transaction.tags.map((tagRelation) => ({
            id: tagRelation.tag.id,
            name: tagRelation.tag.name,
            color: tagRelation.tag.color
          })),

          familyMembers: transaction.members.map((memberRelation) => ({
            id: memberRelation.familyMember.id,
            name: memberRelation.familyMember.name,
            color: memberRelation.familyMember.color
          }))
        };
      });

      // Add recurring transactions as pending if includeRecurring is true
      if (filters.includeRecurring === 'true') {
        const recurringTransactions = await this.generatePendingFromRecurring(userId, filters);
        formattedTransactions.push(...recurringTransactions);

        // Update summary with recurring transactions
        recurringTransactions.forEach(transaction => {
          if (transaction.type === 'INCOME') {
            summary.totalIncome += transaction.totalAmount;
          } else if (transaction.type === 'EXPENSE') {
            summary.totalExpenses += transaction.totalAmount;
          }
        });
      }

      summary.netAmount = summary.totalIncome - summary.totalExpenses;

      return { transactions: formattedTransactions, summary };
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Get all transactions with installments (legacy method)
   */
  private async getAllTransactions() {
    // Import prisma here to avoid circular dependency
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      const transactions = await prisma.transaction.findMany({
        where: { deletedAt: null },
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          account: true,
          category: true,
          destinationAccount: true,
          tags: {
            include: { tag: true }
          },
          members: {
            include: { familyMember: true }
          }
        },
        orderBy: { transactionDate: 'desc' }
      });

      // Format transactions to match frontend expectations
      return transactions.map((transaction: PopulatedTransaction) => ({
        id: transaction.id,
        description: transaction.description,
        totalAmount: Number(transaction.totalAmount),
        totalInstallments: transaction.totalInstallments,
        transactionDate: transaction.transactionDate.toISOString().split('T')[0],
        type: transaction.type,
        accountId: transaction.accountId,
        categoryId: transaction.categoryId,
        destinationAccountId: transaction.destinationAccountId,
        exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
        isFuture: transaction.isFuture,
        sourceCurrency: transaction.sourceCurrency,
        destinationCurrency: transaction.destinationCurrency,
        sourceAmount: transaction.sourceAmount ? Number(transaction.sourceAmount) : undefined,
        destinationAmount: transaction.destinationAmount ? Number(transaction.destinationAmount) : undefined,
        transferReference: transaction.transferReference,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),

        // Format installments
        installments: transaction.installments.map((installment: Installment) => ({
          id: installment.id,
          installmentNumber: installment.installmentNumber,
          amount: Number(installment.amount),
          dueDate: installment.dueDate.toISOString().split('T')[0],
          isPaid: installment.isPaid,
          paidAt: installment.paidAt?.toISOString(),
          description: installment.description
        })),

        // Format related data
        account: {
          id: transaction.account.id,
          name: transaction.account.name,
          type: transaction.account.type,
          currency: transaction.account.currency
        },

        category: transaction.category ? {
          id: transaction.category.id,
          name: transaction.category.name,
          color: transaction.category.color
        } : undefined,

        destinationAccount: transaction.destinationAccount ? {
          id: transaction.destinationAccount.id,
          name: transaction.destinationAccount.name,
          type: transaction.destinationAccount.type,
          currency: transaction.destinationAccount.currency
        } : undefined,

        // Format tags - flatten the nested structure
        tags: transaction.tags.map((tagRelation) => ({
          id: tagRelation.tag.id,
          name: tagRelation.tag.name,
          color: tagRelation.tag.color
        })),

        // Format family members - flatten the nested structure
        familyMembers: transaction.members.map((memberRelation) => ({
          id: memberRelation.familyMember.id,
          name: memberRelation.familyMember.name,
          color: memberRelation.familyMember.color
        }))
      }));
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    console.error('Transaction Controller Error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          res.status(409).json({
            success: false,
            error: {
              message: 'Violação de restrição única',
              code: 'UNIQUE_CONSTRAINT_VIOLATION'
            }
          });
          return;
        case 'P2003':
          res.status(400).json({
            success: false,
            error: {
              message: 'Violação de chave estrangeira',
              code: 'FOREIGN_KEY_CONSTRAINT_VIOLATION'
            }
          });
          return;
        case 'P2025':
          res.status(404).json({
            success: false,
            error: {
              message: 'Registro não encontrado',
              code: 'RECORD_NOT_FOUND'
            }
          });
          return;
      }
    }

    // Handle application errors
    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'TRANSACTION_ERROR'
        }
      });
      return;
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }

  /**
   * Generate pending transactions from recurring transactions
   */
  private async generatePendingFromRecurring(userId: string, filters: any) {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      // Get active recurring transactions
      const recurringTransactions = await prisma.recurringTransaction.findMany({
        where: {
          userId,
          isActive: true,
          deletedAt: null
        },
        include: {
          account: true,
          category: true
        }
      });

      const pendingTransactions = [];
      const today = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 3); // Generate for next 3 months

      for (const recurring of recurringTransactions) {
        // Apply filters to recurring transactions
        if (filters.type && recurring.type !== filters.type) continue;
        if (filters.accountId && recurring.accountId !== filters.accountId) continue;
        if (filters.categoryId && recurring.categoryId !== filters.categoryId) continue;

        // Generate dates based on frequency
        const dates = this.generateRecurringDates(recurring.startDate, recurring.frequency, endDate);

        for (const date of dates) {
          // Skip if date is before filter start date
          if (filters.startDate && date < new Date(filters.startDate)) continue;
          if (filters.endDate && date > new Date(filters.endDate)) continue;

          // Create pending transaction object
          const pendingTransaction = {
            id: `recurring-${recurring.id}-${date.getTime()}`,
            description: `${recurring.description} (Recorrente)`,
            totalAmount: Number(recurring.fixedAmount),
            totalInstallments: 1,
            transactionDate: date.toISOString().split('T')[0],
            type: recurring.type,
            accountId: recurring.accountId,
            categoryId: recurring.categoryId,
            destinationAccountId: null,
            exchangeRate: null,
            isFuture: date > today,
            sourceCurrency: null,
            destinationCurrency: null,
            sourceAmount: null,
            destinationAmount: null,
            transferReference: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isRecurring: true, // Flag to identify recurring transactions
            recurringTransactionId: recurring.id,

            installments: [{
              id: `recurring-installment-${recurring.id}-${date.getTime()}`,
              installmentNumber: 1,
              amount: Number(recurring.fixedAmount),
              dueDate: date.toISOString().split('T')[0],
              isPaid: false,
              paidAt: null,
              description: `${recurring.description} (Recorrente)`
            }],

            account: {
              id: recurring.account.id,
              name: recurring.account.name,
              type: recurring.account.type,
              currency: recurring.account.currency
            },

            category: recurring.category ? {
              id: recurring.category.id,
              name: recurring.category.name,
              color: recurring.category.color
            } : undefined,

            destinationAccount: undefined,
            tags: [],
            members: []
          };

          pendingTransactions.push(pendingTransaction);
        }
      }

      return pendingTransactions;
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Generate dates for recurring transactions based on frequency
   */
  private generateRecurringDates(startDate: Date, frequency: string, endDate: Date): Date[] {
    const dates = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));

      switch (frequency) {
        case 'DAILY':
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case 'WEEKLY':
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case 'BIWEEKLY':
          currentDate.setDate(currentDate.getDate() + 14);
          break;
        case 'MONTHLY':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case 'BIANNUAL':
          currentDate.setMonth(currentDate.getMonth() + 6);
          break;
        case 'YEARLY':
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
        default:
          // If unknown frequency, break to avoid infinite loop
          break;
      }
    }

    return dates;
  }
}

export const transactionController = new TransactionController();
