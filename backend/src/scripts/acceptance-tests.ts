#!/usr/bin/env ts-node

/**
 * Testes de Aceitação para Funcionalidades Avançadas
 * Valida integração entre todos os serviços implementados
 */

import { PrismaClient } from '@prisma/client';
import { TransactionService } from '../services/transaction.service';
import { CurrencyService } from '../services/currency.service';
import { FutureTransactionJobService } from '../services/future-transaction-job.service';

const prisma = new PrismaClient();

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration: number;
}

class AcceptanceTests {
  private transactionService = new TransactionService();
  private currencyService = new CurrencyService();
  private jobService = new FutureTransactionJobService();
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Iniciando Testes de Aceitação...\n');

    const tests = [
      { name: '<PERSON><PERSON><PERSON> de Moedas', fn: this.testCurrencyConversion },
      { name: 'Transferência Simples', fn: this.testSimpleTransfer },
      { name: 'Transferência com Conversão', fn: this.testTransferWithConversion },
      { name: 'Transação Parcelada', fn: this.testInstallmentTransaction },
      { name: 'Funcionalidades de Parcelas', fn: this.testInstallmentFeatures },
      { name: 'Transações Futuras', fn: this.testFutureTransactions },
      { name: 'Processamento em Lote', fn: this.testBatchProcessing },
      { name: 'Validações de Negócio', fn: this.testBusinessValidations }
    ];

    for (const test of tests) {
      await this.runTest(test.name, test.fn.bind(this));
    }

    this.printResults();
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Executando: ${testName}`);
      await testFn();
      
      const duration = Date.now() - startTime;
      this.results.push({ testName, passed: true, duration });
      console.log(`✅ ${testName} - ${duration}ms\n`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({ 
        testName, 
        passed: false, 
        error: error instanceof Error ? error.message : String(error),
        duration 
      });
      console.log(`❌ ${testName} - ${error}\n`);
    }
  }

  private async testCurrencyConversion(): Promise<void> {
    // Teste 1: Conversão com taxa manual
    const result1 = await this.currencyService.convertAmount(
      1000,
      'USD',
      'BRL',
      5.25
    );

    if (result1.convertedAmount !== 5250) {
      throw new Error(`Conversão manual incorreta: esperado 5250, recebido ${result1.convertedAmount}`);
    }

    // Teste 2: Conversão mesma moeda
    const result2 = await this.currencyService.convertAmount(
      1000,
      'BRL',
      'BRL'
    );

    if (result2.exchangeRate !== 1.0) {
      throw new Error(`Taxa para mesma moeda deve ser 1.0, recebido ${result2.exchangeRate}`);
    }

    // Teste 3: Formatação de moeda
    const formatted = this.currencyService.formatCurrency(1234.56, 'BRL');
    if (!formatted.includes('1.234,56')) {
      throw new Error(`Formatação BRL incorreta: ${formatted}`);
    }
  }

  private async testSimpleTransfer(): Promise<void> {
    // Buscar contas de teste
    const accounts = await prisma.account.findMany({
      where: { currency: 'BRL' },
      take: 2
    });

    if (accounts.length < 2) {
      throw new Error('Necessário pelo menos 2 contas BRL para teste');
    }

    const [sourceAccount, destAccount] = accounts;
    const initialSourceBalance = Number(sourceAccount.currentBalance);
    const initialDestBalance = Number(destAccount.currentBalance);

    // Criar transferência
    const transfer = await this.transactionService.create({
      description: 'Teste transferência simples',
      totalAmount: 100.00,
      type: 'TRANSFER',
      accountId: sourceAccount.id,
      destinationAccountId: destAccount.id,
      transactionDate: new Date(),
      familyMemberIds: [],
      isFuture: false
    });

    // Verificar se a transferência foi criada
    if (!transfer.id) {
      throw new Error('Transferência não foi criada');
    }

    // Verificar saldos atualizados
    const updatedSource = await prisma.account.findUnique({
      where: { id: sourceAccount.id }
    });
    const updatedDest = await prisma.account.findUnique({
      where: { id: destAccount.id }
    });

    if (!updatedSource || !updatedDest) {
      throw new Error('Contas não encontradas após transferência');
    }

    const expectedSourceBalance = initialSourceBalance - 100;
    const expectedDestBalance = initialDestBalance + 100;

    if (Math.abs(Number(updatedSource.currentBalance) - expectedSourceBalance) > 0.01) {
      throw new Error(`Saldo origem incorreto: esperado ${expectedSourceBalance}, atual ${updatedSource.currentBalance}`);
    }

    if (Math.abs(Number(updatedDest.currentBalance) - expectedDestBalance) > 0.01) {
      throw new Error(`Saldo destino incorreto: esperado ${expectedDestBalance}, atual ${updatedDest.currentBalance}`);
    }
  }

  private async testTransferWithConversion(): Promise<void> {
    // Buscar contas USD e BRL
    const usdAccount = await prisma.account.findFirst({
      where: { currency: 'USD' }
    });
    const brlAccount = await prisma.account.findFirst({
      where: { currency: 'BRL' }
    });

    if (!usdAccount || !brlAccount) {
      throw new Error('Necessário contas USD e BRL para teste');
    }

    // Criar transferência com conversão
    const transfer = await this.transactionService.create({
      description: 'Teste conversão USD→BRL',
      totalAmount: 100.00,
      type: 'TRANSFER',
      accountId: usdAccount.id,
      destinationAccountId: brlAccount.id,
      transactionDate: new Date(),
      exchangeRate: 5.25,
      sourceCurrency: 'USD',
      destinationCurrency: 'BRL',
      sourceAmount: 100.00,
      destinationAmount: 525.00,
      familyMemberIds: [],
      isFuture: false
    });

    // Verificar campos de conversão
    if (Number(transfer.exchangeRate) !== 5.25) {
      throw new Error(`Taxa de câmbio incorreta: ${transfer.exchangeRate}`);
    }

    if (Number(transfer.sourceAmount) !== 100.00) {
      throw new Error(`Valor origem incorreto: ${transfer.sourceAmount}`);
    }

    if (Number(transfer.destinationAmount) !== 525.00) {
      throw new Error(`Valor destino incorreto: ${transfer.destinationAmount}`);
    }
  }

  private async testInstallmentTransaction(): Promise<void> {
    // Buscar conta de crédito
    const creditAccount = await prisma.account.findFirst({
      where: { type: 'CREDIT_CARD' }
    });

    if (!creditAccount) {
      throw new Error('Necessário conta de crédito para teste');
    }

    // Criar transação parcelada
    const installments = [
      { amount: 200.00, dueDate: new Date('2024-02-01'), isPaid: false },
      { amount: 200.00, dueDate: new Date('2024-03-01'), isPaid: false },
      { amount: 200.00, dueDate: new Date('2024-04-01'), isPaid: false }
    ];

    const result = await this.transactionService.create({
      description: 'Teste parcelamento 3x',
      totalAmount: 600.00,
      type: 'EXPENSE',
      accountId: creditAccount.id,
      transactionDate: new Date('2024-02-01'),
      installments: installments,
      familyMemberIds: [],
      isFuture: false
    });

    // Verificar resultado
    if (!result.id) {
      throw new Error('Transação principal não criada');
    }

    if (result.installments.length !== 3) {
      throw new Error(`Esperado 3 parcelas, criadas ${result.installments.length}`);
    }

    // Verificar valores das parcelas
    const totalParcelas = result.installments.reduce(
      (sum: number, installment: any) => sum + Number(installment.amount), 0
    );

    if (Math.abs(totalParcelas - 600.00) > 0.01) {
      throw new Error(`Soma das parcelas incorreta: ${totalParcelas}`);
    }
  }

  private async testInstallmentFeatures(): Promise<void> {
    // Buscar uma transação com parcelas
    const transaction = await prisma.transaction.findFirst({
      where: { 
        totalInstallments: { gt: 1 },
        deletedAt: null
      },
      include: { installments: true }
    });

    if (!transaction) {
      throw new Error('Nenhuma transação parcelada encontrada para teste');
    }

    // Testar resumo de parcelas
    const summary = await this.transactionService.getInstallmentSummary(transaction.id);
    
    if (summary.totalInstallments !== transaction.installments.length) {
      throw new Error(`Total de parcelas incorreto no resumo: ${summary.totalInstallments}`);
    }

    // Testar busca de parcelas por transação
    const installmentsByTransaction = await this.transactionService.getInstallmentsByTransaction(transaction.id);
    
    if (!installmentsByTransaction.transaction || !installmentsByTransaction.summary) {
      throw new Error('Dados de parcelas por transação incompletos');
    }

    console.log(`   📊 Resumo: ${summary.paidInstallments}/${summary.totalInstallments} pagas`);
  }

  private async testFutureTransactions(): Promise<void> {
    // Buscar conta para teste
    const account = await prisma.account.findFirst({
      where: { currency: 'BRL' }
    });

    if (!account) {
      throw new Error('Nenhuma conta BRL encontrada');
    }

    // Criar transação futura
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 30); // 30 dias no futuro

    const futureTransaction = await this.transactionService.create({
      description: 'Teste transação futura',
      totalAmount: 50.00,
      type: 'EXPENSE',
      accountId: account.id,
      transactionDate: futureDate,
      familyMemberIds: [],
      isFuture: true
    });

    if (!futureTransaction.isFuture) {
      throw new Error('Transação não marcada como futura');
    }

    console.log(`   📅 Transação futura criada para ${futureDate.toLocaleDateString()}`);
  }

  private async testBatchProcessing(): Promise<void> {
    // Executar job de processamento
    const result = await this.jobService.execute();

    if (typeof result.processedTransactions !== 'number') {
      throw new Error('Resultado do job inválido');
    }

    console.log(`   📊 Processadas: ${result.processedTransactions}, Falhas: 0`);
  }

  private async testBusinessValidations(): Promise<void> {
    // Buscar conta para teste
    const account = await prisma.account.findFirst();
    if (!account) {
      throw new Error('Nenhuma conta encontrada');
    }

    // Teste 1: Transferência para mesma conta (deve falhar)
    try {
      await this.transactionService.create({
        description: 'Transferência inválida',
        totalAmount: 100.00,
        type: 'TRANSFER',
        accountId: account.id,
        destinationAccountId: account.id,
        transactionDate: new Date(),
        familyMemberIds: [],
        isFuture: false
      });
      throw new Error('Transferência para mesma conta deveria falhar');
    } catch (error: any) {
      if (!error.message.includes('same account') && !error.message.includes('mesma conta')) {
        throw new Error(`Erro inesperado: ${error.message}`);
      }
    }

    // Teste 2: Valor negativo (deve falhar)
    try {
      await this.currencyService.convertAmount(
        -100,
        'USD',
        'BRL'
      );
      throw new Error('Conversão com valor negativo deveria falhar');
    } catch (error: any) {
      if (!error.message.includes('positive') && !error.message.includes('positivo')) {
        throw new Error(`Erro inesperado: ${error.message}`);
      }
    }
  }

  private printResults(): void {
    console.log('\n📊 RESULTADOS DOS TESTES DE ACEITAÇÃO\n');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => r.passed === false).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passou: ${passed}`);
    console.log(`❌ Falhou: ${failed}`);
    console.log(`⏱️  Tempo total: ${totalTime}ms`);
    console.log(`📈 Taxa de sucesso: ${((passed / this.results.length) * 100).toFixed(1)}%\n`);

    if (failed > 0) {
      console.log('❌ TESTES QUE FALHARAM:\n');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   • ${r.testName}: ${r.error}`);
        });
      console.log('');
    }

    if (passed === this.results.length) {
      console.log('🎉 TODOS OS TESTES PASSARAM! Sistema pronto para produção.\n');
    } else {
      console.log('⚠️  Alguns testes falharam. Revisar antes de produção.\n');
    }
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  const tests = new AcceptanceTests();
  tests.runAllTests()
    .then(() => {
      console.log('✅ Testes de aceitação concluídos');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erro nos testes:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { AcceptanceTests };
