import prisma from '../lib/prisma';
import { performance } from 'perf_hooks';

/**
 * <PERSON><PERSON>t to optimize dashboard performance by analyzing and creating indexes
 */

interface QueryPerformanceResult {
  query: string;
  duration: number;
  rowsAffected?: number;
  explanation?: any;
}

class DashboardIndexOptimizer {
  private results: QueryPerformanceResult[] = [];

  /**
   * Test query performance before and after index creation
   */
  async analyzeQueryPerformance(): Promise<void> {
    console.log('🔍 Analyzing dashboard query performance...\n');

    // Test queries that are commonly used in dashboard
    const testQueries = [
      {
        name: 'Account Balance Aggregation',
        query: () => prisma.account.findMany({
          where: {
            deletedAt: null,
            includeInTotal: true,
            type: 'CHECKING'
          },
          select: {
            currentBalance: true,
            currency: true,
            exchangeRate: true
          }
        })
      },
      {
        name: 'Expense Transactions by Date Range',
        query: () => prisma.transaction.findMany({
          where: {
            deletedAt: null,
            type: 'EXPENSE',
            transactionDate: {
              gte: new Date('2024-01-01'),
              lte: new Date('2024-12-31')
            }
          },
          select: {
            totalAmount: true,
            categoryId: true,
            accountId: true
          },
          take: 1000
        })
      },
      {
        name: 'Budget Comparison Query',
        query: () => prisma.budget.findMany({
          where: {
            deletedAt: null,
            year: 2024,
            month: 12
          },
          include: {
            category: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })
      },
      {
        name: 'Goal Progress Query',
        query: () => prisma.goal.findMany({
          where: {
            deletedAt: null
          },
          include: {
            milestones: true,
            members: {
              include: {
                familyMember: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        })
      },
      {
        name: 'Transaction Members Join',
        query: () => prisma.transaction.findMany({
          where: {
            deletedAt: null,
            type: 'EXPENSE',
            transactionDate: {
              gte: new Date('2024-01-01')
            }
          },
          include: {
            members: {
              include: {
                familyMember: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            category: {
              select: {
                id: true,
                name: true,
                parent: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          },
          take: 500
        })
      },
      {
        name: 'Credit Card Usage Analysis',
        query: () => prisma.account.findMany({
          where: {
            deletedAt: null,
            type: 'CREDIT_CARD'
          },
          select: {
            id: true,
            name: true,
            currentBalance: true,
            creditLimit: true,
            currency: true,
            exchangeRate: true
          }
        })
      },
      {
        name: 'Net Worth History Query',
        query: () => prisma.accountBalanceHistory.findMany({
          where: {
            balanceDate: {
              gte: new Date('2023-01-01'),
              lte: new Date('2024-12-31')
            }
          },
          include: {
            account: {
              select: {
                type: true,
                currency: true,
                exchangeRate: true,
                includeInTotal: true
              }
            }
          },
          orderBy: {
            balanceDate: 'asc'
          }
        })
      }
    ];

    // Run performance tests
    for (const test of testQueries) {
      await this.measureQueryPerformance(test.name, test.query);
    }

    // Display results
    this.displayResults();
  }

  /**
   * Measure performance of a specific query
   */
  private async measureQueryPerformance(
    queryName: string, 
    queryFn: () => Promise<any>
  ): Promise<void> {
    console.log(`⏱️  Testing: ${queryName}`);
    
    try {
      const startTime = performance.now();
      const result = await queryFn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      this.results.push({
        query: queryName,
        duration: Math.round(duration * 100) / 100, // Round to 2 decimal places
        rowsAffected: Array.isArray(result) ? result.length : 1
      });

      console.log(`   ✅ Completed in ${duration.toFixed(2)}ms (${Array.isArray(result) ? result.length : 1} rows)`);
    } catch (error) {
      console.log(`   ❌ Failed: ${error}`);
      this.results.push({
        query: queryName,
        duration: -1,
        rowsAffected: 0
      });
    }
  }

  /**
   * Display performance analysis results
   */
  private displayResults(): void {
    console.log('\n📊 Performance Analysis Results:');
    console.log('=====================================');
    
    const sortedResults = this.results
      .filter(r => r.duration > 0)
      .sort((a, b) => b.duration - a.duration);

    sortedResults.forEach((result, index) => {
      const status = result.duration > 1000 ? '🔴' : result.duration > 500 ? '🟡' : '🟢';
      console.log(`${index + 1}. ${status} ${result.query}`);
      console.log(`   Duration: ${result.duration}ms | Rows: ${result.rowsAffected}`);
    });

    // Summary
    const avgDuration = sortedResults.reduce((sum, r) => sum + r.duration, 0) / sortedResults.length;
    const slowQueries = sortedResults.filter(r => r.duration > 1000).length;
    
    console.log('\n📈 Summary:');
    console.log(`   Average query time: ${avgDuration.toFixed(2)}ms`);
    console.log(`   Slow queries (>1s): ${slowQueries}/${sortedResults.length}`);
    
    if (slowQueries > 0) {
      console.log('\n⚠️  Recommendations:');
      console.log('   - Consider adding more specific indexes for slow queries');
      console.log('   - Review query patterns and add composite indexes');
      console.log('   - Consider implementing caching for frequently accessed data');
    }
  }

  /**
   * Apply database indexes for dashboard optimization
   */
  async applyDashboardIndexes(): Promise<void> {
    console.log('🔧 Applying dashboard optimization indexes...\n');

    const indexQueries = [
      // Transaction indexes
      'CREATE INDEX IF NOT EXISTS "Transaction_transactionDate_idx" ON "transactions"("transaction_date");',
      'CREATE INDEX IF NOT EXISTS "Transaction_type_idx" ON "transactions"("type");',
      'CREATE INDEX IF NOT EXISTS "Transaction_accountId_idx" ON "transactions"("account_id");',
      'CREATE INDEX IF NOT EXISTS "Transaction_categoryId_idx" ON "transactions"("category_id");',
      'CREATE INDEX IF NOT EXISTS "Transaction_expense_analysis_idx" ON "transactions"("type", "transaction_date", "account_id") WHERE "type" = \'EXPENSE\';',
      'CREATE INDEX IF NOT EXISTS "Transaction_future_idx" ON "transactions"("is_future", "transaction_date") WHERE "is_future" = true;',
      
      // Account indexes
      'CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "accounts"("type");',
      'CREATE INDEX IF NOT EXISTS "Account_currency_idx" ON "accounts"("currency");',
      'CREATE INDEX IF NOT EXISTS "Account_includeInTotal_idx" ON "accounts"("include_in_total");',
      'CREATE INDEX IF NOT EXISTS "Account_balance_analysis_idx" ON "accounts"("type", "currency", "include_in_total") WHERE "deleted_at" IS NULL;',
      
      // Budget indexes
      'CREATE INDEX IF NOT EXISTS "Budget_period_idx" ON "budgets"("year", "month");',
      'CREATE INDEX IF NOT EXISTS "Budget_categoryId_idx" ON "budgets"("category_id");',
      
      // Goal indexes
      'CREATE INDEX IF NOT EXISTS "Goal_targetDate_idx" ON "goals"("target_date");',
      
      // Junction table indexes
      'CREATE INDEX IF NOT EXISTS "TransactionMember_transactionId_idx" ON "transaction_members"("transaction_id");',
      'CREATE INDEX IF NOT EXISTS "TransactionMember_familyMemberId_idx" ON "transaction_members"("family_member_id");',
      'CREATE INDEX IF NOT EXISTS "AccountMember_accountId_idx" ON "account_members"("account_id");',
      'CREATE INDEX IF NOT EXISTS "AccountMember_familyMemberId_idx" ON "account_members"("family_member_id");',
      
      // Balance history indexes
      'CREATE INDEX IF NOT EXISTS "AccountBalanceHistory_balanceDate_idx" ON "account_balance_history"("balance_date");',
      'CREATE INDEX IF NOT EXISTS "AccountBalanceHistory_networth_idx" ON "account_balance_history"("balance_date", "account_id", "balance");'
    ];

    for (const [index, query] of indexQueries.entries()) {
      try {
        console.log(`   Creating index ${index + 1}/${indexQueries.length}...`);
        await prisma.$executeRawUnsafe(query);
        console.log(`   ✅ Index created successfully`);
      } catch (error) {
        console.log(`   ⚠️  Index may already exist or failed: ${error}`);
      }
    }

    console.log('\n✅ Dashboard indexes optimization completed!');
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<void> {
    console.log('📊 Database Statistics:');
    console.log('=======================');

    try {
      // Get table sizes (PostgreSQL specific)
      const tableStats = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE schemaname = 'public' 
        AND tablename IN ('transactions', 'accounts', 'budgets', 'goals')
        ORDER BY tablename, attname;
      `;

      console.log('Table statistics:', tableStats);
    } catch (error) {
      console.log('Could not retrieve database statistics (may not be PostgreSQL)');
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const optimizer = new DashboardIndexOptimizer();

  console.log('🚀 Dashboard Performance Optimization Tool');
  console.log('==========================================\n');

  // Step 1: Analyze current performance
  await optimizer.analyzeQueryPerformance();

  // Step 2: Apply optimization indexes
  await optimizer.applyDashboardIndexes();

  // Step 3: Re-analyze performance
  console.log('\n🔄 Re-analyzing performance after optimization...\n');
  await optimizer.analyzeQueryPerformance();

  // Step 4: Get database stats
  await optimizer.getDatabaseStats();

  console.log('\n🎉 Dashboard optimization completed!');
}

// Run the optimization
if (require.main === module) {
  main()
    .catch((e) => {
      console.error('❌ Optimization failed:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { DashboardIndexOptimizer };
