import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Script para otimizar índices do schema Prisma
 * Remove índices redundantes e cria índices compostos otimizados
 */
async function optimizeSchemaIndexes() {
  console.log('🔧 Iniciando otimização de índices do schema...');
  console.log('📅 Data:', new Date().toISOString());
  console.log('');

  try {
    // Verificar conexão com o banco
    await prisma.$connect();
    console.log('✅ Conectado ao banco de dados');

    // 1. Otimizar índices da tabela transactions
    console.log('');
    console.log('📊 Otimizando índices da tabela transactions...');
    
    // Criar índice composto otimizado para dashboard
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transactions_dashboard_optimized" 
      ON "transactions"("transaction_date" DESC, "account_id", "type") 
      WHERE "deleted_at" IS NULL;
    `;
    console.log('✅ Índice dashboard otimizado criado');

    // Criar índice para análise por categoria
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transactions_category_analysis" 
      ON "transactions"("category_id", "transaction_date" DESC) 
      WHERE "deleted_at" IS NULL AND "category_id" IS NOT NULL;
    `;
    console.log('✅ Índice análise por categoria criado');

    // Criar índice para transações futuras
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transactions_future_optimized" 
      ON "transactions"("is_future", "transaction_date" ASC) 
      WHERE "deleted_at" IS NULL AND "is_future" = true;
    `;
    console.log('✅ Índice transações futuras otimizado criado');

    // 2. Otimizar índices da tabela accounts
    console.log('');
    console.log('💰 Otimizando índices da tabela accounts...');
    
    // Criar índice composto para cálculo de saldo
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_accounts_balance_calculation" 
      ON "accounts"("type", "include_in_total", "currency") 
      WHERE "deleted_at" IS NULL;
    `;
    console.log('✅ Índice cálculo de saldo criado');

    // Criar índice para cache invalidation
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_accounts_cache_invalidation" 
      ON "accounts"("updated_at" DESC) 
      WHERE "deleted_at" IS NULL;
    `;
    console.log('✅ Índice cache invalidation criado');

    // 3. Otimizar índices da tabela goals
    console.log('');
    console.log('🎯 Otimizando índices da tabela goals...');
    
    // Criar índice composto para dashboard de metas
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_goals_dashboard_optimized" 
      ON "goals"("goal_type", "target_date" ASC NULLS LAST, "current_amount", "target_amount") 
      WHERE "deleted_at" IS NULL;
    `;
    console.log('✅ Índice dashboard de metas otimizado criado');

    // 4. Otimizar índices da tabela insights
    console.log('');
    console.log('💡 Otimizando índices da tabela insights...');
    
    // Criar índice composto para consultas de insights
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_insights_active_prioritized" 
      ON "insights"("status", "priority", "type", "created_at" DESC) 
      WHERE "deleted_at" IS NULL AND "status" IN ('NEW', 'VIEWED');
    `;
    console.log('✅ Índice insights ativos priorizados criado');

    // 5. Remover índices redundantes (se existirem)
    console.log('');
    console.log('🧹 Removendo índices redundantes...');
    
    const redundantIndexes = [
      'accounts_type_idx',
      'accounts_currency_idx',
      'transactions_type_idx',
      'transactions_account_id_idx',
      'goals_target_date_idx'
    ];

    for (const indexName of redundantIndexes) {
      try {
        await prisma.$executeRaw`DROP INDEX CONCURRENTLY IF EXISTS ${indexName};`;
        console.log(`✅ Índice redundante removido: ${indexName}`);
      } catch (error) {
        console.log(`⚠️  Índice não encontrado ou já removido: ${indexName}`);
      }
    }

    // 6. Analisar estatísticas das tabelas
    console.log('');
    console.log('📈 Atualizando estatísticas das tabelas...');
    
    const tables = ['transactions', 'accounts', 'goals', 'insights', 'categories'];
    for (const table of tables) {
      await prisma.$executeRaw`ANALYZE ${table};`;
      console.log(`✅ Estatísticas atualizadas: ${table}`);
    }

    // 7. Verificar tamanho dos índices
    console.log('');
    console.log('📊 Verificando tamanho dos índices...');
    
    const indexSizes = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes 
      WHERE schemaname = 'public'
      ORDER BY pg_relation_size(indexrelid) DESC
      LIMIT 10;
    `;

    console.log('🔍 Top 10 maiores índices:');
    console.table(indexSizes);

    console.log('');
    console.log('🎉 Otimização de índices concluída com sucesso!');
    console.log('');
    console.log('📋 Resumo das otimizações:');
    console.log('   ✅ Índices compostos otimizados criados');
    console.log('   ✅ Índices redundantes removidos');
    console.log('   ✅ Estatísticas das tabelas atualizadas');
    console.log('   ✅ Performance de queries melhorada');
    console.log('');
    console.log('💡 Recomendações:');
    console.log('   - Execute VACUUM ANALYZE periodicamente');
    console.log('   - Monitore performance das queries');
    console.log('   - Considere particionamento para tabelas grandes');

  } catch (error) {
    console.error('❌ Erro durante otimização:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Desconectado do banco de dados');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  optimizeSchemaIndexes()
    .then(() => {
      console.log('✅ Script executado com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erro na execução:', error);
      process.exit(1);
    });
}

export { optimizeSchemaIndexes };