#!/usr/bin/env ts-node

/**
 * Script para popular o banco de dados com dados de demonstração
 * das funcionalidades avançadas (transferências, parcelas, conversões)
 */

import { PrismaClient, TransactionType, AccountType } from '@prisma/client';
import { TransactionService } from '../services/transaction.service';
import { CurrencyService } from '../services/currency.service';

const prisma = new PrismaClient();
const transactionService = new TransactionService();
const currencyService = new CurrencyService();

async function main() {
  console.log('🚀 Iniciando seed das funcionalidades avançadas...');

  try {
    // 1. Criar usuário de demonstração
    const demoUser = await createDemoUser();
    console.log('✅ Usuário de demonstração criado');

    // 2. Criar contas em diferentes moedas
    const accounts = await createDemoAccounts(demoUser.id);
    console.log('✅ Contas de demonstração criadas');

    // 3. Criar categorias
    const categories = await createDemoCategories(demoUser.id);
    console.log('✅ Categorias criadas');

    // 4. Criar transferências de exemplo
    await createDemoTransfers(accounts, demoUser.id);
    console.log('✅ Transferências de exemplo criadas');

    // 5. Criar transações parceladas
    await createDemoInstallments(accounts, categories, demoUser.id);
    console.log('✅ Transações parceladas criadas');

    // 6. Criar transações futuras
    await createFutureTransactions(accounts, categories, demoUser.id);
    console.log('✅ Transações futuras criadas');

    console.log('🎉 Seed concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante o seed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function createDemoUser() {
  return await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Usuário Demonstração',
      password: 'demo123', // Em produção, usar hash
    }
  });
}

async function createDemoAccounts(userId: string) {
  const accounts = [];

  // Conta corrente BRL
  const checkingBRL = await prisma.account.upsert({
    where: {
      id: 'checking-brl-demo'
    },
    update: {},
    create: {
      id: 'checking-brl-demo',
      name: 'Conta Corrente BRL',
      type: AccountType.CHECKING,
      currency: 'BRL',
      currentBalance: 15000.00
    }
  });
  accounts.push(checkingBRL);

  // Poupança BRL
  const savingsBRL = await prisma.account.upsert({
    where: {
      id: 'savings-brl-demo'
    },
    update: {},
    create: {
      id: 'savings-brl-demo',
      name: 'Poupança BRL',
      type: AccountType.SAVINGS,
      currency: 'BRL',
      currentBalance: 25000.00
    }
  });
  accounts.push(savingsBRL);

  // Conta USD
  const accountUSD = await prisma.account.upsert({
    where: {
      id: 'checking-usd-demo'
    },
    update: {},
    create: {
      id: 'checking-usd-demo',
      name: 'Conta USD',
      type: AccountType.CHECKING,
      currency: 'USD',
      currentBalance: 5000.00
    }
  });
  accounts.push(accountUSD);

  // Cartão de Crédito
  const creditCard = await prisma.account.upsert({
    where: {
      id: 'credit-card-demo'
    },
    update: {},
    create: {
      id: 'credit-card-demo',
      name: 'Cartão de Crédito',
      type: AccountType.CREDIT_CARD,
      currency: 'BRL',
      currentBalance: -2500.00, // Saldo negativo (dívida)
      creditLimit: 10000.00
    }
  });
  accounts.push(creditCard);

  return accounts;
}

async function createDemoCategories(userId: string) {
  const categories = [];

  const categoryData = [
    { name: 'Eletrônicos', description: 'Compras de eletrônicos e tecnologia' },
    { name: 'Veículos', description: 'Gastos com veículos e transporte' },
    { name: 'Casa', description: 'Gastos com casa e móveis' },
    { name: 'Educação', description: 'Cursos e educação' },
    { name: 'Investimentos', description: 'Transferências para investimentos' }
  ];

  for (const cat of categoryData) {
    const category = await prisma.category.upsert({
      where: {
        id: `category-${cat.name.toLowerCase().replace(/\s+/g, '-')}-demo`
      },
      update: {},
      create: {
        id: `category-${cat.name.toLowerCase().replace(/\s+/g, '-')}-demo`,
        name: cat.name
      }
    });
    categories.push(category);
  }

  return categories;
}

async function createDemoTransfers(accounts: any[], userId: string) {
  const [checkingBRL, savingsBRL, accountUSD, creditCard] = accounts;

  // 1. Transferência simples BRL → BRL
  await transactionService.create({
    description: 'Transferência para poupança',
    totalAmount: 2000.00,
    type: TransactionType.TRANSFER,
    accountId: checkingBRL.id,
    destinationAccountId: savingsBRL.id,
    transactionDate: new Date('2024-01-15'),
    familyMemberIds: [],
    isFuture: false
  });

  // 2. Transferência com conversão USD → BRL
  const conversionResult = await currencyService.convertAmount(
    1000,
    'USD',
    'BRL',
    5.25
  );

  await transactionService.create({
    description: 'Conversão USD para BRL',
    totalAmount: conversionResult.originalAmount,
    type: TransactionType.TRANSFER,
    accountId: accountUSD.id,
    destinationAccountId: checkingBRL.id,
    exchangeRate: conversionResult.exchangeRate,
    sourceCurrency: 'USD',
    destinationCurrency: 'BRL',
    sourceAmount: conversionResult.originalAmount,
    destinationAmount: conversionResult.convertedAmount,
    transactionDate: new Date('2024-01-20'),
    familyMemberIds: [],
    isFuture: false
  });

  // 3. Pagamento de cartão de crédito
  await transactionService.create({
    description: 'Pagamento cartão de crédito',
    totalAmount: 1500.00,
    type: TransactionType.TRANSFER,
    accountId: checkingBRL.id,
    destinationAccountId: creditCard.id,
    transactionDate: new Date('2024-01-25'),
    familyMemberIds: [],
    isFuture: false
  });
}

async function createDemoInstallments(accounts: any[], categories: any[], userId: string) {
  const [checkingBRL, savingsBRL, accountUSD, creditCard] = accounts;
  const [electronics, vehicles, house, education] = categories;

  // Helper function to generate installments array
  function generateInstallments(totalAmount: number, installmentCount: number, startDate: Date) {
    const installmentAmount = Math.round((totalAmount / installmentCount) * 100) / 100;
    const remainder = Math.round((totalAmount - (installmentAmount * installmentCount)) * 100) / 100;
    
    const installments = [];
    for (let i = 0; i < installmentCount; i++) {
      const dueDate = new Date(startDate);
      dueDate.setMonth(dueDate.getMonth() + i);
      
      // Add remainder to last installment
      const amount = i === installmentCount - 1 ? installmentAmount + remainder : installmentAmount;
      
      installments.push({
        amount: amount,
        dueDate: dueDate,
        isPaid: false
      });
    }
    return installments;
  }

  // 1. Notebook parcelado no cartão
  await transactionService.create({
    description: 'Notebook Dell Inspiron 15',
    totalAmount: 3600.00,
    type: TransactionType.EXPENSE,
    accountId: creditCard.id,
    categoryId: electronics.id,
    transactionDate: new Date('2024-02-01'),
    installments: generateInstallments(3600.00, 12, new Date('2024-02-01')),
    familyMemberIds: [],
    isFuture: false
  });

  // 2. Financiamento de veículo
  await transactionService.create({
    description: 'Financiamento Honda Civic 2024',
    totalAmount: 80000.00,
    type: TransactionType.EXPENSE,
    accountId: checkingBRL.id,
    categoryId: vehicles.id,
    transactionDate: new Date('2024-03-01'),
    installments: generateInstallments(80000.00, 48, new Date('2024-03-01')),
    familyMemberIds: [],
    isFuture: false
  });

  // 3. Móveis para casa
  await transactionService.create({
    description: 'Sofá e mesa de jantar',
    totalAmount: 4800.00,
    type: TransactionType.EXPENSE,
    accountId: creditCard.id,
    categoryId: house.id,
    transactionDate: new Date('2024-02-15'),
    installments: generateInstallments(4800.00, 8, new Date('2024-02-15')),
    familyMemberIds: [],
    isFuture: false
  });

  // 4. Curso de especialização
  await transactionService.create({
    description: 'MBA em Gestão Financeira',
    totalAmount: 24000.00,
    type: TransactionType.EXPENSE,
    accountId: checkingBRL.id,
    categoryId: education.id,
    transactionDate: new Date('2024-04-01'),
    installments: generateInstallments(24000.00, 24, new Date('2024-04-01')),
    familyMemberIds: [],
    isFuture: false
  });
}

async function createFutureTransactions(accounts: any[], categories: any[], userId: string) {
  const [checkingBRL, savingsBRL, accountUSD, creditCard] = accounts;
  const [electronics, vehicles, house, education, investments] = categories;

  // Criar transações futuras para os próximos 6 meses
  const futureTransactions = [
    {
      description: 'Transferência automática poupança',
      totalAmount: 1000.00,
      type: TransactionType.TRANSFER,
      accountId: checkingBRL.id,
      destinationAccountId: savingsBRL.id,
      transactionDate: new Date('2024-03-01'),
      categoryId: investments.id
    },
    {
      description: 'Pagamento seguro veículo',
      totalAmount: 450.00,
      type: TransactionType.EXPENSE,
      accountId: checkingBRL.id,
      transactionDate: new Date('2024-03-15'),
      categoryId: vehicles.id
    },
    {
      description: 'Renovação curso online',
      totalAmount: 299.00,
      type: TransactionType.EXPENSE,
      accountId: creditCard.id,
      transactionDate: new Date('2024-04-01'),
      categoryId: education.id
    },
    {
      description: 'Manutenção casa',
      totalAmount: 800.00,
      type: TransactionType.EXPENSE,
      accountId: checkingBRL.id,
      transactionDate: new Date('2024-04-15'),
      categoryId: house.id
    }
  ];

  for (const transaction of futureTransactions) {
    await transactionService.create({
      ...transaction,
      familyMemberIds: [],
      isFuture: true
    });
  }
}

// Executar o script
if (require.main === module) {
  main()
    .then(() => {
      console.log('✅ Script executado com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erro na execução:', error);
      process.exit(1);
    });
}

export { main as seedAdvancedFeatures };
