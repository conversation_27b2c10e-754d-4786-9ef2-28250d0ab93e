import prisma from '../lib/prisma';
import { AccountType, TransactionType, RecurrenceFrequency, GoalType, InsightType, InsightPriority, InsightStatus } from '@prisma/client';

// Utility functions for realistic data generation
function getRandomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function getRandomAmount(min: number, max: number): number {
  return Math.round((Math.random() * (max - min) + min) * 100) / 100;
}

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function addMonths(date: Date, months: number): Date {
  const result = new Date(date);
  result.setMonth(result.getMonth() + months);
  return result;
}

async function seed() {
  console.log('🌱 Seeding database with comprehensive sample data...\n');

  try {
    // Clear existing data (in reverse order of dependencies)
    await prisma.goalProgressHistory.deleteMany();
    await prisma.insight.deleteMany();
    await prisma.installment.deleteMany();
    await prisma.transactionTag.deleteMany();
    await prisma.transactionMember.deleteMany();
    await prisma.accountMember.deleteMany();
    await prisma.goalMember.deleteMany();
    await prisma.recurringTransactionMember.deleteMany();
    await prisma.accountBalanceHistory.deleteMany();
    await prisma.goalMilestone.deleteMany();
    await prisma.transaction.deleteMany();
    await prisma.recurringTransaction.deleteMany();
    await prisma.budget.deleteMany();
    await prisma.goal.deleteMany();
    await prisma.tag.deleteMany();
    await prisma.category.deleteMany();
    await prisma.account.deleteMany();
    await prisma.familyMember.deleteMany();
    await prisma.user.deleteMany();

    console.log('🗑️  Cleared existing data');

    // Create User
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2a$10$bVHlIQja0IhXfVIddMSJ6.IumN9qbFI3WBm9yASf.BX18O0v1yQuC', // password: 123456
        name: 'Test User'
      }
    });
    console.log('👤 Created test user');

    // Create Family Members with diverse profiles
    const familyMembers = await Promise.all([
      prisma.familyMember.create({
        data: { name: 'João Silva', color: '#3B82F6' }
      }),
      prisma.familyMember.create({
        data: { name: 'Maria Silva', color: '#EF4444' }
      }),
      prisma.familyMember.create({
        data: { name: 'Pedro Silva (Filho)', color: '#10B981' }
      }),
      prisma.familyMember.create({
        data: { name: 'Ana Silva (Filha)', color: '#F59E0B' }
      }),
      prisma.familyMember.create({
        data: { name: 'Carlos Silva (Avô)', color: '#8B5CF6' }
      })
    ]);
    console.log('👥 Created family members');

    // Create Accounts with realistic balances
    const accounts = await Promise.all([
      prisma.account.create({
        data: {
          name: 'Conta Corrente Banco do Brasil',
          type: AccountType.CHECKING,
          currency: 'BRL',
          currentBalance: 8500.75,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Poupança Caixa',
          type: AccountType.SAVINGS,
          currency: 'BRL',
          currentBalance: 25000.00,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Cartão Nubank',
          type: AccountType.CREDIT_CARD,
          currency: 'BRL',
          currentBalance: -1250.30,
          creditLimit: 5000.00,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Cartão Itaú',
          type: AccountType.CREDIT_CARD,
          currency: 'BRL',
          currentBalance: -850.00,
          creditLimit: 3000.00,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Conta USD',
          type: AccountType.CHECKING,
          currency: 'USD',
          currentBalance: 2500.00,
          exchangeRate: 5.20,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Investimentos XP',
          type: AccountType.INVESTMENT,
          currency: 'BRL',
          currentBalance: 45000.00,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Apartamento',
          type: AccountType.ASSETS,
          currency: 'BRL',
          currentBalance: 350000.00,
          includeInTotal: false
        }
      }),
      prisma.account.create({
        data: {
          name: 'Dinheiro',
          type: AccountType.CASH,
          currency: 'BRL',
          currentBalance: 450.00,
          includeInTotal: true
        }
      })
    ]);
    console.log('🏦 Created accounts');

    // Associate accounts with family members
    await Promise.all([
      prisma.accountMember.create({
        data: { accountId: accounts[0].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[0].id, familyMemberId: familyMembers[1].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[1].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[2].id, familyMemberId: familyMembers[1].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[3].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[5].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[5].id, familyMemberId: familyMembers[1].id }
      })
    ]);

    // Create Categories with hierarchy
    const parentCategories = await Promise.all([
      prisma.category.create({
        data: { name: 'Alimentação', color: '#EF4444' }
      }),
      prisma.category.create({
        data: { name: 'Transporte', color: '#3B82F6' }
      }),
      prisma.category.create({
        data: { name: 'Moradia', color: '#10B981' }
      }),
      prisma.category.create({
        data: { name: 'Saúde', color: '#F59E0B' }
      }),
      prisma.category.create({
        data: { name: 'Educação', color: '#8B5CF6' }
      }),
      prisma.category.create({
        data: { name: 'Lazer', color: '#EC4899' }
      }),
      prisma.category.create({
        data: { name: 'Receitas', color: '#10B981' }
      }),
      prisma.category.create({
        data: { name: 'Investimentos', color: '#6366F1' }
      }),
      prisma.category.create({
        data: { name: 'Impostos', color: '#DC2626' }
      }),
      prisma.category.create({
        data: { name: 'Seguros', color: '#059669' }
      }),
      prisma.category.create({
        data: { name: 'Vestuário', color: '#F97316' }
      }),
      prisma.category.create({
        data: { name: 'Tecnologia', color: '#6B7280' }
      })
    ]);

    // Create subcategories
    const subcategories = await Promise.all([
      // Alimentação subcategories
      prisma.category.create({
        data: { name: 'Supermercado', color: '#FCA5A5', parentId: parentCategories[0].id }
      }),
      prisma.category.create({
        data: { name: 'Restaurantes', color: '#F87171', parentId: parentCategories[0].id }
      }),
      prisma.category.create({
        data: { name: 'Delivery', color: '#EF4444', parentId: parentCategories[0].id }
      }),
      // Transporte subcategories
      prisma.category.create({
        data: { name: 'Combustível', color: '#93C5FD', parentId: parentCategories[1].id }
      }),
      prisma.category.create({
        data: { name: 'Uber/Taxi', color: '#60A5FA', parentId: parentCategories[1].id }
      }),
      prisma.category.create({
        data: { name: 'Transporte Público', color: '#3B82F6', parentId: parentCategories[1].id }
      }),
      // Moradia subcategories
      prisma.category.create({
        data: { name: 'Aluguel', color: '#86EFAC', parentId: parentCategories[2].id }
      }),
      prisma.category.create({
        data: { name: 'Condomínio', color: '#4ADE80', parentId: parentCategories[2].id }
      }),
      prisma.category.create({
        data: { name: 'Energia Elétrica', color: '#22C55E', parentId: parentCategories[2].id }
      }),
      prisma.category.create({
        data: { name: 'Água', color: '#16A34A', parentId: parentCategories[2].id }
      }),
      // Receitas subcategories
      prisma.category.create({
        data: { name: 'Salário', color: '#86EFAC', parentId: parentCategories[6].id }
      }),
      prisma.category.create({
        data: { name: 'Freelance', color: '#4ADE80', parentId: parentCategories[6].id }
      }),
      prisma.category.create({
        data: { name: 'Rendimentos', color: '#22C55E', parentId: parentCategories[6].id }
      })
    ]);

    const categories = [...parentCategories, ...subcategories];
    console.log('📂 Created categories and subcategories');

    // Create Tags
    const tags = await Promise.all([
      prisma.tag.create({
        data: { name: 'viagem2024', color: '#8B5CF6' }
      }),
      prisma.tag.create({
        data: { name: 'trabalho', color: '#3B82F6' }
      }),
      prisma.tag.create({
        data: { name: 'emergencia', color: '#EF4444' }
      }),
      prisma.tag.create({
        data: { name: 'casa', color: '#10B981' }
      }),
      prisma.tag.create({
        data: { name: 'saude', color: '#F59E0B' }
      }),
      prisma.tag.create({
        data: { name: 'educacao', color: '#8B5CF6' }
      }),
      prisma.tag.create({
        data: { name: 'lazer', color: '#EC4899' }
      }),
      prisma.tag.create({
        data: { name: 'investimento', color: '#6366F1' }
      }),
      prisma.tag.create({
        data: { name: 'natal2024', color: '#DC2626' }
      }),
      prisma.tag.create({
        data: { name: 'ferias', color: '#059669' }
      })
    ]);
    console.log('🏷️  Created tags');

    // Generate comprehensive historical transactions
    console.log('💰 Generating historical transactions...');

    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-12-31');
    const transactions = [];

    // Helper function to create transaction with installments
    async function createTransactionWithInstallments(transactionData: any) {
      const installmentsData = [];
      const installmentAmount = transactionData.totalAmount / transactionData.totalInstallments;

      for (let i = 1; i <= transactionData.totalInstallments; i++) {
        const dueDate = new Date(transactionData.transactionDate);
        dueDate.setMonth(dueDate.getMonth() + (i - 1));

        const isPaid = dueDate <= new Date();

        installmentsData.push({
          installmentNumber: i,
          amount: i === transactionData.totalInstallments
            ? transactionData.totalAmount - (installmentAmount * (i - 1)) // Adjust last installment for rounding
            : installmentAmount,
          dueDate,
          isPaid,
          paidAt: isPaid ? dueDate : null,
          description: `${transactionData.description} - Parcela ${i}/${transactionData.totalInstallments}`
        });
      }

      return await prisma.transaction.create({
        data: {
          ...transactionData,
          installments: {
            create: installmentsData
          }
        }
      });
    }

    // Monthly salaries
    for (let month = 0; month < 12; month++) {
      const salaryDate = new Date(2024, month, 5);
      const transaction = await createTransactionWithInstallments({
        description: `Salário ${salaryDate.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`,
        totalAmount: getRandomAmount(4800, 5200),
        totalInstallments: 1,
        transactionDate: salaryDate,
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[10].id, // Salário
        tags: {
          create: [{ tagId: tags[1].id }] // trabalho
        },
        members: {
          create: [{ familyMemberId: familyMembers[0].id }]
        }
      });
      transactions.push(transaction);
    }

    // Monthly rent
    for (let month = 0; month < 12; month++) {
      const rentDate = new Date(2024, month, 10);
      const transaction = await createTransactionWithInstallments({
        description: `Aluguel ${rentDate.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`,
        totalAmount: 1200.00,
        totalInstallments: 1,
        transactionDate: rentDate,
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: subcategories[6].id, // Aluguel
        tags: {
          create: [{ tagId: tags[3].id }] // casa
        }
      });
      transactions.push(transaction);
    }

    console.log('💰 Created basic recurring transactions');

    // Create installment purchases (parceled transactions)
    const installmentTransactions = [
      {
        description: 'Notebook Dell',
        totalAmount: 3600.00,
        totalInstallments: 12,
        transactionDate: new Date('2024-03-15'),
        type: TransactionType.EXPENSE,
        accountId: accounts[2].id, // Credit card
        categoryId: parentCategories[11].id, // Tecnologia
        tags: [tags[1].id], // trabalho
        members: [familyMembers[0].id]
      },
      {
        description: 'Geladeira Brastemp',
        totalAmount: 2400.00,
        totalInstallments: 8,
        transactionDate: new Date('2024-05-20'),
        type: TransactionType.EXPENSE,
        accountId: accounts[3].id, // Credit card Itaú
        categoryId: parentCategories[2].id, // Moradia
        tags: [tags[3].id], // casa
        members: [familyMembers[0].id, familyMembers[1].id]
      },
      {
        description: 'Curso de Inglês',
        totalAmount: 1800.00,
        totalInstallments: 6,
        transactionDate: new Date('2024-07-10'),
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[4].id, // Educação
        tags: [tags[5].id], // educacao
        members: [familyMembers[2].id]
      }
    ];

    for (const txData of installmentTransactions) {
      const transaction = await createTransactionWithInstallments({
        description: txData.description,
        totalAmount: txData.totalAmount,
        totalInstallments: txData.totalInstallments,
        transactionDate: txData.transactionDate,
        type: txData.type,
        accountId: txData.accountId,
        categoryId: txData.categoryId,
        tags: {
          create: txData.tags.map(tagId => ({ tagId }))
        },
        members: {
          create: txData.members.map(memberId => ({ familyMemberId: memberId }))
        }
      });
      transactions.push(transaction);
    }

    console.log('💳 Created installment transactions');

    // Generate random daily transactions
    const randomTransactions = [
      // Grocery shopping
      { desc: 'Supermercado Extra', amount: [150, 300], category: subcategories[0].id, account: accounts[0].id, type: TransactionType.EXPENSE, tags: [tags[3].id] },
      { desc: 'Padaria do João', amount: [15, 45], category: subcategories[0].id, account: accounts[7].id, type: TransactionType.EXPENSE, tags: [] },
      { desc: 'Feira livre', amount: [30, 80], category: subcategories[0].id, account: accounts[7].id, type: TransactionType.EXPENSE, tags: [] },

      // Restaurants and food
      { desc: 'Restaurante Italiano', amount: [80, 150], category: subcategories[1].id, account: accounts[2].id, type: TransactionType.EXPENSE, tags: [tags[6].id] },
      { desc: 'McDonald\'s', amount: [25, 50], category: subcategories[2].id, account: accounts[0].id, type: TransactionType.EXPENSE, tags: [] },
      { desc: 'iFood - Pizza', amount: [40, 70], category: subcategories[2].id, account: accounts[2].id, type: TransactionType.EXPENSE, tags: [] },

      // Transportation
      { desc: 'Posto Shell', amount: [80, 120], category: subcategories[3].id, account: accounts[0].id, type: TransactionType.EXPENSE, tags: [] },
      { desc: 'Uber', amount: [15, 35], category: subcategories[4].id, account: accounts[2].id, type: TransactionType.EXPENSE, tags: [] },
      { desc: 'Metrô', amount: [4, 8], category: subcategories[5].id, account: accounts[7].id, type: TransactionType.EXPENSE, tags: [] },

      // Health
      { desc: 'Farmácia Droga Raia', amount: [25, 80], category: parentCategories[3].id, account: accounts[0].id, type: TransactionType.EXPENSE, tags: [tags[4].id] },
      { desc: 'Consulta médica', amount: [150, 300], category: parentCategories[3].id, account: accounts[0].id, type: TransactionType.EXPENSE, tags: [tags[4].id] },

      // Entertainment
      { desc: 'Cinema', amount: [30, 60], category: parentCategories[5].id, account: accounts[2].id, type: TransactionType.EXPENSE, tags: [tags[6].id] },
      { desc: 'Netflix', amount: [25, 35], category: parentCategories[5].id, account: accounts[2].id, type: TransactionType.EXPENSE, tags: [] },

      // Freelance income
      { desc: 'Freelance - Design', amount: [500, 1500], category: subcategories[11].id, account: accounts[0].id, type: TransactionType.INCOME, tags: [tags[1].id] },
      { desc: 'Consultoria TI', amount: [800, 2000], category: subcategories[11].id, account: accounts[0].id, type: TransactionType.INCOME, tags: [tags[1].id] },

      // Investments
      { desc: 'Aplicação CDB', amount: [1000, 3000], category: parentCategories[7].id, account: accounts[5].id, type: TransactionType.INCOME, tags: [tags[7].id] },
      { desc: 'Dividendos', amount: [200, 800], category: subcategories[12].id, account: accounts[5].id, type: TransactionType.INCOME, tags: [tags[7].id] }
    ];

    // Generate 150 random transactions over the year
    for (let i = 0; i < 150; i++) {
      const randomTx = getRandomElement(randomTransactions);
      const randomDate = getRandomDate(startDate, endDate);
      const amount = getRandomAmount(randomTx.amount[0], randomTx.amount[1]);

      const transaction = await createTransactionWithInstallments({
        description: randomTx.desc,
        totalAmount: amount,
        totalInstallments: 1,
        transactionDate: randomDate,
        type: randomTx.type,
        accountId: randomTx.account,
        categoryId: randomTx.category,
        tags: {
          create: randomTx.tags.map(tagId => ({ tagId }))
        },
        members: {
          create: [{ familyMemberId: getRandomElement(familyMembers).id }]
        }
      });
      transactions.push(transaction);
    }

    console.log('🎲 Created random transactions');

    // ========================================
    // FUTURE TRANSACTIONS - COMPREHENSIVE SEED
    // ========================================
    console.log('🔮 Creating comprehensive future transactions...');

    const today = new Date();
    const futureTransactions = [];

    // Helper function to create future transaction with installments
    async function createFutureTransactionWithInstallments(transactionData: any) {
      const installmentsData = [];
      const installmentAmount = transactionData.totalAmount / transactionData.totalInstallments;

      for (let i = 1; i <= transactionData.totalInstallments; i++) {
        const dueDate = new Date(transactionData.transactionDate);
        dueDate.setMonth(dueDate.getMonth() + (i - 1));

        // For future transactions, determine isPaid based on due date
        const isPaid = dueDate <= today;

        installmentsData.push({
          installmentNumber: i,
          amount: i === transactionData.totalInstallments
            ? transactionData.totalAmount - (installmentAmount * (i - 1)) // Adjust last installment for rounding
            : installmentAmount,
          dueDate,
          isPaid,
          paidAt: isPaid ? dueDate : null,
          description: `${transactionData.description} - Parcela ${i}/${transactionData.totalInstallments}`
        });
      }

      return await prisma.transaction.create({
        data: {
          ...transactionData,
          isFuture: true, // Mark as future transaction
          installments: {
            create: installmentsData
          }
        }
      });
    }

    // 1. OVERDUE FUTURE TRANSACTIONS (vencidas)
    const overdueTransactions = [
      {
        description: 'Pagamento Cartão Vencido',
        totalAmount: 850.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[8].id, // Impostos
        tags: { create: [{ tagId: tags[2].id }] }, // emergencia
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Freelance Atrasado',
        totalAmount: 1200.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[11].id, // Freelance
        tags: { create: [{ tagId: tags[1].id }] }, // trabalho
        members: { create: [{ familyMemberId: familyMembers[1].id }] }
      },
      {
        description: 'Conta de Luz Vencida',
        totalAmount: 180.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: subcategories[8].id, // Energia Elétrica
        tags: { create: [{ tagId: tags[3].id }] }, // casa
      }
    ];

    for (const txData of overdueTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    // 2. DUE TODAY (vencendo hoje)
    const dueTodayTransactions = [
      {
        description: 'Salário Hoje',
        totalAmount: 5000.00,
        totalInstallments: 1,
        transactionDate: today,
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[10].id, // Salário
        tags: { create: [{ tagId: tags[1].id }] }, // trabalho
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Pagamento Cartão Hoje',
        totalAmount: 650.00,
        totalInstallments: 1,
        transactionDate: today,
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[8].id, // Impostos
        tags: { create: [{ tagId: tags[2].id }] }, // emergencia
        members: { create: [{ familyMemberId: familyMembers[1].id }] }
      }
    ];

    for (const txData of dueTodayTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    // 3. DUE THIS WEEK (vencendo esta semana)
    const dueThisWeekTransactions = [
      {
        description: 'Consulta Médica Agendada',
        totalAmount: 250.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[3].id, // Saúde
        tags: { create: [{ tagId: tags[4].id }] }, // saude
        members: { create: [{ familyMemberId: familyMembers[2].id }] }
      },
      {
        description: 'Recebimento Dividendos',
        totalAmount: 450.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
        type: TransactionType.INCOME,
        accountId: accounts[5].id, // Investimentos
        categoryId: subcategories[12].id, // Rendimentos
        tags: { create: [{ tagId: tags[7].id }] }, // investimento
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Compra Supermercado Agendada',
        totalAmount: 320.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 6 * 24 * 60 * 60 * 1000), // 6 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: subcategories[0].id, // Supermercado
        tags: { create: [{ tagId: tags[3].id }] }, // casa
      }
    ];

    for (const txData of dueThisWeekTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    console.log('🔮 Created future transactions (overdue, today, this week)');

    // 4. DUE THIS MONTH (vencendo este mês)
    const dueThisMonthTransactions = [
      {
        description: 'Aluguel Janeiro 2025',
        totalAmount: 1200.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: subcategories[6].id, // Aluguel
        tags: { create: [{ tagId: tags[3].id }] }, // casa
      },
      {
        description: 'Bonus Trabalho',
        totalAmount: 2500.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[10].id, // Salário
        tags: { create: [{ tagId: tags[1].id }] }, // trabalho
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Seguro Carro',
        totalAmount: 380.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 20 * 24 * 60 * 60 * 1000), // 20 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[9].id, // Seguros
        tags: { create: [{ tagId: tags[1].id }] }, // trabalho
      },
      {
        description: 'Investimento Programado',
        totalAmount: 1000.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 25 * 24 * 60 * 60 * 1000), // 25 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[7].id, // Investimentos
        tags: { create: [{ tagId: tags[7].id }] }, // investimento
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      }
    ];

    for (const txData of dueThisMonthTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    // 5. FUTURE TRANSACTIONS (mais de 1 mês)
    const longTermFutureTransactions = [
      {
        description: 'Viagem Férias 2025',
        totalAmount: 4500.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[1].id, // Poupança
        categoryId: parentCategories[5].id, // Lazer
        tags: { create: [{ tagId: tags[9].id }] }, // ferias
        members: {
          create: [
            { familyMemberId: familyMembers[0].id },
            { familyMemberId: familyMembers[1].id }
          ]
        }
      },
      {
        description: 'Matrícula Escola 2025',
        totalAmount: 1800.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[4].id, // Educação
        tags: { create: [{ tagId: tags[5].id }] }, // educacao
        members: { create: [{ familyMemberId: familyMembers[2].id }] }
      },
      {
        description: 'Rendimento Poupança',
        totalAmount: 125.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 35 * 24 * 60 * 60 * 1000), // 35 days from now
        type: TransactionType.INCOME,
        accountId: accounts[1].id, // Poupança
        categoryId: subcategories[12].id, // Rendimentos
        tags: { create: [{ tagId: tags[7].id }] }, // investimento
      },
      {
        description: 'Reforma Casa',
        totalAmount: 8500.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        type: TransactionType.EXPENSE,
        accountId: accounts[1].id, // Poupança
        categoryId: parentCategories[2].id, // Moradia
        tags: { create: [{ tagId: tags[3].id }] }, // casa
        members: {
          create: [
            { familyMemberId: familyMembers[0].id },
            { familyMemberId: familyMembers[1].id }
          ]
        }
      }
    ];

    for (const txData of longTermFutureTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    console.log('🔮 Created future transactions (this month, long term)');

    // 6. INSTALLMENT TRANSACTIONS WITH FUTURE PAYMENTS (parcelas futuras)
    const installmentFutureTransactions = [
      {
        description: 'Smartphone Parcelado',
        totalAmount: 2400.00,
        totalInstallments: 12,
        transactionDate: new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000), // Started 3 months ago
        type: TransactionType.EXPENSE,
        accountId: accounts[2].id, // Credit card
        categoryId: parentCategories[11].id, // Tecnologia
        tags: { create: [{ tagId: tags[1].id }] }, // trabalho
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Curso Online Parcelado',
        totalAmount: 1200.00,
        totalInstallments: 6,
        transactionDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000), // Started 1 month ago
        type: TransactionType.EXPENSE,
        accountId: accounts[3].id, // Credit card Itaú
        categoryId: parentCategories[4].id, // Educação
        tags: { create: [{ tagId: tags[5].id }] }, // educacao
        members: { create: [{ familyMemberId: familyMembers[1].id }] }
      },
      {
        description: 'Móveis Casa Parcelado',
        totalAmount: 3600.00,
        totalInstallments: 18,
        transactionDate: new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000), // Started 6 months ago
        type: TransactionType.EXPENSE,
        accountId: accounts[2].id, // Credit card
        categoryId: parentCategories[2].id, // Moradia
        tags: { create: [{ tagId: tags[3].id }] }, // casa
        members: {
          create: [
            { familyMemberId: familyMembers[0].id },
            { familyMemberId: familyMembers[1].id }
          ]
        }
      },
      {
        description: 'Plano de Saúde Anual',
        totalAmount: 2400.00,
        totalInstallments: 12,
        transactionDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000), // Starts in 5 days
        type: TransactionType.EXPENSE,
        accountId: accounts[0].id,
        categoryId: parentCategories[3].id, // Saúde
        tags: { create: [{ tagId: tags[4].id }] }, // saude
        members: {
          create: [
            { familyMemberId: familyMembers[0].id },
            { familyMemberId: familyMembers[1].id },
            { familyMemberId: familyMembers[2].id }
          ]
        }
      }
    ];

    for (const txData of installmentFutureTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    // 7. RECURRING INCOME TRANSACTIONS (receitas recorrentes futuras)
    const recurringIncomeTransactions = [
      {
        description: 'Aluguel Recebido Apartamento',
        totalAmount: 800.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[12].id, // Rendimentos
        tags: { create: [{ tagId: tags[7].id }] }, // investimento
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Pensão Alimentícia',
        totalAmount: 600.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
        type: TransactionType.INCOME,
        accountId: accounts[0].id,
        categoryId: subcategories[10].id, // Salário (categoria similar)
        members: { create: [{ familyMemberId: familyMembers[2].id }] }
      }
    ];

    for (const txData of recurringIncomeTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    // 8. TRANSFER TRANSACTIONS (transferências futuras)
    const futureTransferTransactions = [
      {
        description: 'Transferência para Poupança',
        totalAmount: 1500.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        type: TransactionType.TRANSFER,
        accountId: accounts[0].id, // From checking
        destinationAccountId: accounts[1].id, // To savings
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      },
      {
        description: 'Transferência para Investimentos',
        totalAmount: 2000.00,
        totalInstallments: 1,
        transactionDate: new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        type: TransactionType.TRANSFER,
        accountId: accounts[1].id, // From savings
        destinationAccountId: accounts[5].id, // To investments
        members: { create: [{ familyMemberId: familyMembers[0].id }] }
      }
    ];

    for (const txData of futureTransferTransactions) {
      const transaction = await createFutureTransactionWithInstallments(txData);
      futureTransactions.push(transaction);
    }

    console.log(`🔮 Created comprehensive future transactions: ${futureTransactions.length} total`);

    // Create Goals with diverse scenarios
    const goals = await Promise.all([
      prisma.goal.create({
        data: {
          name: 'Viagem para Europa',
          targetAmount: 15000.00,
          currentAmount: 2500.00,
          targetDate: new Date('2025-06-01'),
          goalType: GoalType.ACCUMULATION,
          milestones: {
            create: [
              {
                name: 'Primeira etapa - Passagens',
                targetAmount: 5000.00,
                targetDate: new Date('2025-02-01')
              },
              {
                name: 'Segunda etapa - Hospedagem',
                targetAmount: 10000.00,
                targetDate: new Date('2025-04-01')
              }
            ]
          },
          members: {
            create: [
              { familyMemberId: familyMembers[0].id },
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Reserva de Emergência',
          targetAmount: 30000.00,
          currentAmount: 8500.00,
          targetDate: new Date('2025-12-31'),
          goalType: GoalType.ACCUMULATION,
          members: {
            create: [
              { familyMemberId: familyMembers[0].id },
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Carro Novo',
          targetAmount: 45000.00,
          currentAmount: 12000.00,
          targetDate: new Date('2025-08-01'),
          goalType: GoalType.ACCUMULATION,
          members: {
            create: [
              { familyMemberId: familyMembers[0].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Reduzir Dívida Cartão',
          targetAmount: 5000.00,
          currentAmount: 2100.00,
          initialAmount: 5000.00,
          targetDate: new Date('2025-03-01'),
          goalType: GoalType.REDUCTION,
          members: {
            create: [
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Curso de MBA',
          targetAmount: 8000.00,
          currentAmount: 1200.00,
          targetDate: new Date('2025-07-01'),
          goalType: GoalType.ACCUMULATION,
          members: {
            create: [
              { familyMemberId: familyMembers[0].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Casa Própria - Entrada',
          targetAmount: 80000.00,
          currentAmount: 15000.00,
          targetDate: new Date('2026-12-31'),
          goalType: GoalType.ACCUMULATION,
          members: {
            create: [
              { familyMemberId: familyMembers[0].id },
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      // ADDITIONAL REDUCTION GOALS
      prisma.goal.create({
        data: {
          name: 'Reduzir Gastos com Delivery',
          targetAmount: 300.00, // Meta: reduzir para R$ 300/mês
          currentAmount: 650.00, // Gasto atual: R$ 650/mês
          initialAmount: 800.00, // Gasto inicial: R$ 800/mês
          targetDate: new Date('2025-04-01'),
          goalType: GoalType.REDUCTION,
          milestones: {
            create: [
              {
                name: 'Primeira redução - 50%',
                targetAmount: 600.00, // Reduzir para R$ 600/mês
                targetDate: new Date('2025-02-01')
              },
              {
                name: 'Meta final - 62.5%',
                targetAmount: 300.00, // Meta final: R$ 300/mês
                targetDate: new Date('2025-04-01')
              }
            ]
          },
          members: {
            create: [
              { familyMemberId: familyMembers[0].id },
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Reduzir Financiamento Carro',
          targetAmount: 0.00, // Meta: quitar completamente
          currentAmount: 18500.00, // Saldo devedor atual
          initialAmount: 25000.00, // Valor inicial do financiamento
          targetDate: new Date('2025-10-01'),
          goalType: GoalType.REDUCTION,
          milestones: {
            create: [
              {
                name: 'Redução 50%',
                targetAmount: 12500.00, // Reduzir para R$ 12.500
                targetDate: new Date('2025-05-01')
              },
              {
                name: 'Redução 80%',
                targetAmount: 5000.00, // Reduzir para R$ 5.000
                targetDate: new Date('2025-08-01')
              },
              {
                name: 'Quitação total',
                targetAmount: 0.00, // Quitar completamente
                targetDate: new Date('2025-10-01')
              }
            ]
          },
          members: {
            create: [
              { familyMemberId: familyMembers[0].id }
            ]
          }
        }
      })
    ]);
    console.log('🎯 Created goals with milestones');

    // Create comprehensive budgets for current month and next months
    const budgetData = [];

    // Current month budgets (December 2024)
    const currentMonthBudgets = [
      { categoryId: parentCategories[0].id, amount: 800.00, memberId: null }, // Alimentação
      { categoryId: parentCategories[1].id, amount: 400.00, memberId: familyMembers[0].id }, // Transporte - João
      { categoryId: parentCategories[2].id, amount: 1500.00, memberId: null }, // Moradia
      { categoryId: parentCategories[3].id, amount: 300.00, memberId: null }, // Saúde
      { categoryId: parentCategories[4].id, amount: 200.00, memberId: familyMembers[2].id }, // Educação - Pedro
      { categoryId: parentCategories[5].id, amount: 500.00, memberId: null }, // Lazer
      { categoryId: parentCategories[10].id, amount: 300.00, memberId: familyMembers[1].id }, // Vestuário - Maria
      { categoryId: subcategories[0].id, amount: 600.00, memberId: null }, // Supermercado
      { categoryId: subcategories[1].id, amount: 200.00, memberId: null }, // Restaurantes
      { categoryId: subcategories[3].id, amount: 250.00, memberId: familyMembers[0].id }, // Combustível - João
      { categoryId: subcategories[6].id, amount: 1200.00, memberId: null }, // Aluguel
      { categoryId: subcategories[7].id, amount: 150.00, memberId: null }, // Condomínio
      { categoryId: subcategories[8].id, amount: 200.00, memberId: null }, // Energia Elétrica
      { categoryId: subcategories[9].id, amount: 80.00, memberId: null }, // Água
    ];

    // Create budgets for December 2024, January 2025, and February 2025
    for (const monthOffset of [0, 1, 2]) {
      const targetDate = new Date(2024, 11 + monthOffset, 1); // December 2024 + offset
      const month = targetDate.getMonth() + 1;
      const year = targetDate.getFullYear();

      for (const budget of currentMonthBudgets) {
        budgetData.push({
          plannedAmount: budget.amount,
          month,
          year,
          categoryId: budget.categoryId,
          familyMemberId: budget.memberId
        });
      }
    }

    await Promise.all(
      budgetData.map(budget =>
        prisma.budget.create({ data: budget })
      )
    );
    console.log('📊 Created comprehensive budgets');

    // Create comprehensive recurring transactions
    const recurringTransactions = await Promise.all([
      prisma.recurringTransaction.create({
        data: {
          description: 'Salário mensal - João',
          fixedAmount: 5000.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.INCOME,
          accountId: accounts[0].id,
          categoryId: subcategories[10].id, // Salário
          userId: user.id,
          members: {
            create: [{ familyMemberId: familyMembers[0].id }]
          }
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Freelance - Maria',
          fixedAmount: 1500.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.INCOME,
          accountId: accounts[0].id,
          categoryId: subcategories[11].id, // Freelance
          userId: user.id,
          members: {
            create: [{ familyMemberId: familyMembers[1].id }]
          }
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Aluguel',
          fixedAmount: 1200.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: subcategories[6].id, // Aluguel
          userId: user.id
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Condomínio',
          fixedAmount: 150.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: subcategories[7].id, // Condomínio
          userId: user.id
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Netflix',
          fixedAmount: 32.90,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[2].id,
          categoryId: parentCategories[5].id, // Lazer
          userId: user.id
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Spotify',
          fixedAmount: 19.90,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[2].id,
          categoryId: parentCategories[5].id, // Lazer
          userId: user.id
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Academia',
          fixedAmount: 89.90,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: parentCategories[3].id, // Saúde
          userId: user.id,
          members: {
            create: [
              { familyMemberId: familyMembers[0].id },
              { familyMemberId: familyMembers[1].id }
            ]
          }
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Seguro do Carro',
          fixedAmount: 180.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: parentCategories[9].id, // Seguros
          userId: user.id
        }
      })
    ]);
    console.log('🔄 Created recurring transactions');

    // Create comprehensive Account Balance History (last 90 days)
    const balanceHistoryData = [];
    const todayForHistory = new Date();

    for (let i = 90; i >= 0; i--) {
      const date = new Date(todayForHistory);
      date.setDate(date.getDate() - i);

      // Generate realistic balance progression for each account
      for (let j = 0; j < accounts.length; j++) {
        const account = accounts[j];
        let baseBalance = Number(account.currentBalance) || 0;

        // Add some realistic variation
        const variation = (Math.random() - 0.5) * 200; // ±100
        const progressionFactor = (90 - i) / 90; // Gradual progression

        let balance;
        if (account.type === 'CREDIT_CARD') {
          balance = baseBalance + (variation * 0.5); // Less variation for credit cards
        } else if (account.type === 'SAVINGS') {
          balance = baseBalance * (0.8 + progressionFactor * 0.2) + variation; // Gradual growth
        } else {
          balance = baseBalance * (0.9 + progressionFactor * 0.1) + variation;
        }

        balanceHistoryData.push({
          accountId: account.id,
          balance: Math.round(balance * 100) / 100,
          balanceDate: date
        });
      }
    }

    await Promise.all(
      balanceHistoryData.map(data =>
        prisma.accountBalanceHistory.create({ data })
      )
    );
    console.log('📈 Created comprehensive balance history');

    // Create Goal Progress History
    const goalProgressData = [];

    for (const goal of goals) {
      // Create 5-10 progress entries for each goal
      const progressEntries = Math.floor(Math.random() * 6) + 5;
      let currentAmount = 0;

      for (let i = 0; i < progressEntries; i++) {
        const previousAmount = currentAmount;
        const goalCurrentAmount = Number(goal.currentAmount);
        const increment = goalCurrentAmount / progressEntries;
        currentAmount += increment + (Math.random() - 0.5) * increment * 0.3;

        if (i === progressEntries - 1) {
          currentAmount = goalCurrentAmount; // Ensure final amount matches
        }

        const progressDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);

        goalProgressData.push({
          goalId: goal.id,
          previousAmount: Math.round(previousAmount * 100) / 100,
          newAmount: Math.round(currentAmount * 100) / 100,
          amountChanged: Math.round((currentAmount - previousAmount) * 100) / 100,
          operation: 'add',
          description: `Progresso registrado em ${progressDate.toLocaleDateString('pt-BR')}`,
          createdAt: progressDate
        });
      }
    }

    await Promise.all(
      goalProgressData.map(data =>
        prisma.goalProgressHistory.create({ data })
      )
    );
    console.log('🎯 Created goal progress history');

    // Create Insights for analysis and recommendations
    const insights = await Promise.all([
      prisma.insight.create({
        data: {
          type: InsightType.SPENDING_PATTERN,
          priority: InsightPriority.MEDIUM,
          status: InsightStatus.NEW,
          title: 'Aumento nos gastos com alimentação',
          description: 'Seus gastos com alimentação aumentaram 15% em relação ao mês anterior. Considere revisar o orçamento desta categoria.',
          data: {
            category: 'Alimentação',
            currentMonth: 850.00,
            previousMonth: 740.00,
            increase: 14.86
          },
          categoryId: parentCategories[0].id,
          periodStart: new Date('2024-11-01'),
          periodEnd: new Date('2024-11-30'),
          recommendations: {
            actions: [
              'Revisar gastos com delivery',
              'Planejar refeições semanalmente',
              'Comparar preços em diferentes supermercados'
            ]
          },
          relevanceScore: 0.75
        }
      }),
      prisma.insight.create({
        data: {
          type: InsightType.BUDGET_ALERT,
          priority: InsightPriority.HIGH,
          status: InsightStatus.NEW,
          title: 'Orçamento de transporte quase esgotado',
          description: 'Você já utilizou 90% do orçamento de transporte deste mês. Restam apenas R$ 40,00.',
          data: {
            category: 'Transporte',
            budgeted: 400.00,
            spent: 360.00,
            remaining: 40.00,
            percentage: 90
          },
          categoryId: parentCategories[1].id,
          periodStart: new Date('2024-12-01'),
          periodEnd: new Date('2024-12-31'),
          recommendations: {
            actions: [
              'Considere usar transporte público',
              'Avalie caronas compartilhadas',
              'Revise o orçamento para o próximo mês'
            ]
          },
          relevanceScore: 0.95
        }
      }),
      prisma.insight.create({
        data: {
          type: InsightType.GOAL_PROGRESS,
          priority: InsightPriority.MEDIUM,
          status: InsightStatus.NEW,
          title: 'Meta de viagem no cronograma',
          description: 'Sua meta de viagem para Europa está 16.7% concluída. Você está no cronograma para atingir o objetivo.',
          data: {
            goalName: 'Viagem para Europa',
            targetAmount: 15000.00,
            currentAmount: 2500.00,
            percentage: 16.7,
            monthsRemaining: 6
          },
          goalId: goals[0].id,
          recommendations: {
            actions: [
              'Continue com os aportes mensais',
              'Considere uma aplicação de renda fixa',
              'Monitore promoções de passagens'
            ]
          },
          relevanceScore: 0.80
        }
      })
    ]);

    // Create additional insights
    const moreInsights = await Promise.all([
      prisma.insight.create({
        data: {
          type: InsightType.SAVINGS_OPPORTUNITY,
          priority: InsightPriority.MEDIUM,
          status: InsightStatus.NEW,
          title: 'Oportunidade de economia em assinaturas',
          description: 'Você tem múltiplas assinaturas de streaming. Considere cancelar serviços não utilizados.',
          data: {
            totalSubscriptions: 3,
            monthlyTotal: 87.70,
            yearlyTotal: 1052.40,
            unusedServices: ['Amazon Prime', 'Disney+']
          },
          recommendations: {
            actions: [
              'Revisar uso de cada serviço',
              'Cancelar assinaturas não utilizadas',
              'Considerar planos familiares'
            ]
          },
          relevanceScore: 0.70
        }
      }),
      prisma.insight.create({
        data: {
          type: InsightType.CASH_FLOW_ANALYSIS,
          priority: InsightPriority.LOW,
          status: InsightStatus.NEW,
          title: 'Fluxo de caixa positivo',
          description: 'Seu fluxo de caixa está positivo em R$ 1.250,00 este mês. Considere investir o excedente.',
          data: {
            income: 6500.00,
            expenses: 5250.00,
            surplus: 1250.00,
            savingsRate: 19.23
          },
          recommendations: {
            actions: [
              'Investir o excedente em CDB',
              'Aumentar contribuição para reserva de emergência',
              'Considerar investimentos de longo prazo'
            ]
          },
          relevanceScore: 0.85
        }
      }),
      prisma.insight.create({
        data: {
          type: InsightType.ANOMALY_DETECTION,
          priority: InsightPriority.HIGH,
          status: InsightStatus.NEW,
          title: 'Gasto incomum detectado',
          description: 'Foi detectado um gasto de R$ 850,00 em restaurantes, 300% acima da média mensal.',
          data: {
            category: 'Restaurantes',
            amount: 850.00,
            average: 200.00,
            deviation: 325.00
          },
          categoryId: subcategories[1].id,
          recommendations: {
            actions: [
              'Verificar se o gasto foi planejado',
              'Revisar gastos com alimentação fora de casa',
              'Ajustar orçamento se necessário'
            ]
          },
          relevanceScore: 0.90
        }
      }),
      prisma.insight.create({
        data: {
          type: InsightType.TREND_ANALYSIS,
          priority: InsightPriority.LOW,
          status: InsightStatus.NEW,
          title: 'Tendência de crescimento nos investimentos',
          description: 'Seus investimentos cresceram 8% nos últimos 3 meses. Continue com a estratégia atual.',
          data: {
            category: 'Investimentos',
            growth: 8.2,
            period: '3 meses',
            currentValue: 45000.00,
            previousValue: 41667.00
          },
          accountId: accounts[5].id,
          recommendations: {
            actions: [
              'Manter estratégia de investimento',
              'Considerar diversificar portfólio',
              'Revisar metas de longo prazo'
            ]
          },
          relevanceScore: 0.65
        }
      })
    ]);
    console.log('💡 Created comprehensive insights');

    console.log('\n🎉 Database seeded successfully!');
    console.log(`
📊 Summary:
- ${familyMembers.length} family members
- ${accounts.length} accounts
- ${categories.length + subcategories.length} categories
- ${tags.length} tags
- ${transactions.length + futureTransactions.length} transactions (${transactions.length} historical + ${futureTransactions.length} future)
- ${goals.length} goals
- ${budgetData.length} budgets
- ${recurringTransactions.length} recurring transactions
- ${balanceHistoryData.length} balance history records
- ${insights.length + moreInsights.length} insights

🔮 Future Transactions Breakdown:
- Overdue: ${overdueTransactions.length}
- Due today: ${dueTodayTransactions.length}
- Due this week: ${dueThisWeekTransactions.length}
- Due this month: ${dueThisMonthTransactions.length}
- Long term: ${longTermFutureTransactions.length}
- Installments: ${installmentFutureTransactions.length}
- Recurring income: ${recurringIncomeTransactions.length}
- Transfers: ${futureTransferTransactions.length}

🎯 Goals Breakdown:
- Accumulation goals: 6 (Viagem Europa, Reserva Emergência, Carro Novo, MBA, Casa Própria, etc.)
- Reduction goals: 3 (Reduzir Dívida Cartão, Reduzir Gastos Delivery, Reduzir Financiamento Carro)
- Goals with milestones: 4 (including reduction goals with progressive targets)
    `);

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seed if this file is executed directly
if (require.main === module) {
  seed()
    .then(() => {
      console.log('✅ Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seed;
