import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

async function createTestUser() {
  console.log('👤 Criando usuário de teste...')

  try {
    const hashedPassword = await bcrypt.hash('123456', 10)
    
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>u<PERSON><PERSON> Teste',
        password: hashedPassword,
        isActive: true
      }
    })

    console.log('✅ Usuário de teste criado:', user.email)
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Senha: 123456')
    
  } catch (error) {
    console.error('❌ Erro ao criar usuário de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
