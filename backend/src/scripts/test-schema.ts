import prisma from '../lib/prisma';
import { AccountType, TransactionType, RecurrenceFrequency } from '@prisma/client';

async function testSchema() {
  console.log('🧪 Testing Database Schema...\n');

  try {
    // Test 1: Create Family Member
    console.log('1. Testing FamilyMember creation...');
    const familyMember = await prisma.familyMember.create({
      data: {
        name: '<PERSON>',
        color: '#3B82F6'
      }
    });
    console.log('✅ FamilyMember created:', familyMember.name);

    // Test 2: Create Account
    console.log('\n2. Testing Account creation...');
    const account = await prisma.account.create({
      data: {
        name: 'Conta Corrente Banco do Brasil',
        type: AccountType.CHECKING,
        currency: 'BRL',
        includeInTotal: true
      }
    });
    console.log('✅ Account created:', account.name);

    // Test 3: Create Category with hierarchy
    console.log('\n3. Testing Category hierarchy...');
    const parentCategory = await prisma.category.create({
      data: {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        color: '#10B981'
      }
    });

    const subCategory = await prisma.category.create({
      data: {
        name: 'Restaurantes',
        color: '#059669',
        parentId: parentCategory.id
      }
    });
    console.log('✅ Category hierarchy created:', `${parentCategory.name} > ${subCategory.name}`);

    // Test 4: Create Tag
    console.log('\n4. Testing Tag creation...');
    const tag = await prisma.tag.create({
      data: {
        name: 'viagem2024',
        color: '#8B5CF6'
      }
    });
    console.log('✅ Tag created:', tag.name);

    // Test 5: Create Transaction with relationships
    console.log('\n5. Testing Transaction with relationships...');
    const transaction = await prisma.transaction.create({
      data: {
        description: 'Almoço no restaurante',
        totalAmount: 45.50,
        transactionDate: new Date(),
        type: TransactionType.EXPENSE,
        accountId: account.id,
        categoryId: subCategory.id,
        tags: {
          create: [
            { tagId: tag.id }
          ]
        },
        members: {
          create: [
            { familyMemberId: familyMember.id }
          ]
        }
      },
      include: {
        account: true,
        category: true,
        tags: {
          include: {
            tag: true
          }
        },
        members: {
          include: {
            familyMember: true
          }
        }
      }
    });
    console.log('✅ Transaction created with relationships:', transaction.description);

    // Test 6: Create Budget
    console.log('\n6. Testing Budget creation...');
    const budget = await prisma.budget.create({
      data: {
        plannedAmount: 800.00,
        month: 12,
        year: 2024,
        categoryId: parentCategory.id,
        familyMemberId: familyMember.id
      }
    });
    console.log('✅ Budget created:', `R$ ${budget.plannedAmount} for ${budget.month}/${budget.year}`);

    // Test 7: Create Goal with Milestone
    console.log('\n7. Testing Goal with Milestone...');
    const goal = await prisma.goal.create({
      data: {
        name: 'Viagem para Europa',
        targetAmount: 15000.00,
        currentAmount: 2500.00,
        targetDate: new Date('2025-06-01'),
        milestones: {
          create: [
            {
              name: 'Primeira etapa',
              targetAmount: 5000.00,
              targetDate: new Date('2025-02-01')
            }
          ]
        },
        members: {
          create: [
            { familyMemberId: familyMember.id }
          ]
        }
      },
      include: {
        milestones: true,
        members: {
          include: {
            familyMember: true
          }
        }
      }
    });
    console.log('✅ Goal created with milestone:', goal.name);

    // Test 8: Create Recurring Transaction
    console.log('\n8. Testing Recurring Transaction...');
    const recurringTransaction = await prisma.recurringTransaction.create({
      data: {
        description: 'Salário mensal',
        fixedAmount: 5000.00,
        frequency: RecurrenceFrequency.MONTHLY,
        startDate: new Date('2024-01-01'),
        type: TransactionType.INCOME,
        accountId: account.id,
        categoryId: parentCategory.id,
        userId: 'test-user-id'
      }
    });
    console.log('✅ Recurring Transaction created:', recurringTransaction.description);

    // Test 9: Create Account Balance History
    console.log('\n9. Testing Account Balance History...');
    const balanceHistory = await prisma.accountBalanceHistory.create({
      data: {
        accountId: account.id,
        balance: 1250.75,
        balanceDate: new Date()
      }
    });
    console.log('✅ Balance History created:', `R$ ${balanceHistory.balance}`);

    // Test 10: Test Soft Delete
    console.log('\n10. Testing Soft Delete...');
    await prisma.tag.update({
      where: { id: tag.id },
      data: { deletedAt: new Date() }
    });

    const activeTags = await prisma.tag.findMany({
      where: { deletedAt: null }
    });
    console.log('✅ Soft delete working, active tags:', activeTags.length);

    // Test 11: Complex Query with Relations
    console.log('\n11. Testing Complex Query...');
    const transactionWithDetails = await prisma.transaction.findFirst({
      where: { id: transaction.id },
      include: {
        account: true,
        category: {
          include: {
            parent: true
          }
        },
        tags: {
          include: {
            tag: true
          }
        },
        members: {
          include: {
            familyMember: true
          }
        }
      }
    });
    console.log('✅ Complex query successful:', transactionWithDetails?.description);

    console.log('\n🎉 All schema tests passed successfully!');

  } catch (error) {
    console.error('❌ Schema test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testSchema()
    .then(() => {
      console.log('\n✅ Schema validation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Schema validation failed:', error);
      process.exit(1);
    });
}

export default testSchema;
