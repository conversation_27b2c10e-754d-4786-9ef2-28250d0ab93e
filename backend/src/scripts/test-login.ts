import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

async function testLogin() {
  console.log('🔍 Testando login...')

  try {
    // Buscar usuário
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!user) {
      console.log('❌ Usuário não encontrado')
      return
    }

    console.log('✅ Usuário encontrado:', user.email)
    console.log('🔑 Hash da senha:', user.password)

    // Testar senha
    const isValid = await bcrypt.compare('123456', user.password)
    console.log('🔐 Senha válida:', isValid)

    if (!isValid) {
      console.log('🔧 Atualizando senha...')
      const newHash = await bcrypt.hash('123456', 10)
      await prisma.user.update({
        where: { id: user.id },
        data: { password: newHash }
      })
      console.log('✅ Senha atualizada')
    }
    
  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testLogin()
