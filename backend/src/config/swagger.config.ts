import { Express } from 'express';

// Especificação OpenAPI 3.0.0 completa
const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Personal Finance Manager API',
    version: '1.0.0',
    description: 'API completa para gerenciamento de finanças pessoais e familiares',
    contact: {
      name: '<PERSON><PERSON><PERSON> de Desenvolvimento',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: process.env.API_BASE_URL || 'http://localhost:3001/api/v1',
      description: 'Servidor de desenvolvimento'
    },
    {
      url: 'https://api.personalfinance.com/api/v1',
      description: 'Servidor de produção'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Token JWT obtido através do endpoint de autenticação'
      }
    },
    schemas: {
      // Schemas de erro padrão
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                description: 'Mensagem de erro em português'
              },
              code: {
                type: 'string',
                description: 'Código específico do erro'
              },
              details: {
                type: 'object',
                description: 'Detalhes adicionais do erro (opcional)'
              }
            },
            required: ['message', 'code']
          }
        },
        required: ['success', 'error']
      },
      
      // Schema de resposta de sucesso
      SuccessResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          data: {
            type: 'object',
            description: 'Dados da resposta'
          },
          meta: {
            type: 'object',
            description: 'Metadados da resposta (paginação, etc.)',
            properties: {
              page: { type: 'integer' },
              limit: { type: 'integer' },
              total: { type: 'integer' },
              totalPages: { type: 'integer' }
            }
          }
        },
        required: ['success', 'data']
      },

      // ========================================
      // SCHEMAS DE AUTENTICAÇÃO
      // ========================================

      // Schema para requisição de login
      LoginRequest: {
        type: 'object',
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: 'Email do usuário',
            example: '<EMAIL>'
          },
          password: {
            type: 'string',
            minLength: 1,
            description: 'Senha do usuário',
            example: 'minhasenha123'
          }
        },
        required: ['email', 'password']
      },

      // Schema para requisição de registro
      RegisterRequest: {
        type: 'object',
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: 'Email do usuário',
            example: '<EMAIL>'
          },
          password: {
            type: 'string',
            minLength: 6,
            description: 'Senha do usuário (mínimo 6 caracteres)',
            example: 'minhasenha123'
          },
          name: {
            type: 'string',
            minLength: 2,
            description: 'Nome completo do usuário',
            example: 'João Silva'
          }
        },
        required: ['email', 'password', 'name']
      },

      // Schema do usuário autenticado
      AuthUser: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único do usuário',
            example: 'cmbm8zo0l0000dx733kfw73ij'
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Email do usuário',
            example: '<EMAIL>'
          },
          name: {
            type: 'string',
            description: 'Nome completo do usuário',
            example: 'João Silva'
          },
          isActive: {
            type: 'boolean',
            description: 'Status de ativação da conta',
            example: true
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação da conta',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          }
        },
        required: ['id', 'email', 'name', 'isActive', 'createdAt', 'updatedAt']
      },

      // Schema da resposta de autenticação
      AuthResponse: {
        type: 'object',
        properties: {
          user: {
            $ref: '#/components/schemas/AuthUser'
          },
          token: {
            type: 'string',
            description: 'Token JWT para autenticação',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.example'
          },
          expiresIn: {
            type: 'string',
            description: 'Tempo de expiração do token',
            example: '7d'
          }
        },
        required: ['user', 'token', 'expiresIn']
      },

      // ========================================
      // SCHEMAS DE CONTAS FINANCEIRAS
      // ========================================

      // Schema para criar conta
      CreateAccountRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome da conta',
            example: 'Conta Corrente Banco do Brasil'
          },
          type: {
            type: 'string',
            enum: ['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'CASH', 'ASSETS'],
            description: 'Tipo da conta financeira'
          },
          currency: {
            type: 'string',
            enum: ['BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'],
            default: 'BRL',
            description: 'Moeda da conta'
          },
          creditLimit: {
            type: 'number',
            minimum: 0.01,
            maximum: *********.99,
            description: 'Limite de crédito (obrigatório para cartões de crédito)',
            example: 5000.00
          },
          exchangeRate: {
            type: 'number',
            minimum: 0.000001,
            maximum: 999999.999999,
            description: 'Taxa de câmbio (obrigatória para moedas estrangeiras)',
            example: 5.25
          },
          includeInTotal: {
            type: 'boolean',
            default: true,
            description: 'Incluir no cálculo do patrimônio total'
          },
          logoPath: {
            type: 'string',
            maxLength: 255,
            description: 'Caminho para o logo da conta',
            example: '/uploads/logos/banco-logo.svg'
          },
          familyMemberIds: {
            type: 'array',
            items: {
              type: 'string',
              format: 'cuid'
            },
            minItems: 1,
            maxItems: 10,
            description: 'IDs dos membros da família associados à conta',
            example: ['cm123abc456def', 'cm789xyz012ghi']
          }
        },
        required: ['name', 'type']
      },

      // Schema para atualizar conta
      UpdateAccountRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome da conta'
          },
          creditLimit: {
            type: 'number',
            minimum: 0.01,
            maximum: *********.99,
            description: 'Limite de crédito'
          },
          exchangeRate: {
            type: 'number',
            minimum: 0.000001,
            maximum: 999999.999999,
            description: 'Taxa de câmbio'
          },
          includeInTotal: {
            type: 'boolean',
            description: 'Incluir no cálculo do patrimônio total'
          },
          logoPath: {
            type: 'string',
            maxLength: 255,
            description: 'Caminho para o logo da conta'
          },
          familyMemberIds: {
            type: 'array',
            items: {
              type: 'string',
              format: 'cuid'
            },
            maxItems: 10,
            description: 'IDs dos membros da família associados à conta'
          }
        }
      },

      // Schema da conta financeira
      Account: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único da conta',
            example: 'cm123abc456def'
          },
          name: {
            type: 'string',
            description: 'Nome da conta',
            example: 'Conta Corrente Banco do Brasil'
          },
          type: {
            type: 'string',
            enum: ['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'CASH', 'ASSETS'],
            description: 'Tipo da conta'
          },
          currency: {
            type: 'string',
            description: 'Moeda da conta',
            example: 'BRL'
          },
          creditLimit: {
            type: 'number',
            description: 'Limite de crédito (apenas cartões)',
            example: 5000.00
          },
          exchangeRate: {
            type: 'number',
            description: 'Taxa de câmbio (moedas estrangeiras)',
            example: 5.25
          },
          includeInTotal: {
            type: 'boolean',
            description: 'Incluir no patrimônio total',
            example: true
          },
          logoPath: {
            type: 'string',
            description: 'Caminho do logo',
            example: '/uploads/logos/banco-logo.svg'
          },
          archived: {
            type: 'boolean',
            description: 'Status de arquivamento',
            example: false
          },
          currentBalance: {
            type: 'number',
            description: 'Saldo atual calculado',
            example: 2500.75
          },
          availableBalance: {
            type: 'number',
            description: 'Saldo disponível (considerando limite)',
            example: 7500.75
          },
          balanceInBRL: {
            type: 'number',
            description: 'Saldo convertido para BRL',
            example: 13128.94
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          },
          familyMembers: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                color: { type: 'string' }
              }
            },
            description: 'Membros da família associados'
          }
        },
        required: ['id', 'name', 'type', 'currency', 'includeInTotal', 'archived', 'createdAt', 'updatedAt']
      },

      // ========================================
      // SCHEMAS DE CATEGORIAS
      // ========================================

      // Schema para criar categoria
      CreateCategoryRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome da categoria',
            example: 'Alimentação'
          },
          color: {
            type: 'string',
            pattern: '^#[0-9A-Fa-f]{6}$',
            description: 'Cor da categoria em formato hexadecimal',
            example: '#10B981'
          },
          parentId: {
            type: 'string',
            format: 'cuid',
            description: 'ID da categoria pai (para subcategorias)',
            example: 'cm123abc456def'
          }
        },
        required: ['name']
      },

      // Schema para atualizar categoria
      UpdateCategoryRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome da categoria'
          },
          color: {
            type: 'string',
            pattern: '^#[0-9A-Fa-f]{6}$',
            description: 'Cor da categoria em formato hexadecimal'
          },
          parentId: {
            type: 'string',
            format: 'cuid',
            nullable: true,
            description: 'ID da categoria pai (null para remover hierarquia)'
          }
        }
      },

      // Schema da categoria
      Category: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único da categoria',
            example: 'cm123abc456def'
          },
          name: {
            type: 'string',
            description: 'Nome da categoria',
            example: 'Alimentação'
          },
          color: {
            type: 'string',
            description: 'Cor da categoria',
            example: '#10B981'
          },
          parentId: {
            type: 'string',
            nullable: true,
            description: 'ID da categoria pai',
            example: 'cm789xyz012ghi'
          },
          archived: {
            type: 'boolean',
            description: 'Status de arquivamento',
            example: false
          },
          level: {
            type: 'integer',
            description: 'Nível hierárquico (0=pai, 1=filho)',
            example: 0
          },
          hasChildren: {
            type: 'boolean',
            description: 'Possui subcategorias',
            example: true
          },
          transactionCount: {
            type: 'integer',
            description: 'Número de transações associadas',
            example: 25
          },
          budgetCount: {
            type: 'integer',
            description: 'Número de orçamentos associados',
            example: 3
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          },
          parent: {
            type: 'object',
            nullable: true,
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              color: { type: 'string' }
            },
            description: 'Categoria pai (se for subcategoria)'
          },
          children: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                color: { type: 'string' }
              }
            },
            description: 'Subcategorias (se for categoria pai)'
          }
        },
        required: ['id', 'name', 'archived', 'level', 'hasChildren', 'createdAt', 'updatedAt']
      },

      // Schema da árvore de categorias
      CategoryTree: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          color: { type: 'string' },
          archived: { type: 'boolean' },
          level: { type: 'integer' },
          transactionCount: { type: 'integer' },
          budgetCount: { type: 'integer' },
          children: {
            type: 'array',
            items: { $ref: '#/components/schemas/CategoryTree' }
          }
        },
        required: ['id', 'name', 'archived', 'level', 'children']
      },

      // ========================================
      // SCHEMAS DE MEMBROS DA FAMÍLIA
      // ========================================

      // Schema para criar membro da família
      CreateFamilyMemberRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome do membro da família',
            example: 'João Silva'
          },
          color: {
            type: 'string',
            pattern: '^#[0-9A-Fa-f]{6}$',
            description: 'Cor para identificação visual em formato hexadecimal',
            example: '#3B82F6'
          }
        },
        required: ['name', 'color']
      },

      // Schema para atualizar membro da família
      UpdateFamilyMemberRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome do membro da família'
          },
          color: {
            type: 'string',
            pattern: '^#[0-9A-Fa-f]{6}$',
            description: 'Cor para identificação visual'
          }
        }
      },

      // Schema do membro da família
      FamilyMember: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único do membro',
            example: 'cm123abc456def'
          },
          name: {
            type: 'string',
            description: 'Nome do membro',
            example: 'João Silva'
          },
          color: {
            type: 'string',
            description: 'Cor de identificação',
            example: '#3B82F6'
          },
          archived: {
            type: 'boolean',
            description: 'Status de arquivamento',
            example: false
          },
          version: {
            type: 'integer',
            description: 'Versão para controle de concorrência',
            example: 1
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          }
        },
        required: ['id', 'name', 'color', 'archived', 'version', 'createdAt', 'updatedAt']
      },

      // Schema de estatísticas dos membros
      FamilyMemberStats: {
        type: 'object',
        properties: {
          totalMembers: {
            type: 'integer',
            description: 'Total de membros ativos',
            example: 4
          },
          archivedMembers: {
            type: 'integer',
            description: 'Total de membros arquivados',
            example: 1
          },
          membersWithTransactions: {
            type: 'integer',
            description: 'Membros com transações',
            example: 3
          },
          membersWithBudgets: {
            type: 'integer',
            description: 'Membros com orçamentos',
            example: 2
          }
        },
        required: ['totalMembers', 'archivedMembers', 'membersWithTransactions', 'membersWithBudgets']
      },

      // ========================================
      // SCHEMAS DE ORÇAMENTOS
      // ========================================

      // Schema para criar orçamento
      CreateBudgetRequest: {
        type: 'object',
        properties: {
          plannedAmount: {
            type: 'number',
            minimum: 0.01,
            maximum: *********.99,
            description: 'Valor planejado para o orçamento',
            example: 1500.00
          },
          month: {
            type: 'integer',
            minimum: 1,
            maximum: 12,
            description: 'Mês do orçamento (1-12)',
            example: 12
          },
          year: {
            type: 'integer',
            minimum: 2020,
            maximum: 2050,
            description: 'Ano do orçamento',
            example: 2024
          },
          categoryId: {
            type: 'string',
            format: 'cuid',
            description: 'ID da categoria',
            example: 'cm123abc456def'
          },
          familyMemberId: {
            type: 'string',
            format: 'cuid',
            description: 'ID do membro da família (opcional)',
            example: 'cm789xyz012ghi'
          }
        },
        required: ['plannedAmount', 'month', 'year', 'categoryId']
      },

      // Schema para atualizar orçamento
      UpdateBudgetRequest: {
        type: 'object',
        properties: {
          plannedAmount: {
            type: 'number',
            minimum: 0.01,
            maximum: *********.99,
            description: 'Valor planejado para o orçamento'
          },
          month: {
            type: 'integer',
            minimum: 1,
            maximum: 12,
            description: 'Mês do orçamento (1-12)'
          },
          year: {
            type: 'integer',
            minimum: 2020,
            maximum: 2050,
            description: 'Ano do orçamento'
          },
          categoryId: {
            type: 'string',
            format: 'cuid',
            description: 'ID da categoria'
          },
          familyMemberId: {
            type: 'string',
            format: 'cuid',
            nullable: true,
            description: 'ID do membro da família'
          }
        }
      },

      // Schema do orçamento
      Budget: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único do orçamento',
            example: 'cm123abc456def'
          },
          plannedAmount: {
            type: 'number',
            description: 'Valor planejado',
            example: 1500.00
          },
          spentAmount: {
            type: 'number',
            description: 'Valor gasto',
            example: 1200.50
          },
          remainingAmount: {
            type: 'number',
            description: 'Valor restante',
            example: 299.50
          },
          month: {
            type: 'integer',
            description: 'Mês do orçamento',
            example: 12
          },
          year: {
            type: 'integer',
            description: 'Ano do orçamento',
            example: 2024
          },
          progress: {
            type: 'number',
            description: 'Progresso em porcentagem (0-100+)',
            example: 80.03
          },
          status: {
            type: 'string',
            enum: ['under_budget', 'on_track', 'over_budget'],
            description: 'Status do orçamento',
            example: 'on_track'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          },
          category: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              color: { type: 'string' },
              parent: {
                type: 'object',
                nullable: true,
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  color: { type: 'string' }
                }
              }
            },
            description: 'Categoria associada'
          },
          familyMember: {
            type: 'object',
            nullable: true,
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              color: { type: 'string' }
            },
            description: 'Membro da família associado'
          }
        },
        required: ['id', 'plannedAmount', 'spentAmount', 'remainingAmount', 'month', 'year', 'progress', 'status', 'createdAt', 'updatedAt', 'category']
      },

      // ========================================
      // SCHEMAS DE METAS FINANCEIRAS
      // ========================================

      // Schema para criar meta financeira
      CreateFinancialGoalRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            description: 'Nome da meta financeira',
            example: 'Viagem para Europa'
          },
          goalType: {
            type: 'string',
            enum: ['ACCUMULATION', 'REDUCTION'],
            default: 'ACCUMULATION',
            description: 'Tipo da meta (acumular ou reduzir)'
          },
          targetAmount: {
            type: 'number',
            minimum: 0,
            maximum: *********.99,
            description: 'Valor alvo da meta',
            example: 15000.00
          },
          currentAmount: {
            type: 'number',
            minimum: 0,
            maximum: *********.99,
            default: 0,
            description: 'Valor atual da meta',
            example: 2500.00
          },
          initialAmount: {
            type: 'number',
            minimum: 0.01,
            maximum: *********.99,
            description: 'Valor inicial (para metas de redução)',
            example: 50000.00
          },
          targetDate: {
            type: 'string',
            format: 'date',
            description: 'Data alvo para atingir a meta',
            example: '2025-12-31'
          },
          familyMemberIds: {
            type: 'array',
            items: {
              type: 'string',
              format: 'cuid'
            },
            minItems: 0,
            maxItems: 10,
            description: 'IDs dos membros da família associados',
            example: ['cm123abc456def', 'cm789xyz012ghi']
          }
        },
        required: ['name', 'targetAmount']
      },

      // Schema da meta financeira
      FinancialGoal: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'ID único da meta',
            example: 'cm123abc456def'
          },
          name: {
            type: 'string',
            description: 'Nome da meta',
            example: 'Viagem para Europa'
          },
          goalType: {
            type: 'string',
            enum: ['ACCUMULATION', 'REDUCTION'],
            description: 'Tipo da meta'
          },
          targetAmount: {
            type: 'number',
            description: 'Valor alvo',
            example: 15000.00
          },
          currentAmount: {
            type: 'number',
            description: 'Valor atual',
            example: 2500.00
          },
          initialAmount: {
            type: 'number',
            description: 'Valor inicial (metas de redução)',
            example: 50000.00
          },
          targetDate: {
            type: 'string',
            format: 'date',
            description: 'Data alvo',
            example: '2025-12-31'
          },
          progress: {
            type: 'object',
            properties: {
              percentage: { type: 'number', example: 16.67 },
              status: {
                type: 'string',
                enum: ['not_started', 'in_progress', 'completed', 'overdue'],
                example: 'in_progress'
              },
              remainingAmount: { type: 'number', example: 12500.00 },
              daysRemaining: { type: 'integer', example: 365 },
              monthlyTarget: { type: 'number', example: 1041.67 }
            },
            description: 'Progresso calculado da meta'
          },
          members: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                color: { type: 'string' }
              }
            },
            description: 'Membros associados à meta'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação',
            example: '2024-01-15T10:30:00.000Z'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização',
            example: '2024-01-15T10:30:00.000Z'
          }
        },
        required: ['id', 'name', 'goalType', 'targetAmount', 'currentAmount', 'progress', 'members', 'createdAt', 'updatedAt']
      },

      // Schema de transação
      Transaction: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'ID único da transação'
          },
          description: {
            type: 'string',
            description: 'Descrição da transação',
            example: 'Compra no supermercado'
          },
          totalAmount: {
            type: 'number',
            format: 'decimal',
            description: 'Valor total da transação',
            example: 150.75
          },
          transactionDate: {
            type: 'string',
            format: 'date',
            description: 'Data da transação',
            example: '2024-01-15'
          },
          type: {
            type: 'string',
            enum: ['INCOME', 'EXPENSE', 'TRANSFER'],
            description: 'Tipo da transação'
          },
          accountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de origem'
          },
          categoryId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da categoria (obrigatório para despesas)',
            nullable: true
          },
          destinationAccountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de destino (para transferências)',
            nullable: true
          },
          totalInstallments: {
            type: 'integer',
            description: 'Número total de parcelas',
            nullable: true,
            example: 12
          },
          sourceCurrency: {
            type: 'string',
            description: 'Moeda de origem',
            example: 'BRL'
          },
          destinationCurrency: {
            type: 'string',
            description: 'Moeda de destino',
            example: 'USD'
          },
          exchangeRate: {
            type: 'number',
            format: 'decimal',
            description: 'Taxa de câmbio aplicada',
            nullable: true
          },
          isFuture: {
            type: 'boolean',
            description: 'Indica se é uma transação futura'
          },
          isDeleted: {
            type: 'boolean',
            description: 'Indica se a transação foi excluída (soft delete)'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização'
          }
        },
        required: ['id', 'description', 'totalAmount', 'transactionDate', 'type', 'accountId']
      },

      // Schema para criar transação
      CreateTransactionRequest: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            minLength: 1,
            description: 'Descrição da transação',
            example: 'Compra no supermercado'
          },
          amount: {
            type: 'number',
            minimum: 0.01,
            description: 'Valor da transação',
            example: 150.75
          },
          transactionDate: {
            type: 'string',
            format: 'date',
            description: 'Data da transação',
            example: '2024-01-15'
          },
          type: {
            type: 'string',
            enum: ['INCOME', 'EXPENSE', 'TRANSFER'],
            description: 'Tipo da transação'
          },
          accountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de origem'
          },
          categoryId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da categoria (obrigatório para despesas)',
            nullable: true
          },
          destinationAccountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de destino (obrigatório para transferências)',
            nullable: true
          },
          totalInstallments: {
            type: 'integer',
            minimum: 1,
            maximum: 120,
            description: 'Número total de parcelas',
            nullable: true
          },
          sourceCurrency: {
            type: 'string',
            description: 'Moeda de origem',
            default: 'BRL'
          },
          destinationCurrency: {
            type: 'string',
            description: 'Moeda de destino',
            default: 'BRL'
          },
          isFuture: {
            type: 'boolean',
            description: 'Indica se é uma transação futura',
            default: false
          }
        },
        required: ['description', 'amount', 'transactionDate', 'type', 'accountId']
      }
    }
  },
  paths: {
    // ========================================
    // ENDPOINTS DE AUTENTICAÇÃO
    // ========================================

    '/auth/register': {
      post: {
        tags: ['Autenticação'],
        summary: 'Registrar novo usuário',
        description: 'Cria uma nova conta de usuário no sistema',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/RegisterRequest' },
              examples: {
                usuario_exemplo: {
                  summary: 'Registro de usuário exemplo',
                  value: {
                    email: '<EMAIL>',
                    password: 'minhasenha123',
                    name: 'João Silva'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Usuário registrado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/AuthResponse' },
                        message: {
                          type: 'string',
                          example: 'Usuário registrado com sucesso'
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou email já em uso',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  dados_invalidos: {
                    summary: 'Dados de entrada inválidos',
                    value: {
                      success: false,
                      error: {
                        message: 'Dados inválidos',
                        code: 'VALIDATION_ERROR',
                        field: 'email'
                      }
                    }
                  },
                  email_em_uso: {
                    summary: 'Email já cadastrado',
                    value: {
                      success: false,
                      error: {
                        message: 'Email já está em uso',
                        code: 'REGISTRATION_ERROR'
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },

    '/auth/login': {
      post: {
        tags: ['Autenticação'],
        summary: 'Fazer login',
        description: 'Autentica um usuário e retorna um token JWT',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/LoginRequest' },
              examples: {
                login_exemplo: {
                  summary: 'Login de usuário exemplo',
                  value: {
                    email: '<EMAIL>',
                    password: 'minhasenha123'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Login realizado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/AuthResponse' },
                        message: {
                          type: 'string',
                          example: 'Login realizado com sucesso'
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Dados inválidos',
                    code: 'VALIDATION_ERROR',
                    field: 'email'
                  }
                }
              }
            }
          },
          '401': {
            description: 'Credenciais inválidas',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Email ou senha incorretos',
                    code: 'LOGIN_ERROR'
                  }
                }
              }
            }
          }
        }
      }
    },

    '/auth/profile': {
      get: {
        tags: ['Autenticação'],
        summary: 'Obter perfil do usuário',
        description: 'Retorna as informações do perfil do usuário autenticado',
        security: [{ bearerAuth: [] }],
        responses: {
          '200': {
            description: 'Perfil do usuário obtido com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/AuthUser' },
                        message: {
                          type: 'string',
                          example: 'Perfil obtido com sucesso'
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido ou ausente',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  token_ausente: {
                    summary: 'Token não fornecido',
                    value: {
                      success: false,
                      error: {
                        message: 'Token de acesso requerido',
                        code: 'MISSING_TOKEN'
                      }
                    }
                  },
                  token_invalido: {
                    summary: 'Token inválido',
                    value: {
                      success: false,
                      error: {
                        message: 'Token inválido ou expirado',
                        code: 'INVALID_TOKEN'
                      }
                    }
                  }
                }
              }
            }
          },
          '403': {
            description: 'Conta desativada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Conta desativada',
                    code: 'ACCOUNT_DEACTIVATED'
                  }
                }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE METAS FINANCEIRAS
    // ========================================

    '/goals': {
      post: {
        tags: ['Metas Financeiras'],
        summary: 'Criar nova meta',
        description: 'Cria uma nova meta financeira com membros associados',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateFinancialGoalRequest' },
              examples: {
                meta_acumulacao: {
                  summary: 'Meta de acumulação',
                  value: {
                    name: 'Viagem para Europa',
                    goalType: 'ACCUMULATION',
                    targetAmount: 15000.00,
                    currentAmount: 2500.00,
                    targetDate: '2025-12-31',
                    familyMemberIds: ['cm123abc456def']
                  }
                },
                meta_reducao: {
                  summary: 'Meta de redução de dívida',
                  value: {
                    name: 'Quitar Cartão de Crédito',
                    goalType: 'REDUCTION',
                    targetAmount: 0.00,
                    currentAmount: 5000.00,
                    initialAmount: 10000.00,
                    targetDate: '2025-06-30',
                    familyMemberIds: ['cm123abc456def', 'cm789xyz012ghi']
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Meta criada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FinancialGoal' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  membro_invalido: {
                    summary: 'Membro da família não encontrado',
                    value: {
                      success: false,
                      error: {
                        message: 'Um ou mais membros da família não foram encontrados',
                        code: 'FAMILY_MEMBER_NOT_FOUND'
                      }
                    }
                  },
                  valores_invalidos: {
                    summary: 'Valores inconsistentes',
                    value: {
                      success: false,
                      error: {
                        message: 'Para metas de redução, valor atual deve ser menor que valor inicial',
                        code: 'INVALID_GOAL_VALUES'
                      }
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Metas Financeiras'],
        summary: 'Listar metas',
        description: 'Lista metas financeiras com filtros e progresso calculado',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'name',
            in: 'query',
            description: 'Filtrar por nome (busca parcial)',
            schema: { type: 'string' }
          },
          {
            name: 'goalType',
            in: 'query',
            description: 'Filtrar por tipo de meta',
            schema: {
              type: 'string',
              enum: ['ACCUMULATION', 'REDUCTION']
            }
          },
          {
            name: 'status',
            in: 'query',
            description: 'Filtrar por status',
            schema: {
              type: 'string',
              enum: ['not_started', 'in_progress', 'completed', 'overdue']
            }
          },
          {
            name: 'familyMemberId',
            in: 'query',
            description: 'Filtrar por membro da família',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeMilestones',
            in: 'query',
            description: 'Incluir marcos na resposta',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de metas financeiras',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/FinancialGoal' }
                        },
                        pagination: {
                          type: 'object',
                          properties: {
                            page: { type: 'integer' },
                            limit: { type: 'integer' },
                            total: { type: 'integer' },
                            totalPages: { type: 'integer' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/goals/{id}': {
      get: {
        tags: ['Metas Financeiras'],
        summary: 'Obter meta por ID',
        description: 'Retorna os detalhes de uma meta específica com progresso',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da meta',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeMilestones',
            in: 'query',
            description: 'Incluir marcos na resposta',
            schema: { type: 'boolean', default: false }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes da meta financeira',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FinancialGoal' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Meta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Metas Financeiras'],
        summary: 'Excluir meta',
        description: 'Exclui permanentemente uma meta financeira',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da meta',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Meta excluída com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Meta financeira deletada com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Meta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/goals/{id}/progress': {
      post: {
        tags: ['Metas Financeiras'],
        summary: 'Atualizar progresso da meta',
        description: 'Adiciona, subtrai ou define o progresso de uma meta',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da meta',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  amount: {
                    type: 'number',
                    minimum: 0.01,
                    maximum: *********.99,
                    description: 'Valor da atualização'
                  },
                  operation: {
                    type: 'string',
                    enum: ['add', 'subtract', 'set'],
                    description: 'Tipo de operação'
                  },
                  description: {
                    type: 'string',
                    maxLength: 500,
                    description: 'Descrição da atualização'
                  }
                },
                required: ['amount', 'operation']
              },
              examples: {
                adicionar_valor: {
                  summary: 'Adicionar valor à meta',
                  value: {
                    amount: 500.00,
                    operation: 'add',
                    description: 'Depósito mensal para viagem'
                  }
                },
                definir_valor: {
                  summary: 'Definir valor atual',
                  value: {
                    amount: 3000.00,
                    operation: 'set',
                    description: 'Ajuste do saldo atual'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Progresso atualizado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FinancialGoal' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Operação inválida',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  valor_negativo: {
                    summary: 'Resultado seria negativo',
                    value: {
                      success: false,
                      error: {
                        message: 'Operação resultaria em valor negativo',
                        code: 'INVALID_OPERATION'
                      }
                    }
                  }
                }
              }
            }
          },
          '404': {
            description: 'Meta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE ORÇAMENTOS
    // ========================================

    '/budgets': {
      post: {
        tags: ['Orçamentos'],
        summary: 'Criar novo orçamento',
        description: 'Cria um novo orçamento para categoria e período específicos',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateBudgetRequest' },
              examples: {
                orcamento_alimentacao: {
                  summary: 'Orçamento para alimentação',
                  value: {
                    plannedAmount: 1500.00,
                    month: 12,
                    year: 2024,
                    categoryId: 'cm123abc456def',
                    familyMemberId: 'cm789xyz012ghi'
                  }
                },
                orcamento_geral: {
                  summary: 'Orçamento geral (sem membro específico)',
                  value: {
                    plannedAmount: 3000.00,
                    month: 1,
                    year: 2025,
                    categoryId: 'cm123abc456def'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Orçamento criado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Budget' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou orçamento duplicado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  orcamento_duplicado: {
                    summary: 'Orçamento já existe',
                    value: {
                      success: false,
                      error: {
                        message: 'Já existe um orçamento para esta categoria, membro e período',
                        code: 'BUDGET_ALREADY_EXISTS'
                      }
                    }
                  },
                  categoria_invalida: {
                    summary: 'Categoria não encontrada',
                    value: {
                      success: false,
                      error: {
                        message: 'Categoria não encontrada ou foi arquivada',
                        code: 'CATEGORY_NOT_FOUND'
                      }
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Orçamentos'],
        summary: 'Listar orçamentos',
        description: 'Lista orçamentos com filtros avançados e progresso',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'categoryId',
            in: 'query',
            description: 'Filtrar por categoria',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'familyMemberId',
            in: 'query',
            description: 'Filtrar por membro da família',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'month',
            in: 'query',
            description: 'Filtrar por mês',
            schema: { type: 'integer', minimum: 1, maximum: 12 }
          },
          {
            name: 'year',
            in: 'query',
            description: 'Filtrar por ano',
            schema: { type: 'integer', minimum: 2020, maximum: 2050 }
          },
          {
            name: 'includeProgress',
            in: 'query',
            description: 'Incluir cálculo de progresso',
            schema: { type: 'boolean', default: true }
          },
          {
            name: 'sortBy',
            in: 'query',
            description: 'Campo para ordenação',
            schema: {
              type: 'string',
              enum: ['plannedAmount', 'month', 'year', 'createdAt', 'category', 'familyMember'],
              default: 'createdAt'
            }
          },
          {
            name: 'sortOrder',
            in: 'query',
            description: 'Ordem de classificação',
            schema: {
              type: 'string',
              enum: ['asc', 'desc'],
              default: 'desc'
            }
          },
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de orçamentos',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Budget' }
                        },
                        pagination: {
                          type: 'object',
                          properties: {
                            page: { type: 'integer' },
                            limit: { type: 'integer' },
                            total: { type: 'integer' },
                            totalPages: { type: 'integer' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/budgets/{id}': {
      get: {
        tags: ['Orçamentos'],
        summary: 'Obter orçamento por ID',
        description: 'Retorna os detalhes de um orçamento específico com progresso',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do orçamento',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeProgress',
            in: 'query',
            description: 'Incluir cálculo de progresso',
            schema: { type: 'boolean', default: true }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes do orçamento',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Budget' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Orçamento não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Orçamentos'],
        summary: 'Atualizar orçamento',
        description: 'Atualiza os dados de um orçamento existente',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do orçamento',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateBudgetRequest' },
              examples: {
                aumentar_valor: {
                  summary: 'Aumentar valor planejado',
                  value: {
                    plannedAmount: 2000.00
                  }
                },
                alterar_periodo: {
                  summary: 'Alterar período',
                  value: {
                    month: 2,
                    year: 2025
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Orçamento atualizado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Budget' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou orçamento duplicado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '404': {
            description: 'Orçamento não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Orçamentos'],
        summary: 'Excluir orçamento',
        description: 'Exclui permanentemente um orçamento',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do orçamento',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Orçamento excluído com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Orçamento deletado com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Orçamento não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE MEMBROS DA FAMÍLIA
    // ========================================

    '/family-members': {
      post: {
        tags: ['Membros da Família'],
        summary: 'Criar novo membro',
        description: 'Cria um novo membro da família com cor de identificação',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateFamilyMemberRequest' },
              examples: {
                membro_exemplo: {
                  summary: 'Novo membro da família',
                  value: {
                    name: 'João Silva',
                    color: '#3B82F6'
                  }
                },
                membro_crianca: {
                  summary: 'Membro criança',
                  value: {
                    name: 'Ana Silva',
                    color: '#EC4899'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Membro criado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FamilyMember' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou nome duplicado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  nome_duplicado: {
                    summary: 'Nome já existe',
                    value: {
                      success: false,
                      error: {
                        message: 'Já existe um membro da família com este nome',
                        code: 'FAMILY_MEMBER_NAME_EXISTS'
                      }
                    }
                  },
                  cor_invalida: {
                    summary: 'Cor inválida',
                    value: {
                      success: false,
                      error: {
                        message: 'Cor deve estar no formato hexadecimal (#RRGGBB)',
                        code: 'VALIDATION_ERROR',
                        field: 'color'
                      }
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Membros da Família'],
        summary: 'Listar membros',
        description: 'Lista todos os membros da família com filtros e paginação',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'name',
            in: 'query',
            description: 'Filtrar por nome (busca parcial)',
            schema: { type: 'string' }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir membros arquivados',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de membros da família',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/FamilyMember' }
                        },
                        pagination: {
                          type: 'object',
                          properties: {
                            page: { type: 'integer' },
                            limit: { type: 'integer' },
                            total: { type: 'integer' },
                            totalPages: { type: 'integer' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/family-members/stats': {
      get: {
        tags: ['Membros da Família'],
        summary: 'Estatísticas dos membros',
        description: 'Retorna estatísticas gerais dos membros da família',
        security: [{ bearerAuth: [] }],
        responses: {
          '200': {
            description: 'Estatísticas dos membros',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FamilyMemberStats' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/family-members/{id}': {
      get: {
        tags: ['Membros da Família'],
        summary: 'Obter membro por ID',
        description: 'Retorna os detalhes de um membro específico',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do membro',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir membro arquivado',
            schema: { type: 'boolean', default: false }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes do membro',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FamilyMember' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Membro não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Membro da família não encontrado',
                    code: 'FAMILY_MEMBER_NOT_FOUND'
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Membros da Família'],
        summary: 'Atualizar membro',
        description: 'Atualiza os dados de um membro da família',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do membro',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateFamilyMemberRequest' },
              examples: {
                atualizar_nome: {
                  summary: 'Atualizar nome',
                  value: {
                    name: 'João Santos Silva'
                  }
                },
                atualizar_cor: {
                  summary: 'Atualizar cor',
                  value: {
                    color: '#10B981'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Membro atualizado com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FamilyMember' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou nome duplicado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '404': {
            description: 'Membro não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Membros da Família'],
        summary: 'Excluir membro',
        description: 'Exclui permanentemente um membro (apenas se não tiver associações)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do membro',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Membro excluído com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Membro da família deletado permanentemente com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Membro possui associações',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  tem_transacoes: {
                    summary: 'Membro com transações',
                    value: {
                      success: false,
                      error: {
                        message: 'Não é possível excluir membro com transações associadas',
                        code: 'FAMILY_MEMBER_HAS_TRANSACTIONS'
                      }
                    }
                  },
                  tem_orcamentos: {
                    summary: 'Membro com orçamentos',
                    value: {
                      success: false,
                      error: {
                        message: 'Não é possível excluir membro com orçamentos associados',
                        code: 'FAMILY_MEMBER_HAS_BUDGETS'
                      }
                    }
                  }
                }
              }
            }
          },
          '404': {
            description: 'Membro não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/family-members/{id}/archive': {
      patch: {
        tags: ['Membros da Família'],
        summary: 'Arquivar/Desarquivar membro',
        description: 'Arquiva ou desarquiva um membro da família (soft delete)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID do membro',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  archived: {
                    type: 'boolean',
                    description: 'Status de arquivamento'
                  }
                },
                required: ['archived']
              },
              examples: {
                arquivar: {
                  summary: 'Arquivar membro',
                  value: { archived: true }
                },
                desarquivar: {
                  summary: 'Desarquivar membro',
                  value: { archived: false }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Status de arquivamento atualizado',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/FamilyMember' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Membro não encontrado',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE CATEGORIAS
    // ========================================

    '/categories': {
      post: {
        tags: ['Categorias'],
        summary: 'Criar nova categoria',
        description: 'Cria uma nova categoria ou subcategoria com validação hierárquica',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateCategoryRequest' },
              examples: {
                categoria_pai: {
                  summary: 'Categoria principal',
                  value: {
                    name: 'Alimentação',
                    color: '#10B981'
                  }
                },
                subcategoria: {
                  summary: 'Subcategoria',
                  value: {
                    name: 'Restaurantes',
                    color: '#059669',
                    parentId: 'cm123abc456def'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Categoria criada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Category' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou categoria duplicada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  nome_duplicado: {
                    summary: 'Nome já existe',
                    value: {
                      success: false,
                      error: {
                        message: 'Já existe uma categoria com este nome na mesma categoria pai',
                        code: 'CATEGORY_NAME_EXISTS'
                      }
                    }
                  },
                  hierarquia_invalida: {
                    summary: 'Hierarquia inválida',
                    value: {
                      success: false,
                      error: {
                        message: 'Profundidade máxima da hierarquia atingida',
                        code: 'MAX_HIERARCHY_DEPTH'
                      }
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Categorias'],
        summary: 'Listar categorias',
        description: 'Lista categorias com filtros hierárquicos e paginação',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'name',
            in: 'query',
            description: 'Filtrar por nome (busca parcial)',
            schema: { type: 'string' }
          },
          {
            name: 'parentId',
            in: 'query',
            description: 'Filtrar por categoria pai',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeChildren',
            in: 'query',
            description: 'Incluir subcategorias na resposta',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'onlyParents',
            in: 'query',
            description: 'Apenas categorias principais',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'onlyChildren',
            in: 'query',
            description: 'Apenas subcategorias',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir categorias arquivadas',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de categorias',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Category' }
                        },
                        pagination: {
                          type: 'object',
                          properties: {
                            page: { type: 'integer' },
                            limit: { type: 'integer' },
                            total: { type: 'integer' },
                            totalPages: { type: 'integer' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/categories/tree': {
      get: {
        tags: ['Categorias'],
        summary: 'Obter árvore de categorias',
        description: 'Retorna a estrutura hierárquica completa das categorias',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir categorias arquivadas',
            schema: { type: 'boolean', default: false }
          }
        ],
        responses: {
          '200': {
            description: 'Árvore de categorias',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/CategoryTree' }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/categories/selection': {
      get: {
        tags: ['Categorias'],
        summary: 'Categorias para seleção',
        description: 'Lista categorias formatadas para dropdowns e seletores',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'onlyActive',
            in: 'query',
            description: 'Apenas categorias ativas',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'excludeId',
            in: 'query',
            description: 'Excluir categoria específica',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Categorias para seleção',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              value: { type: 'string' },
                              label: { type: 'string' },
                              level: { type: 'integer' },
                              disabled: { type: 'boolean' }
                            }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/categories/{id}': {
      get: {
        tags: ['Categorias'],
        summary: 'Obter categoria por ID',
        description: 'Retorna os detalhes de uma categoria específica',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da categoria',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir categoria arquivada',
            schema: { type: 'boolean', default: false }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes da categoria',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Category' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Categoria não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Categorias'],
        summary: 'Atualizar categoria',
        description: 'Atualiza os dados de uma categoria com validação hierárquica',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da categoria',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateCategoryRequest' },
              examples: {
                atualizar_nome: {
                  summary: 'Atualizar nome',
                  value: {
                    name: 'Novo Nome da Categoria'
                  }
                },
                mover_hierarquia: {
                  summary: 'Mover para outra categoria pai',
                  value: {
                    parentId: 'cm789xyz012ghi'
                  }
                },
                remover_hierarquia: {
                  summary: 'Tornar categoria principal',
                  value: {
                    parentId: null
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Categoria atualizada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Category' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos ou hierarquia circular',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  referencia_circular: {
                    summary: 'Referência circular detectada',
                    value: {
                      success: false,
                      error: {
                        message: 'Não é possível criar referência circular na hierarquia',
                        code: 'CIRCULAR_REFERENCE'
                      }
                    }
                  }
                }
              }
            }
          },
          '404': {
            description: 'Categoria não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Categorias'],
        summary: 'Excluir categoria',
        description: 'Exclui permanentemente uma categoria (apenas se não tiver transações ou subcategorias)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da categoria',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Categoria excluída com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Categoria excluída com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Categoria possui dependências',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  tem_transacoes: {
                    summary: 'Categoria com transações',
                    value: {
                      success: false,
                      error: {
                        message: 'Não é possível excluir categoria com transações associadas',
                        code: 'CATEGORY_HAS_TRANSACTIONS'
                      }
                    }
                  },
                  tem_subcategorias: {
                    summary: 'Categoria com subcategorias',
                    value: {
                      success: false,
                      error: {
                        message: 'Não é possível excluir categoria com subcategorias',
                        code: 'CATEGORY_HAS_CHILDREN'
                      }
                    }
                  }
                }
              }
            }
          },
          '404': {
            description: 'Categoria não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/categories/{id}/archive': {
      patch: {
        tags: ['Categorias'],
        summary: 'Arquivar/Desarquivar categoria',
        description: 'Arquiva ou desarquiva uma categoria (soft delete)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da categoria',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  archived: {
                    type: 'boolean',
                    description: 'Status de arquivamento'
                  }
                },
                required: ['archived']
              },
              examples: {
                arquivar: {
                  summary: 'Arquivar categoria',
                  value: { archived: true }
                },
                desarquivar: {
                  summary: 'Desarquivar categoria',
                  value: { archived: false }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Status de arquivamento atualizado',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Category' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Categoria não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE CONTAS FINANCEIRAS
    // ========================================

    '/accounts': {
      post: {
        tags: ['Contas'],
        summary: 'Criar nova conta',
        description: 'Cria uma nova conta financeira (corrente, poupança, cartão de crédito, etc.)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateAccountRequest' },
              examples: {
                conta_corrente: {
                  summary: 'Conta corrente em BRL',
                  value: {
                    name: 'Conta Corrente Banco do Brasil',
                    type: 'CHECKING',
                    currency: 'BRL',
                    includeInTotal: true,
                    familyMemberIds: ['cm123abc456def']
                  }
                },
                cartao_credito: {
                  summary: 'Cartão de crédito',
                  value: {
                    name: 'Cartão Visa Platinum',
                    type: 'CREDIT_CARD',
                    currency: 'BRL',
                    creditLimit: 5000.00,
                    includeInTotal: false,
                    familyMemberIds: ['cm123abc456def']
                  }
                },
                conta_usd: {
                  summary: 'Conta em moeda estrangeira',
                  value: {
                    name: 'Conta USD',
                    type: 'CHECKING',
                    currency: 'USD',
                    exchangeRate: 5.25,
                    includeInTotal: true,
                    familyMemberIds: ['cm123abc456def']
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Conta criada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Account' },
                        message: {
                          type: 'string',
                          example: 'Conta criada com sucesso'
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                examples: {
                  dados_invalidos: {
                    summary: 'Dados de entrada inválidos',
                    value: {
                      success: false,
                      error: {
                        message: 'Dados inválidos',
                        code: 'VALIDATION_ERROR',
                        details: [
                          {
                            field: 'name',
                            message: 'Nome deve ter pelo menos 2 caracteres'
                          }
                        ]
                      }
                    }
                  },
                  limite_obrigatorio: {
                    summary: 'Limite obrigatório para cartão',
                    value: {
                      success: false,
                      error: {
                        message: 'Limite de crédito é obrigatório para cartões de crédito',
                        code: 'VALIDATION_ERROR'
                      }
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Contas'],
        summary: 'Listar contas',
        description: 'Lista todas as contas do usuário com filtros opcionais',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'name',
            in: 'query',
            description: 'Filtrar por nome (busca parcial)',
            schema: { type: 'string' }
          },
          {
            name: 'type',
            in: 'query',
            description: 'Filtrar por tipo de conta',
            schema: {
              type: 'string',
              enum: ['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'CASH', 'ASSETS']
            }
          },
          {
            name: 'currency',
            in: 'query',
            description: 'Filtrar por moeda',
            schema: {
              type: 'string',
              enum: ['BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF']
            }
          },
          {
            name: 'includeInTotal',
            in: 'query',
            description: 'Filtrar por inclusão no total',
            schema: { type: 'boolean' }
          },
          {
            name: 'familyMemberId',
            in: 'query',
            description: 'Filtrar por membro da família',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir contas arquivadas',
            schema: { type: 'boolean', default: false }
          },
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de contas',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Account' }
                        },
                        pagination: {
                          type: 'object',
                          properties: {
                            page: { type: 'integer' },
                            limit: { type: 'integer' },
                            total: { type: 'integer' },
                            totalPages: { type: 'integer' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/accounts/{id}': {
      get: {
        tags: ['Contas'],
        summary: 'Obter conta por ID',
        description: 'Retorna os detalhes de uma conta específica',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da conta',
            schema: { type: 'string', format: 'cuid' }
          },
          {
            name: 'includeArchived',
            in: 'query',
            description: 'Incluir conta arquivada',
            schema: { type: 'boolean', default: false }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes da conta',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Account' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Conta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Conta não encontrada',
                    code: 'ACCOUNT_NOT_FOUND'
                  }
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Contas'],
        summary: 'Atualizar conta',
        description: 'Atualiza os dados de uma conta existente',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da conta',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateAccountRequest' },
              examples: {
                atualizar_nome: {
                  summary: 'Atualizar nome da conta',
                  value: {
                    name: 'Novo Nome da Conta'
                  }
                },
                atualizar_limite: {
                  summary: 'Atualizar limite de crédito',
                  value: {
                    creditLimit: 8000.00
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Conta atualizada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Account' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '404': {
            description: 'Conta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Contas'],
        summary: 'Excluir conta',
        description: 'Exclui permanentemente uma conta (apenas se não tiver transações)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da conta',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Conta excluída com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Conta excluída com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Conta possui transações associadas',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
                example: {
                  success: false,
                  error: {
                    message: 'Não é possível excluir conta com transações associadas',
                    code: 'ACCOUNT_HAS_TRANSACTIONS'
                  }
                }
              }
            }
          },
          '404': {
            description: 'Conta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    '/accounts/{id}/archive': {
      patch: {
        tags: ['Contas'],
        summary: 'Arquivar/Desarquivar conta',
        description: 'Arquiva ou desarquiva uma conta (soft delete)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da conta',
            schema: { type: 'string', format: 'cuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  archived: {
                    type: 'boolean',
                    description: 'Status de arquivamento'
                  }
                },
                required: ['archived']
              },
              examples: {
                arquivar: {
                  summary: 'Arquivar conta',
                  value: { archived: true }
                },
                desarquivar: {
                  summary: 'Desarquivar conta',
                  value: { archived: false }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Status de arquivamento atualizado',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Account' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Conta não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },

    // ========================================
    // ENDPOINTS DE TRANSAÇÕES
    // ========================================

    '/transactions': {
      post: {
        tags: ['Transactions'],
        summary: 'Criar nova transação',
        description: 'Cria uma nova transação financeira (receita, despesa ou transferência)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateTransactionRequest' },
              examples: {
                expense: {
                  summary: 'Despesa parcelada',
                  value: {
                    description: 'Compra parcelada no cartão',
                    amount: 1200.00,
                    transactionDate: '2024-01-15',
                    type: 'EXPENSE',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    categoryId: '123e4567-e89b-12d3-a456-************',
                    totalInstallments: 12,
                    sourceCurrency: 'BRL'
                  }
                },
                income: {
                  summary: 'Receita',
                  value: {
                    description: 'Salário mensal',
                    amount: 5000.00,
                    transactionDate: '2024-01-01',
                    type: 'INCOME',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    sourceCurrency: 'BRL'
                  }
                },
                transfer: {
                  summary: 'Transferência',
                  value: {
                    description: 'Transferência entre contas',
                    amount: 500.00,
                    transactionDate: '2024-01-10',
                    type: 'TRANSFER',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    destinationAccountId: '123e4567-e89b-12d3-a456-************',
                    sourceCurrency: 'BRL'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Transação criada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Transactions'],
        summary: 'Listar transações',
        description: 'Lista todas as transações do usuário com filtros opcionais',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          },
          {
            name: 'type',
            in: 'query',
            description: 'Filtrar por tipo de transação',
            schema: { type: 'string', enum: ['INCOME', 'EXPENSE', 'TRANSFER'] }
          },
          {
            name: 'accountId',
            in: 'query',
            description: 'Filtrar por conta',
            schema: { type: 'string', format: 'uuid' }
          },
          {
            name: 'categoryId',
            in: 'query',
            description: 'Filtrar por categoria',
            schema: { type: 'string', format: 'uuid' }
          },
          {
            name: 'startDate',
            in: 'query',
            description: 'Data inicial (formato: YYYY-MM-DD)',
            schema: { type: 'string', format: 'date' }
          },
          {
            name: 'endDate',
            in: 'query',
            description: 'Data final (formato: YYYY-MM-DD)',
            schema: { type: 'string', format: 'date' }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de transações',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Transaction' }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },
    '/transactions/{id}': {
      get: {
        tags: ['Transactions'],
        summary: 'Obter transação por ID',
        description: 'Retorna os detalhes de uma transação específica',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes da transação',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Transactions'],
        summary: 'Atualizar transação',
        description: 'Atualiza uma transação existente',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  description: { type: 'string', minLength: 1 },
                  amount: { type: 'number', minimum: 0.01 },
                  transactionDate: { type: 'string', format: 'date' },
                  categoryId: { type: 'string', format: 'uuid', nullable: true }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Transação atualizada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Transactions'],
        summary: 'Excluir transação',
        description: 'Exclui uma transação (soft delete)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Transação excluída com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Transação excluída com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

// HTML para Swagger UI
const swaggerUIHTML = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Personal Finance API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .info .title { color: #2c3e50 }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: '/api-docs.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        persistAuthorization: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true
      });
    };
  </script>
</body>
</html>
`;

export const setupSwagger = (app: Express): void => {
  // Swagger UI HTML
  app.get('/api-docs', (req, res) => {
    res.setHeader('Content-Type', 'text/html');
    res.send(swaggerUIHTML);
  });

  // JSON endpoint para o spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.json(swaggerSpec);
  });

  console.log('📚 Swagger documentation available at /api-docs');
  console.log('📄 OpenAPI spec available at /api-docs.json');
};

export { swaggerSpec as specs };