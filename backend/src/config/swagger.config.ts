import { Express } from 'express';

// Especificação OpenAPI 3.0.0 completa
const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Personal Finance Manager API',
    version: '1.0.0',
    description: 'API completa para gerenciamento de finanças pessoais e familiares',
    contact: {
      name: '<PERSON><PERSON><PERSON> de Desenvolvimento',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: process.env.API_BASE_URL || 'http://localhost:3001/api/v1',
      description: 'Servidor de desenvolvimento'
    },
    {
      url: 'https://api.personalfinance.com/api/v1',
      description: 'Servidor de produção'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Token JWT obtido através do endpoint de autenticação'
      }
    },
    schemas: {
      // Schemas de erro padrão
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                description: 'Mensagem de erro em português'
              },
              code: {
                type: 'string',
                description: 'Código específico do erro'
              },
              details: {
                type: 'object',
                description: 'Detalhes adicionais do erro (opcional)'
              }
            },
            required: ['message', 'code']
          }
        },
        required: ['success', 'error']
      },
      
      // Schema de resposta de sucesso
      SuccessResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          data: {
            type: 'object',
            description: 'Dados da resposta'
          },
          meta: {
            type: 'object',
            description: 'Metadados da resposta (paginação, etc.)',
            properties: {
              page: { type: 'integer' },
              limit: { type: 'integer' },
              total: { type: 'integer' },
              totalPages: { type: 'integer' }
            }
          }
        },
        required: ['success', 'data']
      },

      // Schema de transação
      Transaction: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'ID único da transação'
          },
          description: {
            type: 'string',
            description: 'Descrição da transação',
            example: 'Compra no supermercado'
          },
          totalAmount: {
            type: 'number',
            format: 'decimal',
            description: 'Valor total da transação',
            example: 150.75
          },
          transactionDate: {
            type: 'string',
            format: 'date',
            description: 'Data da transação',
            example: '2024-01-15'
          },
          type: {
            type: 'string',
            enum: ['INCOME', 'EXPENSE', 'TRANSFER'],
            description: 'Tipo da transação'
          },
          accountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de origem'
          },
          categoryId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da categoria (obrigatório para despesas)',
            nullable: true
          },
          destinationAccountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de destino (para transferências)',
            nullable: true
          },
          totalInstallments: {
            type: 'integer',
            description: 'Número total de parcelas',
            nullable: true,
            example: 12
          },
          sourceCurrency: {
            type: 'string',
            description: 'Moeda de origem',
            example: 'BRL'
          },
          destinationCurrency: {
            type: 'string',
            description: 'Moeda de destino',
            example: 'USD'
          },
          exchangeRate: {
            type: 'number',
            format: 'decimal',
            description: 'Taxa de câmbio aplicada',
            nullable: true
          },
          isFuture: {
            type: 'boolean',
            description: 'Indica se é uma transação futura'
          },
          isDeleted: {
            type: 'boolean',
            description: 'Indica se a transação foi excluída (soft delete)'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data de criação'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Data da última atualização'
          }
        },
        required: ['id', 'description', 'totalAmount', 'transactionDate', 'type', 'accountId']
      },

      // Schema para criar transação
      CreateTransactionRequest: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            minLength: 1,
            description: 'Descrição da transação',
            example: 'Compra no supermercado'
          },
          amount: {
            type: 'number',
            minimum: 0.01,
            description: 'Valor da transação',
            example: 150.75
          },
          transactionDate: {
            type: 'string',
            format: 'date',
            description: 'Data da transação',
            example: '2024-01-15'
          },
          type: {
            type: 'string',
            enum: ['INCOME', 'EXPENSE', 'TRANSFER'],
            description: 'Tipo da transação'
          },
          accountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de origem'
          },
          categoryId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da categoria (obrigatório para despesas)',
            nullable: true
          },
          destinationAccountId: {
            type: 'string',
            format: 'uuid',
            description: 'ID da conta de destino (obrigatório para transferências)',
            nullable: true
          },
          totalInstallments: {
            type: 'integer',
            minimum: 1,
            maximum: 120,
            description: 'Número total de parcelas',
            nullable: true
          },
          sourceCurrency: {
            type: 'string',
            description: 'Moeda de origem',
            default: 'BRL'
          },
          destinationCurrency: {
            type: 'string',
            description: 'Moeda de destino',
            default: 'BRL'
          },
          isFuture: {
            type: 'boolean',
            description: 'Indica se é uma transação futura',
            default: false
          }
        },
        required: ['description', 'amount', 'transactionDate', 'type', 'accountId']
      }
    }
  },
  paths: {
    '/transactions': {
      post: {
        tags: ['Transactions'],
        summary: 'Criar nova transação',
        description: 'Cria uma nova transação financeira (receita, despesa ou transferência)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateTransactionRequest' },
              examples: {
                expense: {
                  summary: 'Despesa parcelada',
                  value: {
                    description: 'Compra parcelada no cartão',
                    amount: 1200.00,
                    transactionDate: '2024-01-15',
                    type: 'EXPENSE',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    categoryId: '123e4567-e89b-12d3-a456-************',
                    totalInstallments: 12,
                    sourceCurrency: 'BRL'
                  }
                },
                income: {
                  summary: 'Receita',
                  value: {
                    description: 'Salário mensal',
                    amount: 5000.00,
                    transactionDate: '2024-01-01',
                    type: 'INCOME',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    sourceCurrency: 'BRL'
                  }
                },
                transfer: {
                  summary: 'Transferência',
                  value: {
                    description: 'Transferência entre contas',
                    amount: 500.00,
                    transactionDate: '2024-01-10',
                    type: 'TRANSFER',
                    accountId: '123e4567-e89b-12d3-a456-************',
                    destinationAccountId: '123e4567-e89b-12d3-a456-************',
                    sourceCurrency: 'BRL'
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Transação criada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      get: {
        tags: ['Transactions'],
        summary: 'Listar transações',
        description: 'Lista todas as transações do usuário com filtros opcionais',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            description: 'Número da página',
            schema: { type: 'integer', minimum: 1, default: 1 }
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Itens por página',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
          },
          {
            name: 'type',
            in: 'query',
            description: 'Filtrar por tipo de transação',
            schema: { type: 'string', enum: ['INCOME', 'EXPENSE', 'TRANSFER'] }
          },
          {
            name: 'accountId',
            in: 'query',
            description: 'Filtrar por conta',
            schema: { type: 'string', format: 'uuid' }
          },
          {
            name: 'categoryId',
            in: 'query',
            description: 'Filtrar por categoria',
            schema: { type: 'string', format: 'uuid' }
          },
          {
            name: 'startDate',
            in: 'query',
            description: 'Data inicial (formato: YYYY-MM-DD)',
            schema: { type: 'string', format: 'date' }
          },
          {
            name: 'endDate',
            in: 'query',
            description: 'Data final (formato: YYYY-MM-DD)',
            schema: { type: 'string', format: 'date' }
          }
        ],
        responses: {
          '200': {
            description: 'Lista de transações',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Transaction' }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },
    '/transactions/{id}': {
      get: {
        tags: ['Transactions'],
        summary: 'Obter transação por ID',
        description: 'Retorna os detalhes de uma transação específica',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Detalhes da transação',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      put: {
        tags: ['Transactions'],
        summary: 'Atualizar transação',
        description: 'Atualiza uma transação existente',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  description: { type: 'string', minLength: 1 },
                  amount: { type: 'number', minimum: 0.01 },
                  transactionDate: { type: 'string', format: 'date' },
                  categoryId: { type: 'string', format: 'uuid', nullable: true }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Transação atualizada com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: { $ref: '#/components/schemas/Transaction' }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '400': {
            description: 'Dados inválidos',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      },
      delete: {
        tags: ['Transactions'],
        summary: 'Excluir transação',
        description: 'Exclui uma transação (soft delete)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'ID da transação',
            schema: { type: 'string', format: 'uuid' }
          }
        ],
        responses: {
          '200': {
            description: 'Transação excluída com sucesso',
            content: {
              'application/json': {
                schema: {
                  allOf: [
                    { $ref: '#/components/schemas/SuccessResponse' },
                    {
                      properties: {
                        data: {
                          type: 'object',
                          properties: {
                            message: { type: 'string', example: 'Transação excluída com sucesso' }
                          }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          '404': {
            description: 'Transação não encontrada',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          },
          '401': {
            description: 'Token de autenticação inválido',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

// HTML para Swagger UI
const swaggerUIHTML = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <title>Personal Finance API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .info .title { color: #2c3e50 }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: '/api-docs.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        persistAuthorization: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true
      });
    };
  </script>
</body>
</html>
`;

export const setupSwagger = (app: Express): void => {
  // Swagger UI HTML
  app.get('/api-docs', (req, res) => {
    res.setHeader('Content-Type', 'text/html');
    res.send(swaggerUIHTML);
  });

  // JSON endpoint para o spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.json(swaggerSpec);
  });

  console.log('📚 Swagger documentation available at /api-docs');
  console.log('📄 OpenAPI spec available at /api-docs.json');
};

export { swaggerSpec as specs };