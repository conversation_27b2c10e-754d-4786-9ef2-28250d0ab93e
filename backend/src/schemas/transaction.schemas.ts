import { z } from 'zod';
import { TransactionType } from '@prisma/client';

/**
 * Schema for installment data
 */
export const InstallmentSchema = z.object({
  amount: z.number()
    .positive('Valor da parcela deve ser positivo')
    .max(999999.99, 'Valor da parcela muito alto'),
  dueDate: z.string()
    .datetime('Data da parcela inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val),
  isPaid: z.boolean().default(false),
  description: z.string()
    .max(255, 'Descrição da parcela muito longa')
    .optional()
});

/**
 * Schema for creating a transaction with new architecture
 */
export const CreateTransactionSchema = z.object({
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(255, 'Descrição deve ter no máximo 255 caracteres')
    .trim(),
  totalAmount: z.number()
    .positive('Valor total deve ser positivo')
    .max(*********.99, 'Valor total muito alto')
    .transform(val => Number(val.toFixed(2))),
  transactionDate: z.string()
    .datetime('Data da transação inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val),
  type: z.nativeEnum(TransactionType),
  accountId: z.string()
    .cuid('ID da conta inválido'),
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  destinationAccountId: z.string()
    .cuid('ID da conta destino inválido')
    .optional(),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  isFuture: z.boolean().default(false),

  // Advanced transfer fields
  sourceCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  destinationCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  sourceAmount: z.number()
    .positive('Valor de origem deve ser positivo')
    .max(*********.99, 'Valor de origem muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  destinationAmount: z.number()
    .positive('Valor de destino deve ser positivo')
    .max(*********.99, 'Valor de destino muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  transferReference: z.string()
    .max(255, 'Referência da transferência deve ter no máximo 255 caracteres')
    .trim()
    .optional(),

  // New installments array
  installments: z.array(InstallmentSchema)
    .min(1, 'Pelo menos 1 parcela é obrigatória')
    .max(60, 'Máximo de 60 parcelas')
    .optional(),

  tagIds: z.array(z.string().cuid('ID da tag inválido'))
    .max(20, 'Máximo de 20 tags por transação')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(10, 'Máximo de 10 membros da família por transação')
    .optional()
}).refine((data) => {
  // Category is required for expenses
  if (data.type === TransactionType.EXPENSE && !data.categoryId) {
    return false;
  }
  return true;
}, {
  message: 'Categoria é obrigatória para despesas',
  path: ['categoryId']
}).refine((data) => {
  // Destination account is required for transfers
  if (data.type === TransactionType.TRANSFER && !data.destinationAccountId) {
    return false;
  }
  return true;
}, {
  message: 'Conta destino é obrigatória para transferências',
  path: ['destinationAccountId']
}).refine((data) => {
  // Validate installments sum equals total amount
  if (data.installments && data.installments.length > 0) {
    const installmentsSum = data.installments.reduce((sum, inst) => sum + inst.amount, 0);
    return Math.abs(installmentsSum - data.totalAmount) < 0.01;
  }
  return true;
}, {
  message: 'A soma das parcelas deve ser igual ao valor total',
  path: ['installments']
}).refine((data) => {
  // If no installments provided, create default single installment
  if (!data.installments || data.installments.length === 0) {
    data.installments = [{
      amount: data.totalAmount,
      dueDate: data.transactionDate,
      isPaid: false
    }];
  }
  return true;
});

/**
 * Schema for updating a transaction
 */
export const UpdateTransactionSchema = z.object({
  id: z.string().cuid('ID da transação inválido').optional(),
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(255, 'Descrição deve ter no máximo 255 caracteres')
    .trim()
    .optional(),
  totalAmount: z.number()
    .positive('Valor total deve ser positivo')
    .max(*********.99, 'Valor total muito alto')
    .transform(val => Number(val.toFixed(2)))
    .optional(),
  transactionDate: z.string()
    .datetime('Data da transação inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  type: z.nativeEnum(TransactionType).optional(),
  accountId: z.string()
    .cuid('ID da conta inválido')
    .optional(),
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  destinationAccountId: z.string()
    .cuid('ID da conta destino inválido')
    .optional(),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  isFuture: z.boolean().optional(),
  sourceCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  destinationCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  sourceAmount: z.number()
    .positive('Valor de origem deve ser positivo')
    .max(*********.99, 'Valor de origem muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  destinationAmount: z.number()
    .positive('Valor de destino deve ser positivo')
    .max(*********.99, 'Valor de destino muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  transferReference: z.string()
    .max(255, 'Referência da transferência deve ter no máximo 255 caracteres')
    .trim()
    .optional(),
  installments: z.array(InstallmentSchema)
    .min(1, 'Pelo menos 1 parcela é obrigatória')
    .max(60, 'Máximo de 60 parcelas')
    .optional(),
  tagIds: z.array(z.string().cuid('ID da tag inválido'))
    .max(20, 'Máximo de 20 tags por transação')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(10, 'Máximo de 10 membros da família por transação')
    .optional()
});

/**
 * Schema for transaction query filters
 */
export const TransactionFiltersSchema = z.object({
  description: z.string().optional(),
  type: z.nativeEnum(TransactionType).optional(),
  accountId: z.string().cuid('ID da conta inválido').optional(),
  categoryId: z.string().cuid('ID da categoria inválido').optional(),
  familyMemberId: z.string().cuid('ID de membro da família inválido').optional(),
  tagId: z.string().cuid('ID da tag inválido').optional(),
  startDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Data inicial deve estar no formato YYYY-MM-DD')
    .transform(val => new Date(val + 'T00:00:00.000Z'))
    .optional(),
  endDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Data final deve estar no formato YYYY-MM-DD')
    .transform(val => new Date(val + 'T23:59:59.999Z'))
    .optional(),
  minAmount: z.string()
    .transform(val => parseFloat(val))
    .refine(val => !isNaN(val) && val > 0, 'Valor mínimo deve ser um número positivo')
    .optional(),
  maxAmount: z.string()
    .transform(val => parseFloat(val))
    .refine(val => !isNaN(val) && val > 0, 'Valor máximo deve ser um número positivo')
    .optional(),
  isFuture: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeDeleted: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeRecurring: z.string()
    .transform(val => val === 'true')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional(),
  sortBy: z.enum(['transactionDate', 'totalAmount', 'description', 'createdAt'])
    .default('transactionDate')
    .optional(),
  sortOrder: z.enum(['asc', 'desc'])
    .default('desc')
    .optional()
});

/**
 * Type definitions
 */
export type InstallmentData = z.infer<typeof InstallmentSchema>;
export type CreateTransactionRequest = z.infer<typeof CreateTransactionSchema>;
export type UpdateTransactionRequest = z.infer<typeof UpdateTransactionSchema>;
export type TransactionFilters = z.infer<typeof TransactionFiltersSchema>;

/**
 * Transaction response type with new architecture
 */
export interface TransactionResponse {
  id: string;
  description: string;
  totalAmount: number;
  totalInstallments: number;
  transactionDate: string;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  destinationAccountId?: string;
  exchangeRate?: number;
  isFuture: boolean;
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  transferReference?: string;
  createdAt: string;
  updatedAt: string;
  
  // Installments (always present)
  installments: Array<{
    id: string;
    installmentNumber: number;
    amount: number;
    dueDate: string;
    isPaid: boolean;
    paidAt?: string;
    description?: string;
  }>;
  
  // Related data
  account?: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
  category?: {
    id: string;
    name: string;
    color?: string;
  };
  destinationAccount?: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
  tags?: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  familyMembers?: Array<{
    id: string;
    name: string;
    color: string;
  }>;
}

/**
 * Paginated transactions response
 */
export interface PaginatedTransactionsResponse {
  data: TransactionResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary?: {
    totalIncome: number;
    totalExpenses: number;
    totalTransfers: number;
    netAmount: number;
  };
}

/**
 * Schema for updating installments
 */
export const updateInstallmentsSchema = z.object({
  installments: z.array(InstallmentSchema)
    .min(1, 'Pelo menos 1 parcela é obrigatória')
    .max(60, 'Máximo de 60 parcelas')
});

/**
 * Transfer request type
 */
export interface TransferRequest extends CreateTransactionRequest {
  type: 'TRANSFER';
  destinationAccountId: string;
}

export type UpdateInstallmentsRequest = z.infer<typeof updateInstallmentsSchema>;
