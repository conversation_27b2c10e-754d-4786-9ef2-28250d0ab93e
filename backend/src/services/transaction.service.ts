import prisma from '../lib/prisma';
import { TransactionType } from '@prisma/client';
import {
  InstallmentSummary,
  InstallmentCancellationResult,
  InstallmentsByTransactionResult,
  MarkInstallmentPaidData,
  InstallmentStatusUpdate
} from '../types/installment.types';

export interface CreateTransactionData {
  description: string;
  totalAmount: number;
  transactionDate: string | Date;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  destinationAccountId?: string;
  exchangeRate?: number;
  isFuture?: boolean;
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  transferReference?: string;
  installments?: InstallmentData[];
  familyMemberIds?: string[];
  tagIds?: string[];
}

export interface InstallmentData {
  amount: number;
  dueDate: string | Date;
  isPaid?: boolean;
  description?: string;
}

export interface UpdateTransactionData extends Partial<CreateTransactionData> {
  id?: string;
}

export class TransactionService {
  /**
   * Create a new transaction with installments
   */
  async create(data: CreateTransactionData): Promise<any> {
    try {
      // 1. Validate inputs
      await this.validateInputs(data);

      // 2. Create main transaction
      const transaction = await prisma.transaction.create({
        data: {
          description: data.description,
          totalAmount: data.totalAmount,
          totalInstallments: data.installments?.length || 1,
          transactionDate: new Date(data.transactionDate),
          type: data.type,
          accountId: data.accountId,
          categoryId: data.categoryId,
          destinationAccountId: data.destinationAccountId,
          exchangeRate: data.exchangeRate,
          isFuture: data.isFuture || false,
          sourceCurrency: data.sourceCurrency,
          destinationCurrency: data.destinationCurrency,
          sourceAmount: data.sourceAmount,
          destinationAmount: data.destinationAmount,
          transferReference: data.transferReference
        }
      });

      // 3. Create installments
      const installments = data.installments || [{
        amount: data.totalAmount,
        dueDate: data.transactionDate,
        isPaid: false
      }];

      // Set automatic status based on due date
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      for (let i = 0; i < installments.length; i++) {
        const installmentData = installments[i];
        const dueDate = new Date(installmentData.dueDate);

        // Set isPaid to true if due date is today or in the past
        const shouldBePaid = dueDate <= today;
        const isPaid = installmentData.isPaid !== undefined ? installmentData.isPaid : shouldBePaid;

        await prisma.installment.create({
          data: {
            transactionId: transaction.id,
            installmentNumber: i + 1,
            amount: installmentData.amount,
            dueDate: dueDate,
            isPaid: isPaid,
            paidAt: isPaid ? dueDate : null,
            description: installmentData.description || `${data.description} - Parcela ${i + 1}/${installments.length}`
          }
        });
      }

      // 4. Add tags if provided
      if (data.tagIds && data.tagIds.length > 0) {
        await prisma.transactionTag.createMany({
          data: data.tagIds.map(tagId => ({
            transactionId: transaction.id,
            tagId
          }))
        });
      }

      // 5. Add family members if provided
      if (data.familyMemberIds && data.familyMemberIds.length > 0) {
        await prisma.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: transaction.id,
            familyMemberId
          }))
        });
      }

      // 6. Update account balances if not future
      if (!data.isFuture) {
        await this.updateAccountBalance(data.accountId, data.type, data.totalAmount);
        
        if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
          await this.updateAccountBalance(data.destinationAccountId, TransactionType.INCOME, data.totalAmount);
        }
      }

      // 7. Return the created transaction with details
      return await this.findById(transaction.id);
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw new Error('Failed to create transaction');
    }
  }

  /**
   * Update transaction with new installments (DELETE + CREATE strategy)
   */
  async update(id: string, data: UpdateTransactionData): Promise<any> {
    try {
      // 1. Validate inputs
      await this.validateInputs(data);

      // 2. Get existing transaction
      const existingTransaction = await prisma.transaction.findUnique({
        where: { id },
        include: { installments: true }
      });

      if (!existingTransaction) {
        throw new Error('Transação não encontrada');
      }

      // 3. Update main transaction
      await prisma.transaction.update({
        where: { id },
        data: {
          description: data.description,
          totalAmount: data.totalAmount,
          totalInstallments: data.installments?.length || 1,
          transactionDate: data.transactionDate ? new Date(data.transactionDate) : undefined,
          categoryId: data.categoryId,
          destinationAccountId: data.destinationAccountId,
          exchangeRate: data.exchangeRate,
          isFuture: data.isFuture,
          sourceCurrency: data.sourceCurrency,
          destinationCurrency: data.destinationCurrency,
          sourceAmount: data.sourceAmount,
          destinationAmount: data.destinationAmount,
          transferReference: data.transferReference
        }
      });

      // 4. DELETE all existing installments
      await prisma.installment.deleteMany({
        where: { transactionId: id }
      });

      // 5. DELETE existing relationships
      await prisma.transactionTag.deleteMany({
        where: { transactionId: id }
      });
      
      await prisma.transactionMember.deleteMany({
        where: { transactionId: id }
      });

      // 6. CREATE new installments
      const installments = data.installments || [{
        amount: data.totalAmount || Number(existingTransaction.totalAmount),
        dueDate: data.transactionDate || existingTransaction.transactionDate,
        isPaid: false
      }];

      // Set automatic status based on due date
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      for (let i = 0; i < installments.length; i++) {
        const installmentData = installments[i];
        const dueDate = new Date(installmentData.dueDate);

        // Set isPaid to true if due date is today or in the past
        const shouldBePaid = dueDate <= today;
        const isPaid = installmentData.isPaid !== undefined ? installmentData.isPaid : shouldBePaid;

        await prisma.installment.create({
          data: {
            transactionId: id,
            installmentNumber: i + 1,
            amount: installmentData.amount,
            dueDate: dueDate,
            isPaid: isPaid,
            paidAt: isPaid ? dueDate : null,
            description: installmentData.description || `${data.description || existingTransaction.description} - Parcela ${i + 1}/${installments.length}`
          }
        });
      }

      // 7. CREATE new relationships
      if (data.tagIds && data.tagIds.length > 0) {
        await prisma.transactionTag.createMany({
          data: data.tagIds.map(tagId => ({
            transactionId: id,
            tagId
          }))
        });
      }

      if (data.familyMemberIds && data.familyMemberIds.length > 0) {
        await prisma.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: id,
            familyMemberId
          }))
        });
      }

      // 8. Return the updated transaction with details
      return await this.findById(id);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw new Error('Failed to update transaction');
    }
  }

  /**
   * Get transaction by ID with all installments
   */
  async findById(id: string): Promise<any> {
    const transaction = await prisma.transaction.findUnique({
      where: { id, deletedAt: null },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        },
        account: true,
        category: true,
        destinationAccount: true,
        tags: {
          include: { tag: true }
        },
        members: {
          include: { familyMember: true }
        }
      }
    });

    return transaction;
  }

  /**
   * Delete transaction and all installments
   */
  async delete(id: string): Promise<void> {
    // Soft delete transaction
    await prisma.transaction.update({
      where: { id },
      data: { deletedAt: new Date() }
    });

    // Hard delete installments (cascade will handle this automatically)
    await prisma.installment.deleteMany({
      where: { transactionId: id }
    });
  }

  /**
   * Update installment status (paid/unpaid)
   */
  async updateInstallmentStatus(
    transactionId: string,
    installmentNumber: number,
    isPaid: boolean,
    paidAt?: string
  ): Promise<any> {
    try {
      // First, check if transaction exists
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId, deletedAt: null },
        include: { installments: true }
      });

      if (!transaction) {
        throw new Error('Transação não encontrada');
      }

      // Find the specific installment
      const installment = transaction.installments.find(
        inst => inst.installmentNumber === installmentNumber
      );

      if (!installment) {
        throw new Error(`Parcela ${installmentNumber} não encontrada`);
      }

      // Update the installment status
      await prisma.installment.update({
        where: { id: installment.id },
        data: {
          isPaid,
          paidAt: isPaid ? (paidAt ? new Date(paidAt) : new Date()) : null
        }
      });

      // Return the updated transaction with all installments
      return await this.findById(transactionId);
    } catch (error) {
      console.error('Error updating installment status:', error);
      throw new Error('Falha ao atualizar status da parcela');
    }
  }

  /**
   * Validate input data
   */
  private async validateInputs(data: Partial<CreateTransactionData>): Promise<void> {
    // Validate account exists
    if (data.accountId) {
      const account = await prisma.account.findFirst({
        where: { id: data.accountId, deletedAt: null }
      });
      if (!account) {
        throw new Error('Conta não encontrada');
      }
    }

    // Validate category exists
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });
      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Validate installments sum equals total amount
    if (data.installments && data.totalAmount) {
      const installmentsSum = data.installments.reduce((sum, inst) => sum + inst.amount, 0);
      if (Math.abs(installmentsSum - data.totalAmount) > 0.01) {
        throw new Error('A soma das parcelas deve ser igual ao valor total');
      }
    }
  }

  /**
   * Update account balance
   */
  private async updateAccountBalance(
    accountId: string,
    type: TransactionType,
    amount: number
  ): Promise<void> {
    const multiplier = type === TransactionType.INCOME ? 1 : -1;
    
    await prisma.account.update({
      where: { id: accountId },
      data: {
        currentBalance: {
          increment: amount * multiplier
        }
      }
    });
  }

  /**
   * Get installment summary for a transaction
   */
  async getInstallmentSummary(transactionId: string): Promise<InstallmentSummary> {
    try {
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId, deletedAt: null },
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          }
        }
      });

      if (!transaction) {
        throw new Error('Transação não encontrada');
      }

      const installments = transaction.installments;
      const paidInstallments = installments.filter(i => i.isPaid);
      const pendingInstallments = installments.filter(i => !i.isPaid);
      
      const totalAmount = Number(transaction.totalAmount);
      const paidAmount = paidInstallments.reduce((sum, i) => sum + Number(i.amount), 0);
      const remainingAmount = totalAmount - paidAmount;
      const progressPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

      // Find next installment
      const nextInstallment = pendingInstallments
        .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())[0];

      return {
        transactionId: transaction.id,
        totalInstallments: installments.length,
        paidInstallments: paidInstallments.length,
        pendingInstallments: pendingInstallments.length,
        totalAmount,
        paidAmount,
        remainingAmount,
        progressPercentage: Math.round(progressPercentage * 100) / 100,
        nextInstallmentDate: nextInstallment?.dueDate,
        nextInstallmentAmount: nextInstallment ? Number(nextInstallment.amount) : undefined
      };
    } catch (error) {
      console.error('Error getting installment summary:', error);
      throw new Error('Falha ao obter resumo das parcelas');
    }
  }

  /**
   * Cancel future installments (unpaid installments)
   */
  async cancelFutureInstallments(transactionId: string): Promise<InstallmentCancellationResult> {
    try {
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId, deletedAt: null },
        include: {
          installments: true
        }
      });

      if (!transaction) {
        throw new Error('Transação não encontrada');
      }

      // Find unpaid installments
      const unpaidInstallments = transaction.installments.filter(i => !i.isPaid);
      
      if (unpaidInstallments.length === 0) {
        return {
          cancelledInstallments: 0,
          message: 'Nenhuma parcela pendente encontrada para cancelar'
        };
      }

      // Delete unpaid installments
      await prisma.installment.deleteMany({
        where: {
          transactionId: transactionId,
          isPaid: false
        }
      });

      // Update transaction total installments
      const remainingInstallments = transaction.installments.filter(i => i.isPaid);
      await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          totalInstallments: remainingInstallments.length
        }
      });

      return {
        cancelledInstallments: unpaidInstallments.length,
        message: `${unpaidInstallments.length} parcela(s) futura(s) cancelada(s) com sucesso`
      };
    } catch (error) {
      console.error('Error cancelling future installments:', error);
      throw new Error('Falha ao cancelar parcelas futuras');
    }
  }

  /**
   * Mark specific installment as paid
   */
  async markInstallmentAsPaid(data: MarkInstallmentPaidData): Promise<InstallmentStatusUpdate> {
    try {
      const installment = await prisma.installment.findUnique({
        where: { id: data.installmentId },
        include: {
          transaction: true
        }
      });

      if (!installment) {
        throw new Error('Parcela não encontrada');
      }

      if (installment.isPaid) {
        throw new Error('Parcela já está marcada como paga');
      }

      const paidAt = data.paidAt ? new Date(data.paidAt) : new Date();

      await prisma.installment.update({
        where: { id: data.installmentId },
        data: {
          isPaid: true,
          paidAt: paidAt
        }
      });

      return {
        installmentId: data.installmentId,
        isPaid: true,
        paidAt: paidAt
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error; // Re-throw the original error to preserve the message
      }
      throw new Error('Falha ao marcar parcela como paga');
    }
  }

  /**
   * Get installments by transaction with summary
   */
  async getInstallmentsByTransaction(transactionId: string): Promise<InstallmentsByTransactionResult> {
    try {
      const transaction = await this.findById(transactionId);
      
      if (!transaction) {
        throw new Error('Transação não encontrada');
      }

      const summary = await this.getInstallmentSummary(transactionId);

      return {
        transaction,
        installments: transaction.installments,
        summary
      };
    } catch (error) {
      console.error('Error getting installments by transaction:', error);
      throw new Error('Falha ao obter parcelas da transação');
    }
  }

  /**
   * Get all transactions with installments for a specific account
   */
  async getTransactionsWithInstallments(accountId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    onlyWithPendingInstallments?: boolean;
  }): Promise<any[]> {
    try {
      const whereClause: any = {
        accountId,
        deletedAt: null,
        totalInstallments: {
          gt: 1 // Only transactions with multiple installments
        }
      };

      if (filters?.startDate || filters?.endDate) {
        whereClause.transactionDate = {};
        if (filters.startDate) {
          whereClause.transactionDate.gte = filters.startDate;
        }
        if (filters.endDate) {
          whereClause.transactionDate.lte = filters.endDate;
        }
      }

      const transactions = await prisma.transaction.findMany({
        where: whereClause,
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          account: true,
          category: true,
          destinationAccount: true,
          tags: {
            include: { tag: true }
          },
          members: {
            include: { familyMember: true }
          }
        },
        orderBy: { transactionDate: 'desc' }
      });

      // Filter by pending installments if requested
      if (filters?.onlyWithPendingInstallments) {
        return transactions.filter(transaction =>
          transaction.installments.some(installment => !installment.isPaid)
        );
      }

      return transactions;
    } catch (error) {
      console.error('Error getting transactions with installments:', error);
      throw new Error('Falha ao obter transações com parcelas');
    }
  }
}
