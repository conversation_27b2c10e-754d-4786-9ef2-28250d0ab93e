import { RecurrenceFrequency, TransactionType } from '@prisma/client';
import { scheduler, JobType, JobContext, JobResult } from '../config/scheduler.config';
import prisma from '../lib/prisma';

/**
 * Interface for job execution statistics
 */
interface JobExecutionStats {
  totalProcessed: number;
  successfulTransactions: number;
  failedTransactions: number;
  skippedTransactions: number;
  errors: string[];
}

/**
 * Service for managing recurring transaction jobs
 */
export class RecurringTransactionJobService {
  private isProcessing = false;

  /**
   * Initialize and register job functions
   */
  initialize(): void {
    // Register the recurring transaction job function
    scheduler.registerJobFunction(JobType.RECURRING_TRANSACTIONS, this.processRecurringTransactions.bind(this));
    
    console.log('[RecurringTransactionJobService] Job functions registered');
  }

  /**
   * Main job function to process all recurring transactions
   */
  async processRecurringTransactions(context: JobContext): Promise<JobResult> {
    const startTime = Date.now();
    
    try {
      if (this.isProcessing) {
        return {
          success: false,
          message: 'Job already running',
          duration: Date.now() - startTime
        };
      }

      this.isProcessing = true;
      console.log(`[RecurringTransactionJob] Starting job execution (attempt ${context.attempt}/${context.maxAttempts})`);

      const stats: JobExecutionStats = {
        totalProcessed: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        skippedTransactions: 0,
        errors: []
      };

      // Get all recurring transactions that should execute today
      const recurringTransactions = await this.getRecurringTransactionsForToday();
      stats.totalProcessed = recurringTransactions.length;

      console.log(`[RecurringTransactionJob] Found ${recurringTransactions.length} recurring transactions to process`);

      // Process each recurring transaction
      for (const recurringTransaction of recurringTransactions) {
        try {
          await this.processRecurringTransaction(recurringTransaction, stats);
        } catch (error) {
          console.error(`[RecurringTransactionJob] Error processing recurring transaction ${recurringTransaction.id}:`, error);
          stats.failedTransactions++;
          stats.errors.push(`${recurringTransaction.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const duration = Date.now() - startTime;
      const message = `Processed ${stats.totalProcessed} recurring transactions: ${stats.successfulTransactions} successful, ${stats.failedTransactions} failed, ${stats.skippedTransactions} skipped`;

      console.log(`[RecurringTransactionJob] Job completed in ${duration}ms: ${message}`);

      return {
        success: stats.failedTransactions === 0,
        message,
        data: stats,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('[RecurringTransactionJob] Job failed:', error);
      
      return {
        success: false,
        message: `Job failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error : new Error('Unknown error'),
        duration
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get recurring transactions that should execute today
   */
  private async getRecurringTransactionsForToday() {
    const today = new Date();

    return await prisma.recurringTransaction.findMany({
      where: {
        isActive: true,
        deletedAt: null,
        // For now, we'll process all active recurring transactions
        // In a real implementation, you'd add logic based on frequency and last execution
        frequency: {
          in: ['DAILY', 'WEEKLY', 'MONTHLY']
        }
      },
      include: {
        account: {
          select: {
            id: true,
            name: true,
            currentBalance: true
          }
        },
        category: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
  }

  /**
   * Process a single recurring transaction
   */
  private async processRecurringTransaction(
    recurringTransaction: any,
    stats: JobExecutionStats
  ): Promise<void> {
    console.log(`[RecurringTransactionJob] Processing recurring transaction: ${recurringTransaction.description}`);

    // Validate recurring transaction
    const isValid = await this.validateRecurringTransaction(recurringTransaction);
    if (!isValid) {
      stats.skippedTransactions++;
      return;
    }

    // Check for duplicate transactions
    const isDuplicate = await this.checkDuplicateTransaction(recurringTransaction);
    if (isDuplicate) {
      console.log(`[RecurringTransactionJob] Skipping ${recurringTransaction.id} - transaction already exists for today`);
      stats.skippedTransactions++;
      return;
    }

    // Calculate transaction amount
    const amount = await this.calculateTransactionAmount(recurringTransaction);
    if (amount <= 0) {
      console.warn(`[RecurringTransactionJob] Skipping ${recurringTransaction.id} - invalid amount: ${amount}`);
      stats.skippedTransactions++;
      return;
    }

    // Create the transaction using Prisma directly
    await prisma.transaction.create({
      data: {
        description: `${recurringTransaction.description} (Recorrente)`,
        totalAmount: amount,
        transactionDate: new Date(),
        type: recurringTransaction.type,
        accountId: recurringTransaction.accountId,
        categoryId: recurringTransaction.categoryId,
        isFuture: false
      }
    });

    // Update account balance
    await this.updateAccountBalance(recurringTransaction.accountId, recurringTransaction.type, amount);
    
    console.log(`[RecurringTransactionJob] Created transaction for ${recurringTransaction.description}: ${amount}`);
    stats.successfulTransactions++;
  }

  /**
   * Calculate transaction amount based on fixed or percentage amount
   */
  private async calculateTransactionAmount(recurringTransaction: any): Promise<number> {
    // If fixed amount, return it directly
    if (recurringTransaction.fixedAmount) {
      return Number(recurringTransaction.fixedAmount);
    }

    // If percentage amount, calculate based on current account balance
    if (recurringTransaction.percentageAmount) {
      const account = await prisma.account.findUnique({
        where: { id: recurringTransaction.accountId },
        select: { currentBalance: true }
      });

      if (!account) {
        throw new Error(`Account ${recurringTransaction.accountId} not found`);
      }

      const currentBalance = Number(account.currentBalance);
      const percentage = Number(recurringTransaction.percentageAmount) / 100;
      
      return Math.abs(currentBalance * percentage);
    }

    throw new Error('No amount specified (neither fixed nor percentage)');
  }

  /**
   * Update account balance based on transaction type
   */
  private async updateAccountBalance(accountId: string, transactionType: TransactionType, amount: number): Promise<void> {
    const account = await prisma.account.findUnique({
      where: { id: accountId },
      select: { currentBalance: true }
    });

    if (!account) {
      throw new Error(`Account ${accountId} not found`);
    }

    let balanceChange = 0;
    switch (transactionType) {
      case TransactionType.INCOME:
        balanceChange = amount;
        break;
      case TransactionType.EXPENSE:
        balanceChange = -amount;
        break;
      case TransactionType.TRANSFER:
        balanceChange = -amount; // Source account loses money
        break;
    }

    const currentBalance = Number(account.currentBalance);
    const newBalance = currentBalance + balanceChange;

    await prisma.account.update({
      where: { id: accountId },
      data: { currentBalance: newBalance }
    });

    console.log(`[RecurringTransactionJob] Updated account ${accountId} balance: ${currentBalance} → ${newBalance} (${balanceChange > 0 ? '+' : ''}${balanceChange})`);
  }

  /**
   * Validate recurring transaction before processing
   */
  private async validateRecurringTransaction(recurringTransaction: any): Promise<boolean> {
    try {
      // Check if account still exists and is active
      const account = await prisma.account.findFirst({
        where: { 
          id: recurringTransaction.accountId,
          deletedAt: null
        }
      });

      if (!account) {
        console.warn(`[RecurringTransactionJob] Account ${recurringTransaction.accountId} not found or deleted`);
        return false;
      }

      // Check if category still exists (if specified)
      if (recurringTransaction.categoryId) {
        const category = await prisma.category.findFirst({
          where: {
            id: recurringTransaction.categoryId,
            deletedAt: null
          }
        });

        if (!category) {
          console.warn(`[RecurringTransactionJob] Category ${recurringTransaction.categoryId} not found or deleted`);
          return false;
        }
      }

      // Check if recurring transaction is still active
      if (!recurringTransaction.isActive) {
        console.warn(`[RecurringTransactionJob] Recurring transaction ${recurringTransaction.id} is inactive`);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`[RecurringTransactionJob] Validation error for ${recurringTransaction.id}:`, error);
      return false;
    }
  }

  /**
   * Check if transaction already exists for today (prevent duplicates)
   */
  private async checkDuplicateTransaction(
    recurringTransaction: any,
    targetDate: Date = new Date()
  ): Promise<boolean> {
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        accountId: recurringTransaction.accountId,
        description: {
          contains: recurringTransaction.description
        },
        transactionDate: {
          gte: startOfDay,
          lte: endOfDay
        },
        deletedAt: null
      }
    });

    return existingTransaction !== null;
  }

  /**
   * Manually trigger processing of recurring transactions (for testing/admin)
   */
  async triggerManualExecution(targetDate?: Date): Promise<JobExecutionStats> {
    console.log('[RecurringTransactionJob] Manual execution triggered');
    
    const context: JobContext = {
      jobId: `manual_${Date.now()}`,
      name: 'Manual Recurring Transactions Processing',
      type: JobType.RECURRING_TRANSACTIONS,
      startTime: new Date(),
      attempt: 1,
      maxAttempts: 1
    };

    const result = await this.processRecurringTransactions(context);
    
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data as JobExecutionStats;
  }

  /**
   * Get job execution status
   */
  getExecutionStatus(): { isProcessing: boolean } {
    return { isProcessing: this.isProcessing };
  }
}

// Export singleton instance
export const recurringTransactionJobService = new RecurringTransactionJobService();
