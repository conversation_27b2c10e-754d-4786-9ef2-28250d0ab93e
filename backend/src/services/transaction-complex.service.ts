import prisma from '../lib/prisma';
import { Prisma, TransactionType } from '@prisma/client';

// New simplified interfaces for the new architecture
export interface CreateTransactionData {
  description: string;
  totalAmount: number;
  transactionDate: string | Date;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  destinationAccountId?: string;
  exchangeRate?: number;
  isFuture?: boolean;
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  transferReference?: string;
  installments?: InstallmentData[];
  familyMemberIds?: string[];
  tagIds?: string[];
}

export interface InstallmentData {
  amount: number;
  dueDate: string | Date;
  isPaid?: boolean;
  description?: string;
}

export interface UpdateTransactionData extends Partial<CreateTransactionData> {
  id?: string;
}

export interface TransactionWithInstallments {
  id: string;
  description: string;
  totalAmount: number;
  totalInstallments: number;
  transactionDate: Date;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  destinationAccountId?: string;
  exchangeRate?: number;
  isFuture: boolean;
  createdAt: Date;
  updatedAt: Date;
  installments: {
    id: string;
    installmentNumber: number;
    amount: number;
    dueDate: Date;
    isPaid: boolean;
    paidAt?: Date;
    description?: string;
  }[];
  account: any;
  category?: any;
  destinationAccount?: any;
  tags: any[];
  members: any[];
}

export class TransactionService {
  /**
   * Create a new transaction with installments (new architecture)
   */
  async create(data: CreateTransactionData): Promise<TransactionWithInstallments> {
    try {
      return await prisma.$transaction(async (tx) => {
        // 1. Validate inputs
        await this.validateInputs(data);

        // 2. Create main transaction
        const transaction = await tx.transaction.create({
          data: {
            description: data.description,
            totalAmount: data.totalAmount,
            totalInstallments: data.installments?.length || 1,
            transactionDate: new Date(data.transactionDate),
            type: data.type,
            accountId: data.accountId,
            categoryId: data.categoryId,
            destinationAccountId: data.destinationAccountId,
            exchangeRate: data.exchangeRate,
            isFuture: data.isFuture || false,
            sourceCurrency: data.sourceCurrency,
            destinationCurrency: data.destinationCurrency,
            sourceAmount: data.sourceAmount,
            destinationAmount: data.destinationAmount,
            transferReference: data.transferReference
          }
        });

        // 3. Create installments
        const installments = data.installments || [{
          amount: data.totalAmount,
          dueDate: data.transactionDate,
          isPaid: false
        }];

        for (let i = 0; i < installments.length; i++) {
          const installmentData = installments[i];
          await tx.installment.create({
            data: {
              transactionId: transaction.id,
              installmentNumber: i + 1,
              amount: installmentData.amount,
              dueDate: new Date(installmentData.dueDate),
              isPaid: installmentData.isPaid || false,
              paidAt: installmentData.isPaid ? new Date(installmentData.dueDate) : null,
              description: installmentData.description || `${data.description} - Parcela ${i + 1}/${installments.length}`
            }
          });
        }

        // 4. Add tags if provided
        if (data.tagIds && data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: transaction.id,
              tagId
            }))
          });
        }

        // 5. Add family members if provided
        if (data.familyMemberIds && data.familyMemberIds.length > 0) {
          await tx.transactionMember.createMany({
            data: data.familyMemberIds.map(familyMemberId => ({
              transactionId: transaction.id,
              familyMemberId
            }))
          });
        }

        // 6. Update account balances if not future
        if (!data.isFuture) {
          await this.updateAccountBalance(tx, data.accountId, data.type, data.totalAmount);
          
          if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
            await this.updateAccountBalance(tx, data.destinationAccountId, TransactionType.INCOME, data.totalAmount);
          }
        }

        const result = await this.findById(transaction.id);
        if (!result) {
          throw new Error('Falha ao criar transação');
        }
        return result;
      });
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw new Error('Failed to create transaction');
    }
  }

  /**
   * Update transaction with new installments (DELETE + CREATE strategy)
   */
  async update(id: string, data: UpdateTransactionData): Promise<TransactionWithInstallments> {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // 1. Validate inputs
        await this.validateInputs(data);

        // 2. Get existing transaction
        const existingTransaction = await tx.transaction.findUnique({
          where: { id },
          include: { installments: true }
        });

        if (!existingTransaction) {
          throw new Error('Transação não encontrada');
        }

        // 3. Update main transaction
        await tx.transaction.update({
          where: { id },
          data: {
            description: data.description,
            totalAmount: data.totalAmount,
            totalInstallments: data.installments?.length || 1,
            transactionDate: data.transactionDate ? new Date(data.transactionDate) : undefined,
            categoryId: data.categoryId,
            destinationAccountId: data.destinationAccountId,
            exchangeRate: data.exchangeRate,
            isFuture: data.isFuture,
            sourceCurrency: data.sourceCurrency,
            destinationCurrency: data.destinationCurrency,
            sourceAmount: data.sourceAmount,
            destinationAmount: data.destinationAmount,
            transferReference: data.transferReference
          }
        });

        // 4. DELETE all existing installments
        await tx.installment.deleteMany({
          where: { transactionId: id }
        });

        // 5. DELETE existing relationships
        await tx.transactionTag.deleteMany({
          where: { transactionId: id }
        });
        
        await tx.transactionMember.deleteMany({
          where: { transactionId: id }
        });

        // 6. CREATE new installments
        const installments = data.installments || [{
          amount: data.totalAmount || existingTransaction.totalAmount,
          dueDate: data.transactionDate || existingTransaction.transactionDate,
          isPaid: false
        }];

        for (let i = 0; i < installments.length; i++) {
          const installmentData = installments[i];
          await tx.installment.create({
            data: {
              transactionId: id,
              installmentNumber: i + 1,
              amount: installmentData.amount,
              dueDate: new Date(installmentData.dueDate),
              isPaid: installmentData.isPaid || false,
              paidAt: installmentData.isPaid ? new Date(installmentData.dueDate) : null,
              description: (installmentData as any).description || `${data.description || existingTransaction.description} - Parcela ${i + 1}/${installments.length}`
            }
          });
        }

        // 7. CREATE new relationships
        if (data.tagIds && data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: id,
              tagId
            }))
          });
        }

        if (data.familyMemberIds && data.familyMemberIds.length > 0) {
          await tx.transactionMember.createMany({
            data: data.familyMemberIds.map(familyMemberId => ({
              transactionId: id,
              familyMemberId
            }))
          });
        }

        const updatedTransaction = await this.findById(id);
        if (!updatedTransaction) {
          throw new Error('Falha ao atualizar transação');
        }
        return updatedTransaction;
      });
      return result;
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw new Error('Failed to update transaction');
    }
  }

  /**
   * Get transaction by ID with all installments
   */
  async findById(id: string): Promise<TransactionWithInstallments | null> {
    const transaction = await prisma.transaction.findUnique({
      where: { id, deletedAt: null },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        },
        account: true,
        category: true,
        destinationAccount: true,
        tags: {
          include: { tag: true }
        },
        members: {
          include: { familyMember: true }
        }
      }
    });

    if (!transaction) {
      return null;
    }
    
    // Convert Decimal fields to numbers for interface compatibility
    return {
      ...transaction,
      totalAmount: Number(transaction.totalAmount),
      totalInstallments: transaction.totalInstallments,
      exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
      sourceAmount: transaction.sourceAmount ? Number(transaction.sourceAmount) : undefined,
      destinationAmount: transaction.destinationAmount ? Number(transaction.destinationAmount) : undefined,
      installments: transaction.installments.map(inst => ({
        ...inst,
        amount: Number(inst.amount)
      })),
      tags: transaction.tags.map(t => t.tag),
      members: transaction.members.map(m => m.familyMember)
    } as TransactionWithInstallments;
  }

  /**
   * Delete transaction and all installments
   */
  async delete(id: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Soft delete transaction
      await tx.transaction.update({
        where: { id },
        data: { deletedAt: new Date() }
      });

      // Hard delete installments (cascade will handle this automatically)
      // But we can also explicitly delete for clarity
      await tx.installment.deleteMany({
        where: { transactionId: id }
      });
    });
  }

  /**
   * Validate input data
   */
  private async validateInputs(data: Partial<CreateTransactionData>): Promise<void> {
    // Validate account exists
    if (data.accountId) {
      const account = await prisma.account.findFirst({
        where: { id: data.accountId, deletedAt: null }
      });
      if (!account) {
        throw new Error('Conta não encontrada');
      }
    }

    // Validate category exists
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });
      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Validate installments sum equals total amount
    if (data.installments && data.totalAmount) {
      const installmentsSum = data.installments.reduce((sum, inst) => sum + inst.amount, 0);
      if (Math.abs(installmentsSum - data.totalAmount) > 0.01) {
        throw new Error('A soma das parcelas deve ser igual ao valor total');
      }
    }
  }

  /**
   * Update account balance
   */
  private async updateAccountBalance(
    tx: any,
    accountId: string,
    type: TransactionType,
    amount: number
  ): Promise<void> {
    const multiplier = type === TransactionType.INCOME ? 1 : -1;
    
    await tx.account.update({
      where: { id: accountId },
      data: {
        currentBalance: {
          increment: amount * multiplier
        }
      }
    });
  }
}
