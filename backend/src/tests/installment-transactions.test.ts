import { TransactionService } from '../services/transaction.service';
import { TransactionType } from '@prisma/client';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn()
    },
    installment: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn()
    },
    account: {
      findFirst: jest.fn(),
      update: jest.fn()
    },
    category: {
      findFirst: jest.fn()
    },
    transactionTag: {
      createMany: jest.fn(),
      deleteMany: jest.fn()
    },
    transactionMember: {
      createMany: jest.fn(),
      deleteMany: jest.fn()
    }
  }
}));

describe('TransactionService - Installments', () => {
  let transactionService: TransactionService;
  let mockPrisma: any;

  beforeEach(() => {
    transactionService = new TransactionService();
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();
  });

  describe('create with installments', () => {
    it('should create transaction with multiple installments', async () => {
      // Arrange
      const transactionData = {
        description: 'Compra parcelada',
        totalAmount: 1200.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        categoryId: 'cat-1',
        transactionDate: new Date('2024-02-01'),
        installments: [
          { amount: 400.00, dueDate: new Date('2024-02-01'), isPaid: false },
          { amount: 400.00, dueDate: new Date('2024-03-01'), isPaid: false },
          { amount: 400.00, dueDate: new Date('2024-04-01'), isPaid: false }
        ]
      };

      mockPrisma.account.findFirst.mockResolvedValue({ id: 'acc-1' });
      mockPrisma.category.findFirst.mockResolvedValue({ id: 'cat-1' });
      mockPrisma.transaction.create.mockResolvedValue({ id: 'trans-1' });
      mockPrisma.installment.create.mockResolvedValue({ id: 'inst-1' });
      mockPrisma.transaction.findUnique.mockResolvedValue({
        id: 'trans-1',
        installments: [
          { id: 'inst-1', installmentNumber: 1, amount: 400.00 },
          { id: 'inst-2', installmentNumber: 2, amount: 400.00 },
          { id: 'inst-3', installmentNumber: 3, amount: 400.00 }
        ]
      });

      // Act
      const result = await transactionService.create(transactionData);

      // Assert
      expect(mockPrisma.transaction.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          description: 'Compra parcelada',
          totalAmount: 1200.00,
          totalInstallments: 3,
          type: TransactionType.EXPENSE,
          accountId: 'acc-1',
          categoryId: 'cat-1'
        })
      });

      expect(mockPrisma.installment.create).toHaveBeenCalledTimes(3);
      expect(result).toBeDefined();
    });

    it('should create single installment when no installments array provided', async () => {
      // Arrange
      const transactionData = {
        description: 'Compra à vista',
        totalAmount: 500.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        transactionDate: new Date('2024-02-01')
      };

      mockPrisma.account.findFirst.mockResolvedValue({ id: 'acc-1' });
      mockPrisma.transaction.create.mockResolvedValue({ id: 'trans-1' });
      mockPrisma.installment.create.mockResolvedValue({ id: 'inst-1' });
      mockPrisma.transaction.findUnique.mockResolvedValue({
        id: 'trans-1',
        installments: [{ id: 'inst-1', installmentNumber: 1, amount: 500.00 }]
      });

      // Act
      const result = await transactionService.create(transactionData);

      // Assert
      expect(mockPrisma.transaction.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          totalInstallments: 1
        })
      });

      expect(mockPrisma.installment.create).toHaveBeenCalledTimes(1);
      expect(mockPrisma.installment.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          amount: 500.00,
          installmentNumber: 1
        })
      });
    });
  });

  describe('getInstallmentSummary', () => {
    it('should return correct installment summary', async () => {
      // Arrange
      const transactionId = 'trans-1';
      const mockTransaction = {
        id: 'trans-1',
        totalAmount: 1200,
        installments: [
          { id: 'inst-1', amount: 400, isPaid: true },
          { id: 'inst-2', amount: 400, isPaid: true },
          { id: 'inst-3', amount: 400, isPaid: false, dueDate: new Date('2024-04-01') }
        ]
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockTransaction);

      // Act
      const summary = await transactionService.getInstallmentSummary(transactionId);

      // Assert
      expect(summary).toEqual({
        transactionId: 'trans-1',
        totalInstallments: 3,
        paidInstallments: 2,
        pendingInstallments: 1,
        totalAmount: 1200,
        paidAmount: 800,
        remainingAmount: 400,
        progressPercentage: 66.67,
        nextInstallmentDate: new Date('2024-04-01'),
        nextInstallmentAmount: 400
      });
    });
  });

  describe('cancelFutureInstallments', () => {
    it('should cancel unpaid installments', async () => {
      // Arrange
      const transactionId = 'trans-1';
      const mockTransaction = {
        id: 'trans-1',
        installments: [
          { id: 'inst-1', isPaid: true },
          { id: 'inst-2', isPaid: false },
          { id: 'inst-3', isPaid: false }
        ]
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockTransaction);
      mockPrisma.installment.deleteMany.mockResolvedValue({ count: 2 });
      mockPrisma.transaction.update.mockResolvedValue({});

      // Act
      const result = await transactionService.cancelFutureInstallments(transactionId);

      // Assert
      expect(result.cancelledInstallments).toBe(2);
      expect(mockPrisma.installment.deleteMany).toHaveBeenCalledWith({
        where: {
          transactionId: 'trans-1',
          isPaid: false
        }
      });
    });
  });

  describe('markInstallmentAsPaid', () => {
    it('should mark installment as paid', async () => {
      // Arrange
      const installmentData = {
        installmentId: 'inst-1',
        paidAt: new Date('2024-02-15')
      };

      const mockInstallment = {
        id: 'inst-1',
        isPaid: false,
        transaction: { id: 'trans-1' }
      };

      mockPrisma.installment.findUnique.mockResolvedValue(mockInstallment);
      mockPrisma.installment.update.mockResolvedValue({
        id: 'inst-1',
        isPaid: true,
        paidAt: installmentData.paidAt
      });

      // Act
      const result = await transactionService.markInstallmentAsPaid(installmentData);

      // Assert
      expect(result).toEqual({
        installmentId: 'inst-1',
        isPaid: true,
        paidAt: installmentData.paidAt
      });

      expect(mockPrisma.installment.update).toHaveBeenCalledWith({
        where: { id: 'inst-1' },
        data: {
          isPaid: true,
          paidAt: installmentData.paidAt
        }
      });
    });

    it('should throw error if installment already paid', async () => {
      // Arrange
      const installmentData = {
        installmentId: 'inst-1'
      };

      const mockInstallment = {
        id: 'inst-1',
        isPaid: true,
        transaction: { id: 'trans-1' }
      };

      mockPrisma.installment.findUnique.mockResolvedValue(mockInstallment);

      // Act & Assert
      await expect(
        transactionService.markInstallmentAsPaid(installmentData)
      ).rejects.toThrow('Parcela já está marcada como paga');
    });
  });

  describe('getInstallmentsByTransaction', () => {
    it('should return transaction with installments and summary', async () => {
      // Arrange
      const transactionId = 'trans-1';
      const mockTransaction = {
        id: 'trans-1',
        totalAmount: 600,
        installments: [
          { id: 'inst-1', amount: 300, isPaid: true },
          { id: 'inst-2', amount: 300, isPaid: false, dueDate: new Date('2024-03-01') }
        ]
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockTransaction);

      // Act
      const result = await transactionService.getInstallmentsByTransaction(transactionId);

      // Assert
      expect(result.transaction).toEqual(mockTransaction);
      expect(result.installments).toEqual(mockTransaction.installments);
      expect(result.summary).toEqual(expect.objectContaining({
        transactionId: 'trans-1',
        totalInstallments: 2,
        paidInstallments: 1,
        pendingInstallments: 1
      }));
    });
  });

  describe('getTransactionsWithInstallments', () => {
    it('should return transactions with multiple installments', async () => {
      // Arrange
      const accountId = 'acc-1';
      const mockTransactions = [
        {
          id: 'trans-1',
          totalInstallments: 3,
          installments: [
            { id: 'inst-1', isPaid: true },
            { id: 'inst-2', isPaid: false },
            { id: 'inst-3', isPaid: false }
          ]
        }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);

      // Act
      const result = await transactionService.getTransactionsWithInstallments(accountId);

      // Assert
      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          accountId: 'acc-1',
          deletedAt: null,
          totalInstallments: { gt: 1 }
        },
        include: expect.objectContaining({
          installments: { orderBy: { installmentNumber: 'asc' } }
        }),
        orderBy: { transactionDate: 'desc' }
      });

      expect(result).toEqual(mockTransactions);
    });

    it('should filter by pending installments when requested', async () => {
      // Arrange
      const accountId = 'acc-1';
      const filters = { onlyWithPendingInstallments: true };
      const mockTransactions = [
        {
          id: 'trans-1',
          installments: [
            { id: 'inst-1', isPaid: true },
            { id: 'inst-2', isPaid: false }
          ]
        },
        {
          id: 'trans-2',
          installments: [
            { id: 'inst-3', isPaid: true },
            { id: 'inst-4', isPaid: true }
          ]
        }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);

      // Act
      const result = await transactionService.getTransactionsWithInstallments(accountId, filters);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('trans-1');
    });
  });
});
