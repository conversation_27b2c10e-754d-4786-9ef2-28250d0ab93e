/**
 * User Factory for Test Data Generation
 */

import { BaseFactory } from './base.factory';
import { faker } from '@faker-js/faker';
import bcrypt from 'bcrypt';

export interface TestUser {
  id: string;
  email: string;
  name: string;
  password: string;
  hashedPassword: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  preferences?: {
    currency: string;
    language: string;
    timezone: string;
    theme: string;
  };
  profile?: {
    avatar?: string;
    bio?: string;
    phone?: string;
    dateOfBirth?: Date;
  };
}

export class UserFactory extends BaseFactory<TestUser> {
  protected definition(): Partial<TestUser> {
    const password = 'password123';
    const hashedPassword = bcrypt.hashSync(password, 10);
    
    return {
      id: this.generateId(),
      email: this.generateEmail(),
      name: this.generateName(),
      password,
      hashedPassword,
      isActive: true,
      emailVerified: true,
      createdAt: this.generatePastTimestamp(),
      updatedAt: this.generateTimestamp(),
      lastLoginAt: this.generateTimestamp(),
      preferences: {
        currency: 'BRL',
        language: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        theme: 'dark',
      },
      profile: {
        avatar: faker.image.avatar(),
        bio: faker.lorem.paragraph(),
        phone: faker.phone.number(),
        dateOfBirth: faker.date.birthdate({ min: 18, max: 80, mode: 'age' }),
      },
    };
  }

  protected getVariant(variantName: string): Partial<TestUser> {
    switch (variantName) {
      case 'inactive':
        return {
          isActive: false,
          lastLoginAt: this.generatePastTimestamp(),
        };
      
      case 'unverified':
        return {
          emailVerified: false,
          lastLoginAt: undefined,
        };
      
      case 'admin':
        return {
          email: '<EMAIL>',
          name: 'Admin User',
          isActive: true,
          emailVerified: true,
        };
      
      case 'premium':
        return {
          preferences: {
            currency: 'USD',
            language: 'en-US',
            timezone: 'America/New_York',
            theme: 'light',
          },
        };
      
      case 'brazilian':
        return {
          name: faker.person.fullName(),
          preferences: {
            currency: 'BRL',
            language: 'pt-BR',
            timezone: 'America/Sao_Paulo',
            theme: 'dark',
          },
          profile: {
            phone: this.generateBrazilianPhone(),
          },
        };
      
      default:
        return {};
    }
  }

  /**
   * Create user with specific email
   */
  withEmail(email: string): TestUser {
    return this.create({ email });
  }

  /**
   * Create user with specific name
   */
  withName(name: string): TestUser {
    return this.create({ name });
  }

  /**
   * Create user with specific currency preference
   */
  withCurrency(currency: string): TestUser {
    return this.create({
      preferences: {
        currency,
        language: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        theme: 'dark',
      },
    });
  }

  /**
   * Create multiple users with different roles
   */
  createUserSet(): {
    admin: TestUser;
    regular: TestUser;
    inactive: TestUser;
    unverified: TestUser;
  } {
    return {
      admin: this.variant('admin'),
      regular: this.create(),
      inactive: this.variant('inactive'),
      unverified: this.variant('unverified'),
    };
  }

  /**
   * Create family of users
   */
  createFamily(size: number = 3): TestUser[] {
    const familyName = faker.person.lastName();
    return Array.from({ length: size }, (_, index) => {
      const firstName = faker.person.firstName();
      return this.create({
        name: `${firstName} ${familyName}`,
        email: `${firstName.toLowerCase()}.${familyName.toLowerCase()}@example.com`,
      });
    });
  }

  private generateBrazilianPhone(): string {
    const areaCode = faker.helpers.arrayElement(['11', '21', '31', '41', '51']);
    const number = faker.string.numeric(9);
    return `(${areaCode}) ${number.slice(0, 5)}-${number.slice(5)}`;
  }
}

// Export singleton instance
export const userFactory = new UserFactory();
