/**
 * User Seeder for Test Database Population
 */

import { BaseSeeder, SeederOptions } from './base.seeder';
import { userFactory, TestUser } from '../factories/user.factory';
import { PrismaClient } from '@prisma/client';

export class UserSeeder extends BaseSeeder {
  private createdUserIds: string[] = [];

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  getName(): string {
    return 'UserSeeder';
  }

  async run(options: SeederOptions = {}): Promise<TestUser[]> {
    this.ensureTestEnvironment();
    
    const { clean = false, count = 10 } = options;
    
    if (clean) {
      await this.cleanup();
    }

    this.log(`Creating ${count} test users...`);

    try {
      const users: TestUser[] = [];

      // Create admin user
      const adminUser = userFactory.variant('admin', {
        email: '<EMAIL>',
        name: 'Test Admin',
      });

      const createdAdmin = await this.prisma.user.create({
        data: adminUser as any,
      });

      users.push(createdAdmin as any);
      this.createdUserIds.push(createdAdmin.id);

      // Create regular test users
      for (let i = 0; i < count - 1; i++) {
        const userData = userFactory.create({
          email: `user${i + 1}@test.com`,
          name: `Test User ${i + 1}`,
        });

        const createdUser = await this.prisma.user.create({
          data: userData as any,
        });

        users.push(createdUser as any);
        this.createdUserIds.push(createdUser.id);
      }

      // Create some variant users
      const variants = ['inactive', 'unverified', 'brazilian'];
      for (const variant of variants) {
        const userData = userFactory.variant(variant, {
          email: `${variant}@test.com`,
          name: `Test ${variant.charAt(0).toUpperCase() + variant.slice(1)} User`,
        });

        const createdUser = await this.prisma.user.create({
          data: userData as any,
        });

        users.push(createdUser as any);
        this.createdUserIds.push(createdUser.id);
      }

      this.log(`Successfully created ${users.length} users`);
      return users;

    } catch (error) {
      this.logError('Failed to create users', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    this.log('Cleaning up test users...');

    try {
      if (this.createdUserIds.length > 0) {
        await this.prisma.user.deleteMany({
          where: {
            id: {
              in: this.createdUserIds,
            },
          },
        });
        this.createdUserIds = [];
      }

      // Also clean up users with test emails
      await this.prisma.user.deleteMany({
        where: {
          OR: [
            { email: { endsWith: '@test.com' } },
            { email: { contains: 'test_' } },
            { name: { contains: 'Test ' } },
          ],
        },
      });

      this.log('User cleanup completed');
    } catch (error) {
      this.logError('Failed to cleanup users', error);
      throw error;
    }
  }

  /**
   * Create a specific user for testing
   */
  async createTestUser(userData: Partial<TestUser>): Promise<TestUser> {
    this.ensureTestEnvironment();

    const user = userFactory.create(userData);

    try {
      const createdUser = await this.prisma.user.create({
        data: user as any,
      });

      this.createdUserIds.push(createdUser.id);
      return createdUser as any;

    } catch (error) {
      this.logError('Failed to create test user', error);
      throw error;
    }
  }

  /**
   * Get created user IDs
   */
  getCreatedUserIds(): string[] {
    return [...this.createdUserIds];
  }

  /**
   * Create user family for testing
   */
  async createUserFamily(size: number = 3): Promise<TestUser[]> {
    this.ensureTestEnvironment();

    const familyUsers = userFactory.createFamily(size);
    const createdUsers: TestUser[] = [];

    try {
      for (const userData of familyUsers) {
        const createdUser = await this.prisma.user.create({
          data: userData as any,
        });

        createdUsers.push(createdUser as any);
        this.createdUserIds.push(createdUser.id);
      }

      this.log(`Created family of ${createdUsers.length} users`);
      return createdUsers;

    } catch (error) {
      this.logError('Failed to create user family', error);
      throw error;
    }
  }
}
