/**
 * Account Seeder for Test Database Population
 */

import { BaseSeeder, SeederOptions } from './base.seeder';
import { accountFactory, TestAccount } from '../factories/account.factory';
import { PrismaClient } from '@prisma/client';

export class AccountSeeder extends BaseSeeder {
  private createdAccountIds: string[] = [];

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  getName(): string {
    return 'AccountSeeder';
  }

  async run(options: SeederOptions = {}): Promise<TestAccount[]> {
    this.ensureTestEnvironment();
    
    const { clean = false, count = 5 } = options;
    
    if (clean) {
      await this.cleanup();
    }

    // Get existing users to create accounts for
    const users = await this.prisma.user.findMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { email: { contains: 'test_' } },
        ],
      },
      take: 10,
    });

    if (users.length === 0) {
      this.log('No test users found. Please run UserSeeder first.');
      return [];
    }

    this.log(`Creating accounts for ${users.length} users...`);

    try {
      const accounts: TestAccount[] = [];

      for (const user of users) {
        // Create a complete account set for each user
        const accountSet = accountFactory.createAccountSet(user.id);
        
        for (const [type, accountData] of Object.entries(accountSet)) {
          const createdAccount = await this.prisma.account.create({
            data: accountData as any,
          });

          accounts.push(createdAccount as any);
          this.createdAccountIds.push(createdAccount.id);
        }

        // Create additional random accounts
        const additionalAccounts = accountFactory.createMany(count, { userId: user.id });
        
        for (const accountData of additionalAccounts) {
          const createdAccount = await this.prisma.account.create({
            data: accountData as any,
          });

          accounts.push(createdAccount as any);
          this.createdAccountIds.push(createdAccount.id);
        }
      }

      this.log(`Successfully created ${accounts.length} accounts`);
      return accounts;

    } catch (error) {
      this.logError('Failed to create accounts', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    this.log('Cleaning up test accounts...');

    try {
      if (this.createdAccountIds.length > 0) {
        await this.prisma.account.deleteMany({
          where: {
            id: {
              in: this.createdAccountIds,
            },
          },
        });
        this.createdAccountIds = [];
      }

      // Also clean up accounts with test patterns
      await this.prisma.account.deleteMany({
        where: {
          OR: [
            { name: { contains: 'test_' } },
            { name: { contains: 'test_data' } },
          ],
        },
      });

      this.log('Account cleanup completed');
    } catch (error) {
      this.logError('Failed to cleanup accounts', error);
      throw error;
    }
  }

  /**
   * Create accounts for specific user
   */
  async createAccountsForUser(userId: string, count: number = 3): Promise<TestAccount[]> {
    this.ensureTestEnvironment();

    try {
      const accounts: TestAccount[] = [];
      
      // Create account set
      const accountSet = accountFactory.createAccountSet(userId);
      
      for (const [type, accountData] of Object.entries(accountSet)) {
        const createdAccount = await this.prisma.account.create({
          data: accountData as any,
        });

        accounts.push(createdAccount as any);
        this.createdAccountIds.push(createdAccount.id);
      }

      this.log(`Created ${accounts.length} accounts for user ${userId}`);
      return accounts;

    } catch (error) {
      this.logError('Failed to create accounts for user', error);
      throw error;
    }
  }

  /**
   * Create accounts with specific balance distribution
   */
  async createBalanceVarietyAccounts(userId: string): Promise<TestAccount[]> {
    this.ensureTestEnvironment();

    try {
      const accounts = accountFactory.createBalanceVariety(userId, 5);
      const createdAccounts: TestAccount[] = [];

      for (const accountData of accounts) {
        const createdAccount = await this.prisma.account.create({
          data: accountData as any,
        });

        createdAccounts.push(createdAccount as any);
        this.createdAccountIds.push(createdAccount.id);
      }

      this.log(`Created ${createdAccounts.length} accounts with balance variety`);
      return createdAccounts;

    } catch (error) {
      this.logError('Failed to create balance variety accounts', error);
      throw error;
    }
  }

  /**
   * Get created account IDs
   */
  getCreatedAccountIds(): string[] {
    return [...this.createdAccountIds];
  }

  /**
   * Get accounts by user ID
   */
  async getAccountsByUser(userId: string): Promise<TestAccount[]> {
    try {
      const accounts = await this.prisma.account.findMany({
        where: { name: { contains: 'test_' } },
      });

      return accounts as any[];
    } catch (error) {
      this.logError('Failed to get accounts by user', error);
      throw error;
    }
  }
}
