/**
 * Transaction Seeder for Test Database Population
 */

import { BaseSeeder, SeederOptions } from './base.seeder';
import { transactionFactory, TestTransaction } from '../factories/transaction.factory';
import { PrismaClient } from '@prisma/client';

export class TransactionSeeder extends BaseSeeder {
  private createdTransactionIds: string[] = [];

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  getName(): string {
    return 'TransactionSeeder';
  }

  async run(options: SeederOptions = {}): Promise<TestTransaction[]> {
    this.ensureTestEnvironment();
    
    const { clean = false, count = 100 } = options;
    
    if (clean) {
      await this.cleanup();
    }

    // Get existing users, accounts, and categories
    const users = await this.prisma.user.findMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { email: { contains: 'test_' } },
        ],
      },
    });

    if (users.length === 0) {
      this.log('No test users found. Please run UserSeeder first.');
      return [];
    }

    this.log(`Creating transactions for ${users.length} users...`);

    try {
      const transactions: TestTransaction[] = [];

      for (const user of users) {
        const accounts = await this.prisma.account.findMany();
        if (accounts.length === 0) {
          this.log(`User ${user.email} has no accounts. Skipping.`);
          continue;
        }

        const categories = await this.prisma.category.findMany();
        if (categories.length === 0) {
          this.log(`User ${user.email} has no categories. Skipping.`);
          continue;
        }

        // Create transaction history for each account
        for (const account of accounts) {
          // Create balanced transaction set
          const balancedTransactions = transactionFactory.createBalancedSet(
            account.id,
            user.id,
            Math.floor(count / accounts.length)
          );

          for (const transactionData of balancedTransactions) {
            // Assign random category
            const randomCategory = categories[
              Math.floor(Math.random() * categories.length)
            ];

            const createdTransaction = await this.prisma.transaction.create({
              data: transactionData as any,
            });

            transactions.push(createdTransaction as any);
            this.createdTransactionIds.push(createdTransaction.id);
          }

          // Create some recurring transactions
          const recurringTransactions = this.createRecurringTransactions(
            account.id,
            user.id,
            categories,
            3
          );

          for (const transactionData of recurringTransactions) {
            const createdTransaction = await this.prisma.transaction.create({
              data: transactionData as any,
            });

            transactions.push(createdTransaction as any);
            this.createdTransactionIds.push(createdTransaction.id);
          }
        }
      }

      this.log(`Successfully created ${transactions.length} transactions`);
      return transactions;

    } catch (error) {
      this.logError('Failed to create transactions', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    this.log('Cleaning up test transactions...');

    try {
      if (this.createdTransactionIds.length > 0) {
        await this.prisma.transaction.deleteMany({
          where: {
            id: {
              in: this.createdTransactionIds,
            },
          },
        });
        this.createdTransactionIds = [];
      }

      // Also clean up transactions with test patterns
      await this.prisma.transaction.deleteMany({
        where: {
          OR: [
            { description: { contains: 'test_' } },
            { description: { contains: 'test_data' } },
          ],
        },
      });

      this.log('Transaction cleanup completed');
    } catch (error) {
      this.logError('Failed to cleanup transactions', error);
      throw error;
    }
  }

  /**
   * Create transactions for specific account
   */
  async createTransactionsForAccount(
    accountId: string,
    userId: string,
    categoryIds: string[],
    count: number = 20
  ): Promise<TestTransaction[]> {
    this.ensureTestEnvironment();

    try {
      const transactions: TestTransaction[] = [];
      
      // Create transaction history
      const transactionHistory = transactionFactory.createTransactionHistory(
        accountId,
        userId,
        90 // 90 days
      );

      for (const transactionData of transactionHistory.slice(0, count)) {
        // Assign random category
        const randomCategoryId = categoryIds[
          Math.floor(Math.random() * categoryIds.length)
        ];

        const createdTransaction = await this.prisma.transaction.create({
          data: transactionData as any,
        });

        transactions.push(createdTransaction as any);
        this.createdTransactionIds.push(createdTransaction.id);
      }

      this.log(`Created ${transactions.length} transactions for account ${accountId}`);
      return transactions;

    } catch (error) {
      this.logError('Failed to create transactions for account', error);
      throw error;
    }
  }

  /**
   * Create recurring transactions
   */
  private createRecurringTransactions(
    accountId: string,
    userId: string,
    categories: any[],
    count: number
  ): TestTransaction[] {
    const transactions: TestTransaction[] = [];

    for (let i = 0; i < count; i++) {
      const randomCategory = categories[Math.floor(Math.random() * categories.length)];
      
      const transaction = transactionFactory.variant('recurring_monthly', {
        accountId,
        userId,
        categoryId: randomCategory.id,
      });

      transactions.push(transaction);
    }

    return transactions;
  }

  /**
   * Create monthly transaction pattern
   */
  async createMonthlyPattern(
    accountId: string,
    userId: string,
    categoryId: string,
    months: number = 12
  ): Promise<TestTransaction[]> {
    this.ensureTestEnvironment();

    try {
      const transactions: TestTransaction[] = [];
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const monthlyTransactions = transactionFactory.createMonthlyTransactions(
        accountId,
        userId,
        categoryId,
        startDate
      );

      for (const transactionData of monthlyTransactions) {
        const createdTransaction = await this.prisma.transaction.create({
          data: transactionData as any,
        });

        transactions.push(createdTransaction as any);
        this.createdTransactionIds.push(createdTransaction.id);
      }

      this.log(`Created ${transactions.length} monthly transactions`);
      return transactions;

    } catch (error) {
      this.logError('Failed to create monthly pattern', error);
      throw error;
    }
  }

  /**
   * Get created transaction IDs
   */
  getCreatedTransactionIds(): string[] {
    return [...this.createdTransactionIds];
  }

  /**
   * Get transactions by account
   */
  async getTransactionsByAccount(accountId: string): Promise<TestTransaction[]> {
    try {
      const transactions = await this.prisma.transaction.findMany({
        where: { accountId },
        orderBy: { createdAt: 'desc' },
      });

      return transactions as any[];
    } catch (error) {
      this.logError('Failed to get transactions by account', error);
      throw error;
    }
  }
}
