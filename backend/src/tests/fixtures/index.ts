/**
 * Test Fixtures - Static Test Data
 * Provides consistent, reusable test data for various scenarios
 */

export interface TestFixture<T> {
  name: string;
  description: string;
  data: T;
  dependencies?: string[];
}

/**
 * User Fixtures
 */
export const userFixtures = {
  admin: {
    name: 'admin',
    description: 'Administrator user for testing',
    data: {
      id: 'fixture-admin-user-id',
      email: '<EMAIL>',
      name: 'Admin User',
      password: 'admin123',
      isActive: true,
      emailVerified: true,
      preferences: {
        currency: 'BRL',
        language: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        theme: 'dark',
      },
    },
  },
  
  regularUser: {
    name: 'regularUser',
    description: 'Regular user for testing',
    data: {
      id: 'fixture-regular-user-id',
      email: '<EMAIL>',
      name: 'Regular User',
      password: 'user123',
      isActive: true,
      emailVerified: true,
      preferences: {
        currency: 'BRL',
        language: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        theme: 'light',
      },
    },
  },

  inactiveUser: {
    name: 'inactiveUser',
    description: 'Inactive user for testing',
    data: {
      id: 'fixture-inactive-user-id',
      email: '<EMAIL>',
      name: 'Inactive User',
      password: 'inactive123',
      isActive: false,
      emailVerified: true,
    },
  },
};

/**
 * Account Fixtures
 */
export const accountFixtures = {
  checkingAccount: {
    name: 'checkingAccount',
    description: 'Checking account for testing',
    data: {
      id: 'fixture-checking-account-id',
      name: 'Conta Corrente Principal',
      type: 'CHECKING',
      currency: 'BRL',
      balance: 5000.00,
      isActive: true,
      userId: 'fixture-regular-user-id',
      bankName: 'Banco Fixture',
      accountNumber: '12345-6',
      routingNumber: '001',
      color: '#4ECDC4',
      icon: 'credit-card',
    },
    dependencies: ['regularUser'],
  },

  savingsAccount: {
    name: 'savingsAccount',
    description: 'Savings account for testing',
    data: {
      id: 'fixture-savings-account-id',
      name: 'Poupança',
      type: 'SAVINGS',
      currency: 'BRL',
      balance: 15000.00,
      isActive: true,
      userId: 'fixture-regular-user-id',
      bankName: 'Banco Fixture',
      accountNumber: '54321-9',
      routingNumber: '001',
      color: '#96CEB4',
      icon: 'piggy-bank',
    },
    dependencies: ['regularUser'],
  },

  creditCard: {
    name: 'creditCard',
    description: 'Credit card for testing',
    data: {
      id: 'fixture-credit-card-id',
      name: 'Cartão de Crédito',
      type: 'CREDIT_CARD',
      currency: 'BRL',
      balance: -1200.00,
      creditLimit: 5000.00,
      isActive: true,
      userId: 'fixture-regular-user-id',
      bankName: 'Banco Fixture',
      accountNumber: '****-1234',
      color: '#FF6B6B',
      icon: 'credit-card',
    },
    dependencies: ['regularUser'],
  },
};

/**
 * Category Fixtures
 */
export const categoryFixtures = {
  foodExpense: {
    name: 'foodExpense',
    description: 'Food expense category',
    data: {
      id: 'fixture-food-category-id',
      name: 'Alimentação',
      type: 'EXPENSE',
      color: '#FF6B6B',
      icon: 'utensils',
      description: 'Gastos com alimentação',
      userId: 'fixture-regular-user-id',
      isActive: true,
    },
    dependencies: ['regularUser'],
  },

  salaryIncome: {
    name: 'salaryIncome',
    description: 'Salary income category',
    data: {
      id: 'fixture-salary-category-id',
      name: 'Salário',
      type: 'INCOME',
      color: '#00B894',
      icon: 'briefcase',
      description: 'Renda do trabalho',
      userId: 'fixture-regular-user-id',
      isActive: true,
    },
    dependencies: ['regularUser'],
  },

  transportExpense: {
    name: 'transportExpense',
    description: 'Transport expense category',
    data: {
      id: 'fixture-transport-category-id',
      name: 'Transporte',
      type: 'EXPENSE',
      color: '#4ECDC4',
      icon: 'car',
      description: 'Gastos com transporte',
      userId: 'fixture-regular-user-id',
      isActive: true,
    },
    dependencies: ['regularUser'],
  },
};

/**
 * Transaction Fixtures
 */
export const transactionFixtures = {
  salaryTransaction: {
    name: 'salaryTransaction',
    description: 'Monthly salary transaction',
    data: {
      id: 'fixture-salary-transaction-id',
      description: 'Salário Mensal',
      amount: 5000.00,
      type: 'INCOME',
      date: new Date('2024-01-01'),
      accountId: 'fixture-checking-account-id',
      categoryId: 'fixture-salary-category-id',
      userId: 'fixture-regular-user-id',
      isRecurring: true,
      recurringRule: {
        frequency: 'MONTHLY',
        interval: 1,
      },
      status: 'COMPLETED',
    },
    dependencies: ['regularUser', 'checkingAccount', 'salaryIncome'],
  },

  groceryExpense: {
    name: 'groceryExpense',
    description: 'Grocery shopping expense',
    data: {
      id: 'fixture-grocery-transaction-id',
      description: 'Supermercado',
      amount: -150.00,
      type: 'EXPENSE',
      date: new Date('2024-01-02'),
      accountId: 'fixture-checking-account-id',
      categoryId: 'fixture-food-category-id',
      userId: 'fixture-regular-user-id',
      tags: ['supermercado', 'alimentação'],
      location: 'São Paulo, SP',
      status: 'COMPLETED',
    },
    dependencies: ['regularUser', 'checkingAccount', 'foodExpense'],
  },

  gasExpense: {
    name: 'gasExpense',
    description: 'Gas station expense',
    data: {
      id: 'fixture-gas-transaction-id',
      description: 'Combustível',
      amount: -80.00,
      type: 'EXPENSE',
      date: new Date('2024-01-03'),
      accountId: 'fixture-checking-account-id',
      categoryId: 'fixture-transport-category-id',
      userId: 'fixture-regular-user-id',
      tags: ['combustível', 'carro'],
      status: 'COMPLETED',
    },
    dependencies: ['regularUser', 'checkingAccount', 'transportExpense'],
  },

  transferTransaction: {
    name: 'transferTransaction',
    description: 'Transfer between accounts',
    data: {
      id: 'fixture-transfer-transaction-id',
      description: 'Transferência para Poupança',
      amount: 1000.00,
      type: 'TRANSFER',
      date: new Date('2024-01-05'),
      accountId: 'fixture-checking-account-id',
      transferAccountId: 'fixture-savings-account-id',
      userId: 'fixture-regular-user-id',
      status: 'COMPLETED',
    },
    dependencies: ['regularUser', 'checkingAccount', 'savingsAccount'],
  },
};

/**
 * Complete Test Scenarios
 */
export const testScenarios = {
  basicUserWithAccounts: {
    name: 'basicUserWithAccounts',
    description: 'Basic user with checking and savings accounts',
    fixtures: [
      'regularUser',
      'checkingAccount',
      'savingsAccount',
      'foodExpense',
      'salaryIncome',
      'transportExpense',
    ],
  },

  userWithTransactionHistory: {
    name: 'userWithTransactionHistory',
    description: 'User with complete transaction history',
    fixtures: [
      'regularUser',
      'checkingAccount',
      'savingsAccount',
      'creditCard',
      'foodExpense',
      'salaryIncome',
      'transportExpense',
      'salaryTransaction',
      'groceryExpense',
      'gasExpense',
      'transferTransaction',
    ],
  },

  multiUserScenario: {
    name: 'multiUserScenario',
    description: 'Multiple users for testing user isolation',
    fixtures: [
      'admin',
      'regularUser',
      'inactiveUser',
    ],
  },
};

/**
 * Fixture Manager
 */
export class FixtureManager {
  private static loadedFixtures: Map<string, any> = new Map();

  /**
   * Load a specific fixture
   */
  static load(fixtureName: string, fixtureSet: any): any {
    if (this.loadedFixtures.has(fixtureName)) {
      return this.loadedFixtures.get(fixtureName);
    }

    const fixture = fixtureSet[fixtureName];
    if (!fixture) {
      throw new Error(`Fixture '${fixtureName}' not found`);
    }

    this.loadedFixtures.set(fixtureName, fixture.data);
    return fixture.data;
  }

  /**
   * Load multiple fixtures
   */
  static loadMany(fixtureNames: string[], fixtureSet: any): any[] {
    return fixtureNames.map(name => this.load(name, fixtureSet));
  }

  /**
   * Load a complete test scenario
   */
  static loadScenario(scenarioName: string): any[] {
    const scenario = (testScenarios as any)[scenarioName];
    if (!scenario) {
      throw new Error(`Test scenario '${scenarioName}' not found`);
    }

    const fixtures: any[] = [];

    for (const fixtureName of scenario.fixtures) {
      // Try to find fixture in all fixture sets
      let found = false;

      if (userFixtures[fixtureName as keyof typeof userFixtures]) {
        fixtures.push(this.load(fixtureName, userFixtures));
        found = true;
      } else if (accountFixtures[fixtureName as keyof typeof accountFixtures]) {
        fixtures.push(this.load(fixtureName, accountFixtures));
        found = true;
      } else if (categoryFixtures[fixtureName as keyof typeof categoryFixtures]) {
        fixtures.push(this.load(fixtureName, categoryFixtures));
        found = true;
      } else if (transactionFixtures[fixtureName as keyof typeof transactionFixtures]) {
        fixtures.push(this.load(fixtureName, transactionFixtures));
        found = true;
      }

      if (!found) {
        throw new Error(`Fixture '${fixtureName}' not found in any fixture set`);
      }
    }

    return fixtures;
  }

  /**
   * Clear loaded fixtures
   */
  static clear(): void {
    this.loadedFixtures.clear();
  }

  /**
   * Get all available fixture names
   */
  static getAvailableFixtures(): {
    users: string[];
    accounts: string[];
    categories: string[];
    transactions: string[];
    scenarios: string[];
  } {
    return {
      users: Object.keys(userFixtures),
      accounts: Object.keys(accountFixtures),
      categories: Object.keys(categoryFixtures),
      transactions: Object.keys(transactionFixtures),
      scenarios: Object.keys(testScenarios),
    };
  }
}
