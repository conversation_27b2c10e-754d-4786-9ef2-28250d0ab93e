
import prisma from '../../lib/prisma';
import { User, FamilyMember } from '@prisma/client';
import jwt from 'jsonwebtoken';
import { faker } from '@faker-js/faker';

// Função para criar um usuário de teste
export const createTestUser = async (): Promise<User> => {
  return await prisma.user.create({
    data: {
      email: faker.internet.email(),
      name: faker.person.fullName(),
      password: 'hashedpassword', // Em um cenário real, use bcrypt
    },
  });
};

// Função para criar um membro de família de teste
export const createTestFamilyMember = async (): Promise<FamilyMember> => {
  return await prisma.familyMember.create({
    data: {
      name: faker.person.firstName(),
      color: faker.color.rgb(),
    },
  });
};

// Função para gerar um token de autenticação de teste
export const getTestAuthToken = (userId: string): string => {
  const secret = process.env.JWT_SECRET || 'test-secret-key-for-ci';
  return jwt.sign({ id: userId }, secret, { expiresIn: '1h' });
};
