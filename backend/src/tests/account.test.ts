import request from 'supertest';
import app from '../index';
import prisma from '../lib/prisma';
import { AccountType, FamilyMember } from '@prisma/client';
import { createTestUser, createTestFamilyMember, getTestAuthToken } from './helpers/testHelpers';

describe('Account API', () => {
  let authToken: string;
  let testFamilyMember: FamilyMember;

  beforeAll(async () => {
    // Limpar o banco de dados antes de todos os testes
    await prisma.accountMember.deleteMany();
    await prisma.account.deleteMany();
    await prisma.familyMember.deleteMany();
    await prisma.user.deleteMany();

    // Criar usuário e membro de família para os testes
    const user = await createTestUser();
    testFamilyMember = await createTestFamilyMember();
    authToken = getTestAuthToken(user.id);
  });

  afterAll(async () => {
    // Desconectar do Prisma
    await prisma.$disconnect();
  });

  describe('POST /api/v1/accounts', () => {
    it('should return 400 if familyMemberIds is missing', async () => {
      const accountData = {
        name: 'Test Account Without Members',
        type: AccountType.CHECKING,
        currency: 'BRL',
        includeInTotal: true,
        // familyMemberIds é intencionalmente omitido
      };

      const response = await request(app)
        .post('/api/v1/accounts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(accountData);

      expect(response.status).toBe(400);
      // A asserção do corpo do erro será adicionada após a melhoria do error handler
      // expect(response.body.error.code).toBe('VALIDATION_ERROR');
      // expect(response.body.error.message).toContain('familyMemberIds');
    });

    it('should create a new account and associate family members', async () => {
      const accountData = {
        name: 'Test Checking Account',
        type: AccountType.CHECKING,
        currency: 'BRL',
        includeInTotal: true,
        familyMemberIds: [testFamilyMember.id],
      };

      const response = await request(app)
        .post('/api/v1/accounts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(accountData);

      expect(response.status).toBe(201);
      expect(response.body.data.name).toBe(accountData.name);
      expect(response.body.data.members[0].familyMemberId).toBe(testFamilyMember.id);

      // Verificar no banco de dados
      const accountInDb = await prisma.account.findUnique({
        where: { id: response.body.data.id },
        include: { members: true },
      });

      expect(accountInDb).not.toBeNull();
      expect(accountInDb?.members.length).toBe(1);
      expect(accountInDb?.members[0].familyMemberId).toBe(testFamilyMember.id);
    });

    it('should return 400 for invalid data', async () => {
        const response = await request(app)
          .post('/api/v1/accounts')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ name: 'Incomplete' });
  
        expect(response.status).toBe(400);
        expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/v1/accounts', () => {
    it('should get all accounts', async () => {
      const response = await request(app)
        .get('/api/v1/accounts')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });
  });

  // Adicionar mais testes robustos para GET by ID, PUT, DELETE, etc.
});