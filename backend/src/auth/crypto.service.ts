import bcrypt from 'bcrypt';
import { CryptoService, CryptoConfig, CryptoConfigSchema, HashUtils } from '@personal-finance/shared';

/**
 * Implementação do serviço de criptografia usando bcrypt
 * Segue as melhores práticas de segurança e performance
 */
export class BcryptService implements CryptoService {
  private readonly config: CryptoConfig;

  constructor(config?: Partial<CryptoConfig>) {
    // Validar e aplicar configuração padrão
    this.config = CryptoConfigSchema.parse({
      defaultRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
      minRounds: 10,
      maxRounds: 15,
      ...config,
    });

    // Log da configuração em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 BcryptService configurado:', {
        defaultRounds: this.config.defaultRounds,
        minRounds: this.config.minRounds,
        maxRounds: this.config.maxRounds,
      });
    }
  }

  /**
   * Gera hash de uma senha usando bcrypt
   */
  async hash(password: string, rounds?: number): Promise<string> {
    const saltRounds = rounds || this.config.defaultRounds;

    // Validar rounds
    if (!HashUtils.validateRounds(saltRounds, this.config)) {
      throw new Error(
        `Rounds inválidos: ${saltRounds}. Deve estar entre ${this.config.minRounds} e ${this.config.maxRounds}`
      );
    }

    // Validar senha
    if (!password || password.length === 0) {
      throw new Error('Senha não pode estar vazia');
    }

    if (password.length > 72) {
      throw new Error('Senha muito longa (máximo 72 caracteres para bcrypt)');
    }

    try {
      const hash = await bcrypt.hash(password, saltRounds);
      
      // Validar hash gerado
      if (!this.isValidHash(hash)) {
        throw new Error('Hash gerado é inválido');
      }

      return hash;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Erro ao gerar hash: ${error.message}`);
      }
      throw new Error('Erro desconhecido ao gerar hash');
    }
  }

  /**
   * Compara uma senha com seu hash
   */
  async compare(password: string, hash: string): Promise<boolean> {
    // Validações básicas
    if (!password || !hash) {
      return false;
    }

    if (!this.isValidHash(hash)) {
      console.warn('⚠️  Hash inválido fornecido para comparação');
      return false;
    }

    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('❌ Erro ao comparar senha:', error);
      return false;
    }
  }

  /**
   * Gera um salt com o número especificado de rounds
   */
  async generateSalt(rounds: number): Promise<string> {
    if (!HashUtils.validateRounds(rounds, this.config)) {
      throw new Error(
        `Rounds inválidos: ${rounds}. Deve estar entre ${this.config.minRounds} e ${this.config.maxRounds}`
      );
    }

    try {
      return await bcrypt.genSalt(rounds);
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Erro ao gerar salt: ${error.message}`);
      }
      throw new Error('Erro desconhecido ao gerar salt');
    }
  }

  /**
   * Valida se um hash é válido
   */
  isValidHash(hash: string): boolean {
    return HashUtils.isBcryptHash(hash);
  }

  /**
   * Obtém informações sobre um hash
   */
  getHashInfo(hash: string): { rounds: number | null; isValid: boolean } {
    return {
      rounds: HashUtils.extractRounds(hash),
      isValid: this.isValidHash(hash),
    };
  }

  /**
   * Verifica se um hash precisa ser atualizado (rounds diferentes)
   */
  needsRehash(hash: string, targetRounds?: number): boolean {
    const rounds = HashUtils.extractRounds(hash);
    const target = targetRounds || this.config.defaultRounds;
    
    return rounds !== null && rounds !== target;
  }

  /**
   * Migra um hash para novos rounds se necessário
   */
  async migrateHash(password: string, oldHash: string, targetRounds?: number): Promise<string | null> {
    // Verificar se a senha corresponde ao hash atual
    const isValid = await this.compare(password, oldHash);
    if (!isValid) {
      return null;
    }

    // Verificar se precisa migrar
    if (!this.needsRehash(oldHash, targetRounds)) {
      return null;
    }

    // Gerar novo hash
    return await this.hash(password, targetRounds);
  }
}

/**
 * Instância singleton do serviço de criptografia
 */
export const cryptoService = new BcryptService();