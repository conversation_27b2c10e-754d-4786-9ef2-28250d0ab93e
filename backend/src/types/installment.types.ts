/**
 * Types and interfaces for installment functionality
 */

export interface InstallmentSummary {
  transactionId: string;
  totalInstallments: number;
  paidInstallments: number;
  pendingInstallments: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  progressPercentage: number;
  nextInstallmentDate?: Date;
  nextInstallmentAmount?: number;
}

export interface InstallmentCancellationResult {
  cancelledInstallments: number;
  message: string;
}

export interface InstallmentsByTransactionResult {
  transaction: any;
  installments: any[];
  summary: InstallmentSummary;
}

export interface MarkInstallmentPaidData {
  installmentId: string;
  paidAt?: Date;
}

export interface InstallmentStatusUpdate {
  installmentId: string;
  isPaid: boolean;
  paidAt?: Date;
}