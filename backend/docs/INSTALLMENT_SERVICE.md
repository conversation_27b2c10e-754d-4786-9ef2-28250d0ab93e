# 💳 Funcionalidades de Parcelas - Integradas ao TransactionService

## ⚠️ IMPORTANTE: Refatoração Concluída

**O `InstallmentService` foi removido e suas funcionalidades foram integradas ao `TransactionService`.**

Esta documentação foi atualizada para refletir a nova arquitetura onde todas as operações de parcelas são realizadas através do `TransactionService`.

## Visão Geral

As funcionalidades de parcelamento agora fazem parte do `TransactionService`, utilizando o modelo atual do banco de dados com a tabela `Installment` separada da tabela `Transaction`. Isso proporciona melhor organização, performance e manutenibilidade.

## Nova Arquitetura

### Modelo de Dados Atual
- **1 Transaction** → **N Installments** (relacionamento 1:N)
- Tabela `Transaction` contém a transação principal
- Tabela `Installment` contém as parcelas individuais
- Relacionamento com cascade delete

### Funcionalidades Disponíveis no TransactionService

#### ✅ Métodos Implementados

1. **`getInstallmentSummary(transactionId: string)`** - Retorna resumo das parcelas
2. **`cancelFutureInstallments(transactionId: string)`** - Cancela parcelas não pagas
3. **`markInstallmentAsPaid(installmentId: string)`** - Marca parcela como paga
4. **`getInstallmentsByTransaction(transactionId: string)`** - Busca transação com parcelas
5. **`getTransactionsWithInstallments(userId: string, options?)`** - Lista transações parceladas

## API Atualizada

### Criação de Transação com Parcelas

```typescript
// Usar o método create do TransactionService com array de installments
const transactionService = new TransactionService();

const result = await transactionService.create({
  description: 'Compra parcelada - TV 55"',
  totalAmount: 2400.00,
  accountId: 'credit_card_id',
  categoryId: 'electronics_category_id',
  installments: [
    { amount: 200.00, dueDate: new Date('2024-02-01') },
    { amount: 200.00, dueDate: new Date('2024-03-01') },
    { amount: 200.00, dueDate: new Date('2024-04-01') },
    // ... mais parcelas
  ]
});
```

### Resumo de Parcelas

```typescript
interface InstallmentSummary {
  totalInstallments: number;
  paidInstallments: number;
  pendingInstallments: number;
  cancelledInstallments: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  progressPercentage: number;
  nextDueDate?: Date;
}

const summary = await transactionService.getInstallmentSummary('transaction_id');
console.log(`Progresso: ${summary.progressPercentage}%`);
```

### Cancelamento de Parcelas Futuras

```typescript
const result = await transactionService.cancelFutureInstallments('transaction_id');
console.log(`${result.cancelledCount} parcelas canceladas`);
```

### Marcar Parcela como Paga

```typescript
const result = await transactionService.markInstallmentAsPaid('installment_id');
console.log('Parcela marcada como paga:', result.installment);
```

### Buscar Transações com Parcelas

```typescript
// Buscar uma transação específica com suas parcelas
const transactionWithInstallments = await transactionService.getInstallmentsByTransaction('transaction_id');

// Buscar todas as transações parceladas do usuário
const transactions = await transactionService.getTransactionsWithInstallments('user_id', {
  onlyPending: true // Opcional: apenas com parcelas pendentes
});
```

## Schema do Banco de Dados Atual

### Tabela `transactions`
```sql
CREATE TABLE transactions (
  id VARCHAR(30) PRIMARY KEY,
  description TEXT NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL,
  transaction_date TIMESTAMP NOT NULL,
  account_id VARCHAR(30) NOT NULL,
  category_id VARCHAR(30),
  -- outros campos...
);
```

### Tabela `installments`
```sql
CREATE TABLE installments (
  id VARCHAR(30) PRIMARY KEY,
  transaction_id VARCHAR(30) NOT NULL,
  installment_number INTEGER NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  due_date TIMESTAMP NOT NULL,
  paid_date TIMESTAMP,
  status VARCHAR(20) DEFAULT 'PENDING',
  -- outros campos...
  
  FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
);
```

### Status de Parcelas

```typescript
enum InstallmentStatus {
  PENDING = 'PENDING',     // Aguardando pagamento
  PAID = 'PAID',           // Paga
  CANCELLED = 'CANCELLED'  // Cancelada
}
```

## Tipos TypeScript

```typescript
interface InstallmentData {
  amount: number;
  dueDate: Date;
  description?: string;
}

interface InstallmentSummary {
  totalInstallments: number;
  paidInstallments: number;
  pendingInstallments: number;
  cancelledInstallments: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  progressPercentage: number;
  nextDueDate?: Date;
}

interface CancelInstallmentsResult {
  cancelledCount: number;
  cancelledInstallments: Installment[];
}

interface MarkInstallmentPaidResult {
  installment: Installment;
  summary: InstallmentSummary;
}
```

## Validações

### Regras de Negócio
1. **Valor da parcela**: Deve ser positivo
2. **Data de vencimento**: Deve ser futura para parcelas pendentes
3. **Status**: Apenas parcelas PENDING podem ser marcadas como PAID
4. **Cancelamento**: Apenas parcelas não pagas podem ser canceladas

## Testes

### Cobertura de Testes Atualizada
- ✅ Criação de transação com parcelas
- ✅ Resumo de parcelas
- ✅ Cancelamento de parcelas futuras
- ✅ Marcação de parcela como paga
- ✅ Busca de transações com parcelas
- ✅ Validações de entrada
- ✅ Tratamento de erros

### Executar Testes

```bash
npm test -- --testPathPattern="installment-transactions.test.ts"
```

## Migração do Código Antigo

### Antes (InstallmentService - REMOVIDO)
```typescript
// ❌ Código antigo - NÃO USAR
const installmentService = new InstallmentService();
const result = await installmentService.createInstallmentTransaction(data);
```

### Depois (TransactionService - ATUAL)
```typescript
// ✅ Código atual - USAR
const transactionService = new TransactionService();
const result = await transactionService.create({
  ...data,
  installments: [/* array de parcelas */]
});
```

## Arquivos Relacionados

- `src/services/transaction.service.ts` - Implementação principal
- `src/tests/installment-transactions.test.ts` - Testes unitários
- `src/types/installment.types.ts` - Tipos TypeScript
- `src/schemas/transaction.schemas.ts` - Validações Zod

## Vantagens da Nova Arquitetura

1. **Melhor Performance**: Relacionamento 1:N otimizado
2. **Manutenibilidade**: Código centralizado no TransactionService
3. **Consistência**: Uso do modelo atual do banco de dados
4. **Flexibilidade**: Parcelas independentes com controle individual
5. **Escalabilidade**: Suporte a grandes volumes de parcelas

## Casos de Uso Atualizados

### 1. Compra Parcelada no Cartão

```typescript
const purchase = await transactionService.create({
  description: 'Notebook Dell - 12x sem juros',
  totalAmount: 3600.00,
  accountId: 'credit_card_id',
  categoryId: 'technology_id',
  installments: Array.from({ length: 12 }, (_, i) => ({
    amount: 300.00,
    dueDate: new Date(2024, i + 1, 15) // Dia 15 de cada mês
  }))
});
```

### 2. Acompanhamento de Progresso

```typescript
const summary = await transactionService.getInstallmentSummary('transaction_id');
console.log(`Progresso: ${summary.progressPercentage}%`);
console.log(`Próximo vencimento: ${summary.nextDueDate}`);
```

### 3. Cancelamento Antecipado

```typescript
const result = await transactionService.cancelFutureInstallments('transaction_id');
console.log(`${result.cancelledCount} parcelas canceladas`);
```

---

**Última Atualização**: Janeiro 2025  
**Versão**: 2.0.0  
**Status**: ✅ Refatorado e Integrado ao TransactionService  
**Migração**: ✅ Concluída
