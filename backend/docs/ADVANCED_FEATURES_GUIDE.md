# 🚀 Guia de Funcionalidades Avançadas - Personal Finance Manager

## Vis<PERSON> Geral

Este guia apresenta as funcionalidades avançadas implementadas no sistema de gestão financeira, incluindo transferências, conversão de moedas, transações parceladas e processamento em lote.

## 📋 Índice

1. [Transferências entre Contas](#transferências-entre-contas)
2. [<PERSON><PERSON><PERSON> de Moedas](#conversão-de-moedas)
3. [Transações Parceladas](#transações-parceladas)
4. [Processamento em Lote](#processamento-em-lote)
5. [Cenários Práticos](#cenários-práticos)
6. [<PERSON><PERSON>s Práticas](#melhores-práticas)

## 💸 Transferências entre Contas

### Transferência Simples (Mesma Moeda)

```http
POST /api/v1/transactions
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "Transferência para poupança",
  "amount": 1000.00,
  "type": "TRANSFER",
  "accountId": "checking_account_id",
  "destinationAccountId": "savings_account_id",
  "familyMemberIds": ["member_id"]
}
```

### Transferência com Conversão de Moeda

```http
POST /api/v1/transactions
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "Transferência USD → BRL",
  "amount": 500.00,
  "type": "TRANSFER",
  "accountId": "usd_account_id",
  "destinationAccountId": "brl_account_id",
  "exchangeRate": 5.25,
  "sourceCurrency": "USD",
  "destinationCurrency": "BRL",
  "sourceAmount": 500.00,
  "destinationAmount": 2625.00,
  "familyMemberIds": ["member_id"]
}
```

### Validações Automáticas

O sistema valida automaticamente:
- ✅ Saldo suficiente na conta origem
- ✅ Contas diferentes (origem ≠ destino)
- ✅ Contas existentes e ativas
- ✅ Taxa de câmbio válida
- ✅ Códigos de moeda corretos

## 💱 Conversão de Moedas

### Cálculo de Conversão

```http
POST /api/v1/currency/convert
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 1000.00,
  "fromCurrency": "USD",
  "toCurrency": "BRL",
  "manualRate": 5.25
}
```

**Resposta:**
```json
{
  "originalAmount": 1000.00,
  "convertedAmount": 5250.00,
  "exchangeRate": 5.25,
  "isManualRate": true,
  "fromCurrency": "USD",
  "toCurrency": "BRL",
  "conversionFee": 78.75
}
```

### Taxa Automática (API Externa)

```http
POST /api/v1/currency/convert
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 1000.00,
  "fromCurrency": "USD",
  "toCurrency": "BRL"
}
```

O sistema busca automaticamente a taxa atual da API de câmbio.

### Moedas Suportadas

```http
GET /api/v1/currency/supported
Authorization: Bearer <token>
```

**Resposta:**
```json
{
  "currencies": ["BRL", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "ARS"]
}
```

## 💳 Transações Parceladas

**⚠️ ATUALIZAÇÃO**: As funcionalidades de parcelas foram integradas ao `TransactionService` usando o modelo atual do banco de dados (1 Transaction → N Installments).

### Criação de Transação com Parcelas

```http
POST /api/v1/transactions
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "Notebook Dell - 12x sem juros",
  "totalAmount": 3600.00,
  "accountId": "credit_card_id",
  "categoryId": "technology_id",
  "familyMemberIds": ["member_id"],
  "installments": [
    { "amount": 300.00, "dueDate": "2024-02-01T00:00:00Z" },
    { "amount": 300.00, "dueDate": "2024-03-01T00:00:00Z" },
    { "amount": 300.00, "dueDate": "2024-04-01T00:00:00Z" },
    // ... mais 9 parcelas
  ]
}
```

**Resultado:**
- 1 transação principal
- 12 registros na tabela `installments`
- Relacionamento 1:N otimizado

### Resumo de Parcelas

```http
GET /api/v1/transactions/{transactionId}/installments/summary
Authorization: Bearer <token>
```

**Resposta:**
```json
{
  "totalInstallments": 12,
  "paidInstallments": 3,
  "pendingInstallments": 8,
  "cancelledInstallments": 1,
  "totalAmount": 3600.00,
  "paidAmount": 900.00,
  "pendingAmount": 2400.00,
  "progressPercentage": 25.0,
  "nextDueDate": "2024-05-01T00:00:00Z"
}
```

### Cancelamento de Parcelas Futuras

```http
POST /api/v1/transactions/{transactionId}/installments/cancel-future
Authorization: Bearer <token>
```

### Marcar Parcela como Paga

```http
POST /api/v1/transactions/installments/{installmentId}/mark-paid
Authorization: Bearer <token>
```

### Buscar Transações com Parcelas

```http
GET /api/v1/transactions/with-installments?userId={userId}&onlyPending=true
Authorization: Bearer <token>
```

## ⚡ Processamento em Lote

### Processamento de Transações Futuras

O sistema inclui um job service para processar transações futuras automaticamente:

```typescript
import { FutureTransactionJobService } from './services/future-transaction-job.service';

const jobService = new FutureTransactionJobService();

// Processar todas as transações futuras que venceram
const result = await jobService.execute();

console.log(result);
// {
//   processed: 150,
//   failed: 2,
//   skipped: 5,
//   totalTime: '2.5s'
// }
```

### Configuração de Agendamento

```typescript
// Executar a cada hora
import cron from 'node-cron';

cron.schedule('0 * * * *', async () => {
  const jobService = new FutureTransactionJobService();
  await jobService.execute();
});
```

## 🎯 Cenários Práticos

### 1. Compra Internacional com Conversão

```typescript
// 1. Calcular conversão
const conversion = await currencyService.convertAmount({
  amount: 1000,
  fromCurrency: 'USD',
  toCurrency: 'BRL'
});

// 2. Criar transferência
const transfer = await transactionService.create({
  description: 'Compra internacional - Amazon',
  amount: conversion.originalAmount,
  type: 'TRANSFER',
  accountId: 'usd_account_id',
  destinationAccountId: 'brl_account_id',
  exchangeRate: conversion.exchangeRate,
  sourceCurrency: 'USD',
  destinationCurrency: 'BRL',
  sourceAmount: conversion.originalAmount,
  destinationAmount: conversion.convertedAmount
});
```

### 2. Financiamento Parcelado

```typescript
// Criar financiamento de veículo usando TransactionService
const financing = await transactionService.create({
  description: 'Financiamento Honda Civic 2024',
  totalAmount: 80000.00,
  accountId: 'financing_account_id',
  categoryId: 'vehicle_id',
  installments: Array.from({ length: 48 }, (_, i) => ({
    amount: 1666.67, // 80000 / 48
    dueDate: new Date(2024, 2 + i, 1) // Março + i meses
  }))
});

// Acompanhar progresso
const summary = await transactionService.getInstallmentSummary(financing.id);
console.log(`Progresso: ${summary.progressPercentage}%`);
```

### 3. Transferência Programada

```typescript
// Criar transferência futura (automática)
const scheduledTransfer = await transactionService.create({
  description: 'Transferência automática - Poupança',
  amount: 500.00,
  type: 'TRANSFER',
  accountId: 'checking_id',
  destinationAccountId: 'savings_id',
  transactionDate: new Date('2024-03-01'), // Data futura
  isFuture: true
});

// Será processada automaticamente pelo job service
```

## 📊 Melhores Práticas

### 1. Validação de Dados

```typescript
// Sempre validar antes de criar transações
const validateTransferData = (data) => {
  if (data.accountId === data.destinationAccountId) {
    throw new Error('Contas de origem e destino devem ser diferentes');
  }
  
  if (data.amount <= 0) {
    throw new Error('Valor deve ser positivo');
  }
  
  if (data.exchangeRate && data.exchangeRate <= 0) {
    throw new Error('Taxa de câmbio deve ser positiva');
  }
};
```

### 2. Tratamento de Erros

```typescript
try {
  const result = await transactionService.create(transferData);
} catch (error) {
  switch (error.code) {
    case 'INSUFFICIENT_BALANCE':
      // Mostrar erro de saldo insuficiente
      break;
    case 'ACCOUNT_NOT_FOUND':
      // Mostrar erro de conta não encontrada
      break;
    case 'INVALID_CURRENCY_CODE':
      // Mostrar erro de moeda inválida
      break;
    default:
      // Erro genérico
      break;
  }
}
```

### 3. Performance

```typescript
// Para múltiplas operações, use transações do banco
await prisma.$transaction(async (tx) => {
  const transfer1 = await tx.transaction.create({...});
  const transfer2 = await tx.transaction.create({...});
  const transfer3 = await tx.transaction.create({...});
});

// Para conversões, use cache
const cachedRate = await currencyService.getCurrentRate('USD', 'BRL');
```

### 4. Monitoramento

```typescript
// Log de operações importantes
console.log(`[TRANSFER] ${transfer.id}: ${transfer.sourceAmount} ${transfer.sourceCurrency} → ${transfer.destinationAmount} ${transfer.destinationCurrency}`);

// Métricas de performance
const startTime = Date.now();
await jobService.execute();
const duration = Date.now() - startTime;
console.log(`[JOB] Processed in ${duration}ms`);
```

## 🔗 Recursos Adicionais

### Documentação Técnica
- [CurrencyService](./CURRENCY_SERVICE.md)
- [Funcionalidades de Parcelas - TransactionService](./INSTALLMENT_SERVICE.md)
- [Transfers Module](./TRANSFERS_MODULE.md)

### Testes
```bash
# Executar todos os testes avançados
npm test -- --testPathPattern="currency-conversion|transfer-transactions|installment-transactions|batch-processing"

# Testes específicos
npm test -- --testPathPattern="currency-conversion.test.ts"
npm test -- --testPathPattern="installment-transactions.test.ts"
```

### Configuração de Produção
- Configure Redis para cache de taxas de câmbio
- Configure job scheduler (cron) para transações futuras
- Configure monitoramento de performance
- Configure alertas para falhas de conversão

---

**Última Atualização**: Dezembro 2024  
**Versão**: 1.0.0  
**Status**: ✅ Implementado e Documentado
