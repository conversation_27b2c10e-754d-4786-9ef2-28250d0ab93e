import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { dashboardApi, futureTransactionsApi, cacheApi } from '@/lib/api'
import { useFamilyMemberStats } from '@/hooks/useFamilyMembers'
import type { StatsFilters } from '@/components/stats/StatsFilters'

interface EnhancedStatsData {
  overview: any
  familyStats: any
  futureStats: any
  cacheStats: any
  performanceStats: any
  trendsData: any[]
  categoryData: any[]
  comparisonData?: any
}

export function useEnhancedStats(filters: StatsFilters) {
  // Fetch dashboard overview
  const { data: overview, isLoading: isLoadingOverview } = useQuery({
    queryKey: ['dashboard', 'overview', filters],
    queryFn: () => dashboardApi.getOverview({
      startDate: filters.startDate?.toISOString(),
      endDate: filters.endDate?.toISOString()
    }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  // Fetch family members stats
  const { data: familyStats, isLoading: isLoadingFamily } = useFamilyMemberStats()

  // Fetch future transactions stats
  const { data: futureStats, isLoading: isLoadingFuture } = useQuery({
    queryKey: ['future-transactions', 'stats', filters],
    queryFn: () => futureTransactionsApi.getStats(),
    staleTime: 2 * 60 * 1000,
  })

  // Fetch cache stats
  const { data: cacheStats, isLoading: isLoadingCache } = useQuery({
    queryKey: ['cache', 'stats'],
    queryFn: cacheApi.getStats,
    staleTime: 30 * 1000, // 30 seconds
  })

  // Fetch performance metrics
  const { data: performanceStats, isLoading: isLoadingPerformance } = useQuery({
    queryKey: ['dashboard', 'performance-metrics'],
    queryFn: dashboardApi.getPerformanceMetrics,
    staleTime: 1 * 60 * 1000, // 1 minute
  })

  // Mock trends data for now
  const trendsData = {
    data: {
      data: [
        {
          date: '2024-01-01',
          income: 5000,
          expenses: 3500,
          balance: 1500
        },
        {
          date: '2024-02-01',
          income: 5200,
          expenses: 3800,
          balance: 1400
        },
        {
          date: '2024-03-01',
          income: 4800,
          expenses: 3200,
          balance: 1600
        },
        {
          date: '2024-04-01',
          income: 5500,
          expenses: 4000,
          balance: 1500
        },
        {
          date: '2024-05-01',
          income: 5300,
          expenses: 3600,
          balance: 1700
        },
        {
          date: '2024-06-01',
          income: 5100,
          expenses: 3400,
          balance: 1700
        }
      ]
    }
  }
  const isLoadingTrends = false

  // Mock category data for now
  const categoryData = {
    data: {
      data: [
        {
          name: 'Alimentação',
          value: 1200,
          color: 'hsl(var(--chart-1))',
          percentage: 35.3,
          count: 24
        },
        {
          name: 'Transporte',
          value: 800,
          color: 'hsl(var(--chart-2))',
          percentage: 23.5,
          count: 18
        },
        {
          name: 'Lazer',
          value: 600,
          color: 'hsl(var(--chart-3))',
          percentage: 17.6,
          count: 12
        },
        {
          name: 'Saúde',
          value: 400,
          color: 'hsl(var(--chart-4))',
          percentage: 11.8,
          count: 8
        },
        {
          name: 'Educação',
          value: 300,
          color: 'hsl(var(--chart-5))',
          percentage: 8.8,
          count: 6
        },
        {
          name: 'Outros',
          value: 100,
          color: '#8884d8',
          percentage: 2.9,
          count: 4
        }
      ]
    }
  }
  const isLoadingCategories = false

  // Fetch comparison data if needed
  const { data: comparisonData, isLoading: isLoadingComparison } = useQuery({
    queryKey: ['dashboard', 'comparison', filters],
    queryFn: () => {
      if (!filters.compareWithPrevious || !filters.startDate || !filters.endDate) {
        return null
      }

      const periodDiff = filters.endDate.getTime() - filters.startDate.getTime()
      const previousStartDate = new Date(filters.startDate.getTime() - periodDiff)
      const previousEndDate = new Date(filters.endDate.getTime() - periodDiff)

      return dashboardApi.getOverview({
        startDate: previousStartDate.toISOString(),
        endDate: previousEndDate.toISOString()
      })
    },
    enabled: filters.compareWithPrevious,
    staleTime: 5 * 60 * 1000,
  })

  const isLoading = 
    isLoadingOverview || 
    isLoadingFamily || 
    isLoadingFuture || 
    isLoadingCache || 
    isLoadingPerformance ||
    isLoadingTrends ||
    isLoadingCategories ||
    (filters.compareWithPrevious && isLoadingComparison)

  const enhancedData = useMemo((): EnhancedStatsData => {
    return {
      overview: overview?.data || {},
      familyStats: familyStats || {},
      futureStats: futureStats?.data || {},
      cacheStats: cacheStats?.data?.data || {},
      performanceStats: performanceStats?.data || {},
      trendsData: trendsData?.data?.data || [],
      categoryData: categoryData?.data?.data || [],
      comparisonData: comparisonData?.data || null
    }
  }, [
    overview,
    familyStats,
    futureStats,
    cacheStats,
    performanceStats,
    trendsData,
    categoryData,
    comparisonData
  ])

  // Generate enhanced stats cards data
  const statsCardsData = useMemo(() => {
    const data = enhancedData.overview
    const comparison = enhancedData.comparisonData
    
    return [
      {
        title: 'Saldo Total',
        value: data.totalBalance || 0,
        previousValue: comparison?.totalBalance,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-green-500 to-green-600',
        textColor: 'text-green-600',
        format: 'currency' as const,
        tooltip: 'Soma de todos os saldos das contas ativas'
      },
      {
        title: 'Receitas do Período',
        value: data.periodIncome || 0,
        previousValue: comparison?.periodIncome,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-blue-500 to-blue-600',
        textColor: 'text-blue-600',
        format: 'currency' as const,
        tooltip: 'Total de receitas no período selecionado'
      },
      {
        title: 'Despesas do Período',
        value: data.periodExpenses || 0,
        previousValue: comparison?.periodExpenses,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-red-500 to-red-600',
        textColor: 'text-red-600',
        format: 'currency' as const,
        tooltip: 'Total de despesas no período selecionado'
      },
      {
        title: 'Resultado do Período',
        value: (data.periodIncome || 0) - (data.periodExpenses || 0),
        previousValue: comparison ? (comparison.periodIncome || 0) - (comparison.periodExpenses || 0) : undefined,
        icon: () => null, // Will be set in component
        color: 'bg-gradient-to-r from-purple-500 to-purple-600',
        textColor: 'text-purple-600',
        format: 'currency' as const,
        tooltip: 'Diferença entre receitas e despesas do período'
      }
    ]
  }, [enhancedData.overview, enhancedData.comparisonData])

  return {
    data: enhancedData,
    statsCardsData,
    isLoading,
    error: null // TODO: Implement error handling
  }
}
