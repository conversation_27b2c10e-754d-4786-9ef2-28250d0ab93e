# 🎨 Sistema de Design Deep Blue Midnight

Um sistema de design completo e minimalista baseado em **Deep Blue Midnight** (#0f172a) como cor primária, desenvolvido com foco em acessibilidade, consistência visual e experiência do usuário fluida.

## 🚀 Início Rápido

### Importando Componentes
```tsx
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
```

### Exemplo Básico
```tsx
function ExemploBasico() {
  return (
    <Card variant="elevated">
      <CardHeader>
        <CardTitle>Título do Card</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input placeholder="Digite algo..." />
        <div className="flex gap-2">
          <Button variant="default">Primário</Button>
          <Button variant="outline">Secundário</Button>
        </div>
        <Badge variant="success">Concluído</Badge>
      </CardContent>
    </Card>
  )
}
```

## 🎯 Componentes Principais

### Button
Botões com múltiplas variações e estados interativos.

```tsx
// Variações
<Button variant="default">Primário</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="destructive">Destrutivo</Button>

// Tamanhos
<Button size="sm">Pequeno</Button>
<Button size="default">Padrão</Button>
<Button size="lg">Grande</Button>
<Button size="icon">🎨</Button>

// Estados
<Button disabled>Desabilitado</Button>
```

### Card
Cards com diferentes níveis de elevação e efeitos.

```tsx
// Variações
<Card variant="default">Card padrão</Card>
<Card variant="elevated">Card elevado</Card>
<Card variant="outlined">Card com borda</Card>
<Card variant="glass">Card com efeito glass</Card>

// Estrutura completa
<Card variant="elevated">
  <CardHeader>
    <CardTitle>Título</CardTitle>
    <CardDescription>Descrição</CardDescription>
  </CardHeader>
  <CardContent>
    Conteúdo do card
  </CardContent>
  <CardFooter>
    Rodapé do card
  </CardFooter>
</Card>
```

### Input
Inputs com estados de validação e foco aprimorado.

```tsx
// Variações
<Input placeholder="Input padrão" />
<Input variant="error" placeholder="Campo com erro" />
<Input variant="success" placeholder="Campo válido" />

// Com label e mensagem
<div className="form-group">
  <label className="form-label">Nome</label>
  <Input placeholder="Digite seu nome" />
  <span className="form-help">Campo obrigatório</span>
</div>
```

### Badge
Indicadores coloridos para status e categorias.

```tsx
// Variações
<Badge variant="default">Padrão</Badge>
<Badge variant="success">Sucesso</Badge>
<Badge variant="warning">Aviso</Badge>
<Badge variant="destructive">Erro</Badge>
<Badge variant="info">Info</Badge>

// Tamanhos
<Badge size="sm">Pequeno</Badge>
<Badge size="default">Padrão</Badge>
<Badge size="lg">Grande</Badge>
```

## 🎨 Classes Utilitárias

### Gradientes de Texto
```tsx
<h1 className="text-gradient">Título com gradiente</h1>
<h2 className="text-gradient-deep">Gradiente mais escuro</h2>
<h3 className="text-gradient-subtle">Gradiente sutil</h3>
```

### Efeitos Glass
```tsx
<div className="glass p-6">Efeito glass básico</div>
<div className="glass-deep p-6">Efeito glass pronunciado</div>
<div className="glass-card p-6">Efeito glass para cards</div>
```

### Gradientes de Background
```tsx
<div className="bg-gradient-deep">Background deep blue</div>
<div className="bg-gradient-subtle">Background sutil</div>
<div className="bg-gradient-midnight">Background midnight</div>
```

### Sombras Personalizadas
```tsx
<div className="shadow-soft">Sombra sutil</div>
<div className="shadow-elegant">Sombra elegante</div>
<div className="shadow-glow">Sombra com brilho</div>
<div className="shadow-deep">Sombra profunda</div>
```

## 🌓 Modo Escuro

O sistema suporta automaticamente modo claro, escuro e sistema:

```tsx
import { useTheme } from '@/contexts/ThemeProvider'

function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  
  return (
    <div className="flex gap-2">
      <Button 
        variant={theme === 'light' ? 'default' : 'outline'}
        onClick={() => setTheme('light')}
      >
        Claro
      </Button>
      <Button 
        variant={theme === 'dark' ? 'default' : 'outline'}
        onClick={() => setTheme('dark')}
      >
        Escuro
      </Button>
      <Button 
        variant={theme === 'system' ? 'default' : 'outline'}
        onClick={() => setTheme('system')}
      >
        Sistema
      </Button>
    </div>
  )
}
```

## 📏 Sistema de Espaçamento

Use as classes de espaçamento baseadas na escala de 4px:

```tsx
// Padding
<div className="p-xs">4px</div>
<div className="p-sm">8px</div>
<div className="p-md">16px</div>
<div className="p-lg">24px</div>
<div className="p-xl">32px</div>

// Margin
<div className="m-xs">4px</div>
<div className="m-sm">8px</div>
<div className="m-md">16px</div>

// Gap (para flexbox/grid)
<div className="flex gap-sm">8px entre elementos</div>
<div className="grid gap-md">16px entre elementos</div>

// Space-y (espaçamento vertical entre filhos)
<div className="space-y-md">16px entre elementos filhos</div>
```

## 🎭 Estados Interativos

### Hover e Focus
```tsx
// Botões com hover automático
<Button>Hover automático</Button>

// Classes personalizadas
<div className="hover:shadow-elegant transition-all duration-200">
  Hover personalizado
</div>

// Focus ring
<input className="focus-ring" />
```

### Transições
```tsx
// Transições rápidas (150ms)
<div className="transition-all duration-150">Rápida</div>

// Transições normais (200ms)
<div className="transition-all duration-200">Normal</div>

// Transições lentas (350ms)
<div className="transition-all duration-350">Lenta</div>
```

## 🎯 Padrões de Layout

### Container Principal
```tsx
<main className="p-6 bg-gradient-subtle min-h-screen">
  <div className="mx-auto max-w-7xl space-y-6">
    {/* Conteúdo da página */}
  </div>
</main>
```

### Grid Responsivo
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <Card>Item 1</Card>
  <Card>Item 2</Card>
  <Card>Item 3</Card>
</div>
```

### Flexbox
```tsx
<div className="flex flex-col md:flex-row gap-4 items-center justify-between">
  <div>Conteúdo esquerdo</div>
  <div>Conteúdo direito</div>
</div>
```

## 🔧 Customização

### Adicionando Novas Variações
```tsx
// Exemplo: novo variant de botão
const buttonVariants = cva(
  "base-classes...",
  {
    variants: {
      variant: {
        // ... variants existentes
        custom: "bg-purple-600 text-white hover:bg-purple-700"
      }
    }
  }
)
```

### Estendendo Cores
```tsx
// No tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        custom: {
          500: '#your-color',
          600: '#your-darker-color'
        }
      }
    }
  }
}
```

## ♿ Acessibilidade

### Contraste
- Todas as combinações de cores atendem WCAG 2.1 AA (4.5:1)
- Cores críticas atendem WCAG 2.1 AAA (7:1)

### Navegação por Teclado
```tsx
// Focus rings automáticos
<Button>Navegável por teclado</Button>

// Focus customizado
<div className="focus-visible:ring-2 focus-visible:ring-ring">
  Elemento focável
</div>
```

### Screen Readers
```tsx
// Labels descritivos
<Button aria-label="Fechar modal">×</Button>

// Estados para screen readers
<Button aria-pressed={isPressed}>Toggle</Button>
```

## 📱 Responsividade

### Breakpoints
- `sm`: 640px (Mobile landscape)
- `md`: 768px (Tablet portrait)
- `lg`: 1024px (Tablet landscape)
- `xl`: 1280px (Desktop medium)
- `2xl`: 1536px (Desktop large)

### Uso
```tsx
<div className="text-sm md:text-base lg:text-lg">
  Texto responsivo
</div>

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  Grid responsivo
</div>
```

## 🧪 Testando o Sistema

### Componente de Demonstração
```tsx
import { DesignSystemShowcase } from '@/components/examples/DesignSystemShowcase'

function App() {
  return <DesignSystemShowcase />
}
```

### Verificação de Contraste
Use ferramentas como:
- Chrome DevTools (Lighthouse)
- axe DevTools
- Colour Contrast Analyser

## 📚 Recursos Adicionais

- [Tokens de Design](./tokens.md) - Referência completa de tokens
- [Componentes](../components/ui/) - Código fonte dos componentes
- [Exemplos](../components/examples/) - Exemplos de uso

## 🤝 Contribuindo

1. Mantenha a consistência com os tokens existentes
2. Teste em modo claro e escuro
3. Verifique acessibilidade (contraste, navegação por teclado)
4. Documente novos componentes e padrões
5. Adicione exemplos de uso

---

**Versão:** 1.0  
**Última atualização:** Janeiro 2025  
**Compatibilidade:** React 18+, Tailwind CSS 3+