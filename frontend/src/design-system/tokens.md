# Design Tokens - Dark Ocean

## 🎨 Cores

### Prim<PERSON><PERSON><PERSON> (Dark Ocean)
```css
primary-50: #f1f5f8   /* Quase branco com toque oceânico */
primary-100: #e2eaf2  /* Cinza muito claro oceânico */
primary-200: #c5d4e6  /* Cinza claro oceânico */
primary-300: #a8bfd9  /* Cinza médio oceânico */
primary-400: #6b8db5  /* Azul acinzentado médio */
primary-500: #4a6b8a  /* Azul oceânico médio */
primary-600: #3a5570  /* Azul oceânico escuro */
primary-700: #2d4356  /* Dark ocean escuro */
primary-800: #1e2a3a  /* DARK OCEAN BASE */
primary-900: #15202b  /* Dark ocean profundo */
primary-950: #0d1419  /* Quase preto oceânico */
```

### Semânticas
```css
success-600: #059669  /* <PERSON> esmeralda */
warning-600: #d97706  /* <PERSON><PERSON><PERSON> doura<PERSON> */
error-600: #dc2626   /* Ver<PERSON><PERSON> coral */
info-600: #0284c7    /* Azul céu */
```

## 📏 Espaçamento

### Escala Base (4px)
```css
xs: 4px    /* 0.25rem */
sm: 8px    /* 0.5rem */
md: 16px   /* 1rem */
lg: 24px   /* 1.5rem */
xl: 32px   /* 2rem */
2xl: 48px  /* 3rem */
3xl: 64px  /* 4rem */
4xl: 96px  /* 6rem */
```

## 📝 Tipografia

### Escala de Tamanhos
```css
caption: 12px   /* 0.75rem */
small: 14px     /* 0.875rem */
body: 16px      /* 1rem */
h6: 18px        /* 1.125rem */
h5: 20px        /* 1.25rem */
h4: 24px        /* 1.5rem */
h3: 30px        /* 1.875rem */
h2: 36px        /* 2.25rem */
h1: 48px        /* 3rem */
display: 64px   /* 4rem */
```

### Pesos
```css
light: 300
regular: 400
medium: 500
semibold: 600
bold: 700
```

## 🌊 Sombras

### Sistema de Elevação
```css
soft: 0 1px 3px rgba(15, 23, 42, 0.1)
medium: 0 4px 6px rgba(15, 23, 42, 0.1)
large: 0 10px 15px rgba(15, 23, 42, 0.1)
xl: 0 20px 25px rgba(15, 23, 42, 0.1)
glow: 0 0 20px rgba(15, 23, 42, 0.3)
elegant: 0 4px 20px rgba(15, 23, 42, 0.15)
deep: 0 25px 50px rgba(15, 23, 42, 0.25)
```

## 🔄 Transições

### Durações
```css
fast: 150ms     /* Micro-interações */
normal: 250ms   /* Padrão */
slow: 350ms     /* Complexas */
```

### Easing
```css
ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
ease-out: cubic-bezier(0, 0, 0.2, 1)
ease-in: cubic-bezier(0.4, 0, 1, 1)
```

## 📐 Border Radius

```css
sm: 2px     /* Elementos pequenos */
md: 6px     /* Botões, inputs */
lg: 8px     /* Cards padrão */
xl: 12px    /* Cards destacados */
2xl: 16px   /* Elementos grandes */
full: 9999px /* Circulares */
```

## 🎯 Z-Index

```css
dropdown: 1000
sticky: 1020
fixed: 1030
modal-backdrop: 1040
modal: 1050
popover: 1060
tooltip: 1070
```

## 📱 Breakpoints

```css
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet portrait */
lg: 1024px  /* Tablet landscape */
xl: 1280px  /* Desktop medium */
2xl: 1536px /* Desktop large */
```

## 🎨 Classes Utilitárias

### Gradientes de Texto
```css
.text-gradient        /* primary-600 → primary-900 */
.text-gradient-deep   /* primary-700 → primary-950 */
.text-gradient-subtle /* primary-500 → primary-700 */
```

### Efeitos Glass
```css
.glass      /* Básico com backdrop-blur */
.glass-deep /* Mais pronunciado */
.glass-card /* Para cards */
```

### Gradientes de Background
```css
.bg-gradient-deep     /* primary-800 → primary-950 */
.bg-gradient-subtle   /* background → muted */
.bg-gradient-midnight /* primary-900 → primary-950 */
```

### Sombras Personalizadas
```css
.shadow-soft    /* Sutil */
.shadow-elegant /* Média com cor */
.shadow-glow    /* Com brilho */
.shadow-deep    /* Pronunciada */
```

## 🎭 Componentes

### Botões
```css
.btn-primary   /* Principal com deep blue */
.btn-secondary /* Outline com deep blue */
.btn-ghost     /* Transparente */
.btn-sm        /* Pequeno */
.btn-lg        /* Grande */
```

### Cards
```css
.card          /* Padrão */
.card-elevated /* Com mais sombra */
.card-glass    /* Efeito glass */
```

### Inputs
```css
.input         /* Padrão */
.input-error   /* Estado de erro */
.input-success /* Estado de sucesso */
```

## 🌓 Modo Escuro

### Variáveis CSS Automáticas
O sistema usa variáveis CSS que se adaptam automaticamente:

```css
/* Modo Claro */
--background: #ffffff
--foreground: #0f172a
--primary: #0f172a
--primary-foreground: #f8fafc

/* Modo Escuro */
--background: #0f172a
--foreground: #f8fafc
--primary: #f8fafc
--primary-foreground: #0f172a
```

## 🎯 Uso Recomendado

### Para Textos
- **Títulos principais:** `text-foreground font-bold`
- **Textos secundários:** `text-muted-foreground`
- **Links:** `text-primary hover:text-primary/80`

### Para Backgrounds
- **Página:** `bg-background`
- **Cards:** `bg-card`
- **Destaque:** `bg-primary text-primary-foreground`

### Para Bordas
- **Padrão:** `border-border`
- **Destaque:** `border-primary`
- **Estados:** `border-success`, `border-warning`, `border-destructive`

### Para Sombras
- **Cards:** `shadow-soft`
- **Modais:** `shadow-deep`
- **Hover:** `hover:shadow-elegant`