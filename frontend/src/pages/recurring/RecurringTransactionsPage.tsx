import { useState } from 'react'
import { Repeat, Plus, Filter, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useRecurringTransactions } from '@/hooks/useRecurringTransactions'
import { RecurringTransactionsList } from '@/components/recurring/RecurringTransactionsList'
import { RecurringTransactionsListView } from '@/components/recurring/RecurringTransactionsListView'
import { RecurringTransactionModal } from '@/components/recurring/RecurringTransactionModal'
import { RecurringTransactionsStats } from '@/components/recurring/RecurringTransactionsStats'
import { RecurringTransactionsFilters } from '@/components/recurring/RecurringTransactionsFilters'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import type { RecurringTransaction } from '@/hooks/useRecurringTransactions'

type ViewMode = 'cards' | 'list'

export function RecurringTransactionsPage() {
  const [showModal, setShowModal] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState<RecurringTransaction | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<ViewMode>('cards')
  const [filters, setFilters] = useState({
    type: '',
    frequency: '',
    accountId: '',
    categoryId: '',
    isActive: '',
  })

  const { data: recurringTransactions, isLoading, error } = useRecurringTransactions()

  const handleCreateTransaction = () => {
    setEditingTransaction(null)
    setShowModal(true)
  }

  const handleEditTransaction = (transaction: RecurringTransaction) => {
    setEditingTransaction(transaction)
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
    setEditingTransaction(null)
  }

  const filteredTransactions = recurringTransactions?.filter((transaction) => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !filters.type || transaction.type === filters.type
    const matchesFrequency = !filters.frequency || transaction.frequency === filters.frequency
    const matchesAccount = !filters.accountId || transaction.accountId === filters.accountId
    const matchesCategory = !filters.categoryId || transaction.categoryId === filters.categoryId
    const matchesActive = !filters.isActive ||
      (filters.isActive === 'true' ? transaction.isActive : !transaction.isActive)

    return matchesSearch && matchesType && matchesFrequency && matchesAccount && matchesCategory && matchesActive
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="glass-deep p-6 rounded-2xl shadow-elegant">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
                <Repeat className="h-8 w-8" />
                Transações Recorrentes
              </h1>
              <p className="text-lg text-muted-foreground mt-2">
                Configure transações que se repetem automaticamente
              </p>
            </div>
          </div>
        </div>
        <div className="glass-deep p-8 rounded-2xl shadow-elegant">
          <div className="py-16 text-center">
            <h3 className="mb-4 text-2xl font-bold text-red-600">
              Erro ao carregar transações recorrentes
            </h3>
            <p className="text-lg text-muted-foreground">
              Ocorreu um erro ao carregar as transações recorrentes. Tente novamente.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Repeat className="h-8 w-8" />
              Transações Recorrentes
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Configure transações que se repetem automaticamente
            </p>
          </div>

          <Button
            onClick={handleCreateTransaction}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            <Plus className="h-5 w-5 mr-2" />
            Nova Recorrente
          </Button>
        </div>
      </div>

      {/* Stats */}
      <RecurringTransactionsStats transactions={recurringTransactions || []} />

      {/* Search and Filters */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Buscar transações recorrentes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtros
          </Button>
        </div>

        {showFilters && (
          <div className="mt-4">
            <RecurringTransactionsFilters
              filters={filters}
              onFiltersChange={setFilters}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>
        )}
      </div>

      {/* Transactions List */}
      {viewMode === 'cards' ? (
        <RecurringTransactionsList
          transactions={filteredTransactions || []}
          onEdit={handleEditTransaction}
        />
      ) : (
        <RecurringTransactionsListView
          transactions={filteredTransactions || []}
          onEdit={handleEditTransaction}
        />
      )}

      {/* Modal */}
      <RecurringTransactionModal
        isOpen={showModal}
        onClose={handleCloseModal}
        transaction={editingTransaction}
      />
    </div>
  )
}
