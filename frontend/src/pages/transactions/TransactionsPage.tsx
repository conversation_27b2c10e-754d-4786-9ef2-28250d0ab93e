import { useState } from 'react'
import { Credit<PERSON>ard, Plus, Repeat } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useTransactions, useTransactionStats, useDeleteTransaction, useDuplicateTransaction, useUpdateInstallmentStatus } from '@/hooks/useTransactions'
import { expandTransactionsToInstallments } from '@/lib/transaction-utils'
import { TransactionsFilters } from '@/components/transactions/TransactionsFilters'
import { TransactionsStatsCards } from '@/components/transactions/TransactionsStatsCards'
import { TransactionsDataTable } from '@/components/transactions/TransactionsDataTable'
import { TransactionModal } from '@/components/transactions/TransactionModalNew'
import type { TransactionFilters, Transaction } from '@/types/transaction.types'

export function TransactionsPage() {
  const [filters, setFilters] = useState<TransactionFilters>({
    page: 1,
    limit: 20,
    sortBy: 'transactionDate',
    sortOrder: 'desc',
  })

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null)

  // Fetch data
  const {
    data: transactionsData,
    isLoading: isLoadingTransactions,
    error: transactionsError
  } = useTransactions(filters)

  // Remove stats call for now since the endpoint doesn't exist
  // const {
  //   data: stats,
  //   isLoading: isLoadingStats
  // } = useTransactionStats(filters)

  // Mutations
  const deleteMutation = useDeleteTransaction()
  const duplicateMutation = useDuplicateTransaction()
  const updateInstallmentStatusMutation = useUpdateInstallmentStatus()

  const handleFiltersChange = (newFilters: TransactionFilters) => {
    setFilters(newFilters)
  }

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      sortBy: 'transactionDate',
      sortOrder: 'desc',
    })
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, limit: pageSize, page: 1 }))
  }

  const handleEdit = (transactionId: string) => {
    const transaction = transactionsData?.data?.find(t => t.id === transactionId)
    if (transaction) {
      setSelectedTransaction(transaction)
      setIsEditModalOpen(true)
    }
  }

  const handleDelete = async (transactionId: string) => {
    const transaction = transactionsData?.data?.find(t => t.id === transactionId)
    if (transaction && window.confirm(`Tem certeza que deseja deletar a transação "${transaction.description}"?`)) {
      await deleteMutation.mutateAsync(transactionId)
    }
  }

  const handleDuplicate = async (transactionId: string) => {
    await duplicateMutation.mutateAsync({ id: transactionId })
  }

  const handleView = (transactionId: string) => {
    // TODO: Implement view modal or navigate to detail page
    console.log('View transaction:', transactionId)
  }

  const handleToggleInstallmentStatus = async (transactionId: string, installmentNumber: number, isPaid: boolean) => {
    try {
      await updateInstallmentStatusMutation.mutateAsync({
        transactionId,
        installmentNumber,
        isPaid
      })
    } catch (error) {
      console.error('Error updating installment status:', error)
    }
  }

  // Transform transactions to expanded rows for table display
  const expandedData = transactionsData?.data ? expandTransactionsToInstallments(transactionsData.data) : []

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <CreditCard className="h-8 w-8" />
              Transações
            </h1>
            <p className="text-lg text-muted-foreground">
              Gerencie todas as suas transações financeiras
            </p>
          </div>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            <Plus className="h-5 w-5 mr-2" />
            Nova Transação
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <TransactionsStatsCards
        summary={transactionsData?.summary}
        isLoading={isLoadingTransactions}
      />

      {/* Include Recurring Toggle */}
      <div className="glass-deep p-4 rounded-2xl shadow-elegant mb-6">
        <div className="flex items-center space-x-3">
          <Switch
            id="include-recurring"
            checked={filters.includeRecurring || false}
            onCheckedChange={(checked) =>
              handleFiltersChange({ ...filters, includeRecurring: checked })
            }
          />
          <Label htmlFor="include-recurring" className="flex items-center gap-2 cursor-pointer">
            <Repeat className="h-4 w-4" />
            Incluir Transações Recorrentes
          </Label>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Mostra transações futuras baseadas nas configurações recorrentes
        </p>
      </div>

      {/* Filters */}
      <TransactionsFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
        isLoading={isLoadingTransactions}
      />

      {/* Transactions Table */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gradient mb-2">Lista de Transações</h2>
          <p className="text-muted-foreground">
            {transactionsData?.pagination?.total || 0} transação(ões) encontrada(s)
          </p>
        </div>

        <TransactionsDataTable
          data={expandedData}
          isLoading={isLoadingTransactions}
          pagination={transactionsData?.pagination}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onDuplicate={handleDuplicate}
          onView={handleView}
          onToggleInstallmentStatus={handleToggleInstallmentStatus}
        />
      </div>

      {/* Create Transaction Modal */}
      <TransactionModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        mode="create"
      />

      {/* Edit Transaction Modal */}
      <TransactionModal
        open={isEditModalOpen}
        onOpenChange={(open) => {
          setIsEditModalOpen(open)
          if (!open) {
            setSelectedTransaction(null)
          }
        }}
        transaction={selectedTransaction}
        mode="edit"
      />
    </div>
  )
}
