import { useState, useEffect } from 'react'
import { Navigate, Link } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { TrendingUp, Eye, EyeOff, AlertCircle, CheckCircle2, User, Mail, Lock } from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

const registerSchema = z.object({
  name: z
    .string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome muito longo')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  email: z
    .string()
    .min(1, '<PERSON><PERSON> obrigat<PERSON>')
    .email('Formato de email inválido'),
  password: z
    .string()
    .min(6, 'Senha deve ter pelo menos 6 caracteres')
    .max(100, 'Senha muito longa')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula e 1 número'),
  confirmPassword: z
    .string()
    .min(1, 'Confirmação de senha é obrigatória'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Senhas não coincidem',
  path: ['confirmPassword'],
})

type RegisterFormData = z.infer<typeof registerSchema>

export function RegisterPage() {
  const { register: registerUser, isAuthenticated, isLoading } = useAuthStore()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, touchedFields },
    watch,
    setFocus,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    mode: 'onChange', // Validação em tempo real
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  // Auto-focus no campo nome quando a página carrega
  useEffect(() => {
    setFocus('name')
  }, [setFocus])

  // Watch para validação em tempo real
  const watchedName = watch('name')
  const watchedEmail = watch('email')
  const watchedPassword = watch('password')
  const watchedConfirmPassword = watch('confirmPassword')

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  const onSubmit = async (data: RegisterFormData) => {
    try {
      await registerUser(data.email, data.password, data.name)
      setRegistrationSuccess(true)
      // O redirecionamento será automático após o registro bem-sucedido
    } catch (error) {
      // Error is handled by the store
    }
  }

  // Função para determinar o estado visual do campo
  const getFieldState = (fieldName: keyof RegisterFormData) => {
    const hasError = !!errors[fieldName]
    const isTouched = touchedFields[fieldName]
    let hasValue = false
    
    switch (fieldName) {
      case 'name':
        hasValue = !!watchedName
        break
      case 'email':
        hasValue = !!watchedEmail
        break
      case 'password':
        hasValue = !!watchedPassword
        break
      case 'confirmPassword':
        hasValue = !!watchedConfirmPassword
        break
    }
    
    if (hasError && isTouched) return 'error'
    if (!hasError && isTouched && hasValue) return 'success'
    return 'default'
  }

  // Função para calcular força da senha
  const getPasswordStrength = (password: string) => {
    if (!password) return { score: 0, label: '', color: '' }
    
    let score = 0
    if (password.length >= 6) score++
    if (password.length >= 8) score++
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[^a-zA-Z\d]/.test(password)) score++
    
    if (score <= 2) return { score, label: 'Fraca', color: 'bg-red-500' }
    if (score <= 4) return { score, label: 'Média', color: 'bg-yellow-500' }
    return { score, label: 'Forte', color: 'bg-green-500' }
  }

  const passwordStrength = getPasswordStrength(watchedPassword)

  if (registrationSuccess) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8 text-center glass-deep p-8 rounded-2xl">
          <div className="flex justify-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-success shadow-glow">
              <CheckCircle2 className="h-12 w-12 text-white" />
            </div>
          </div>
          <div>
            <h2 className="text-3xl font-bold text-gradient-deep">Cadastro realizado!</h2>
            <p className="mt-3 text-muted-foreground">
              Sua conta foi criada com sucesso. Você será redirecionado automaticamente.
            </p>
          </div>
          <div className="flex justify-center">
            <LoadingSpinner size="sm" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow">
              <TrendingUp className="h-10 w-10 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-4xl font-bold text-gradient-deep">
            Criar Nova Conta
          </h2>
          <p className="mt-3 text-base text-muted-foreground">
            Preencha os dados abaixo para criar sua conta
          </p>
        </div>

        <form className="mt-8 space-y-6 glass-deep p-8 rounded-2xl" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            {/* Campo Nome */}
            <div className="space-y-2">
              <label 
                htmlFor="name" 
                className="text-sm font-medium text-foreground flex items-center"
              >
                <User className="h-4 w-4 mr-2" />
                Nome Completo
              </label>
              <div className="relative">
                <Input
                  id="name"
                  {...register('name')}
                  type="text"
                  autoComplete="name"
                  placeholder="Seu nome completo"
                  className={cn(
                    "transition-all duration-200",
                    getFieldState('name') === 'error' && 
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('name') === 'success' && 
                      "border-green-500 focus-visible:ring-green-500"
                  )}
                  aria-invalid={errors.name ? 'true' : 'false'}
                  aria-describedby={errors.name ? 'name-error' : undefined}
                />
                {getFieldState('name') === 'success' && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                )}
              </div>
              {errors.name && (
                <p 
                  id="name-error"
                  className="text-sm text-destructive flex items-center mt-1"
                  role="alert"
                >
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* Campo Email */}
            <div className="space-y-2">
              <label 
                htmlFor="email" 
                className="text-sm font-medium text-foreground flex items-center"
              >
                <Mail className="h-4 w-4 mr-2" />
                Email
              </label>
              <div className="relative">
                <Input
                  id="email"
                  {...register('email')}
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  className={cn(
                    "transition-all duration-200",
                    getFieldState('email') === 'error' && 
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('email') === 'success' && 
                      "border-green-500 focus-visible:ring-green-500"
                  )}
                  aria-invalid={errors.email ? 'true' : 'false'}
                  aria-describedby={errors.email ? 'email-error' : undefined}
                />
                {getFieldState('email') === 'success' && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                )}
              </div>
              {errors.email && (
                <p 
                  id="email-error"
                  className="text-sm text-destructive flex items-center mt-1"
                  role="alert"
                >
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Campo Senha */}
            <div className="space-y-2">
              <label 
                htmlFor="password" 
                className="text-sm font-medium text-foreground flex items-center"
              >
                <Lock className="h-4 w-4 mr-2" />
                Senha
              </label>
              <div className="relative">
                <Input
                  id="password"
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  placeholder="Sua senha"
                  className={cn(
                    "pr-10 transition-all duration-200",
                    getFieldState('password') === 'error' && 
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('password') === 'success' && 
                      "border-green-500 focus-visible:ring-green-500"
                  )}
                  aria-invalid={errors.password ? 'true' : 'false'}
                  aria-describedby={errors.password ? 'password-error password-strength' : 'password-strength'}
                />
                <div className="absolute inset-y-0 right-0 flex items-center">
                  {getFieldState('password') === 'success' && (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                  )}
                  <button
                    type="button"
                    className="flex items-center pr-3 text-secondary-400 hover:text-secondary-300 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? 'Ocultar senha' : 'Mostrar senha'}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* Indicador de força da senha */}
              {watchedPassword && (
                <div id="password-strength" className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-secondary-400">Força da senha:</span>
                    <span className={cn(
                      "text-xs font-medium",
                      passwordStrength.score <= 2 && "text-red-400",
                      passwordStrength.score > 2 && passwordStrength.score <= 4 && "text-yellow-400",
                      passwordStrength.score > 4 && "text-green-400"
                    )}>
                      {passwordStrength.label}
                    </span>
                  </div>
                  <div className="w-full bg-secondary-700 rounded-full h-2">
                    <div 
                      className={cn("h-2 rounded-full transition-all duration-300", passwordStrength.color)}
                      style={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                    />
                  </div>
                </div>
              )}
              
              {errors.password && (
                <p
                  id="password-error"
                  className="text-sm text-destructive flex items-center mt-1"
                  role="alert"
                >
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* Campo Confirmar Senha */}
            <div className="space-y-2">
              <label
                htmlFor="confirmPassword"
                className="text-sm font-medium text-foreground flex items-center"
              >
                <Lock className="h-4 w-4 mr-2" />
                Confirmar Senha
              </label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  {...register('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  placeholder="Confirme sua senha"
                  className={cn(
                    "pr-10 transition-all duration-200",
                    getFieldState('confirmPassword') === 'error' &&
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('confirmPassword') === 'success' &&
                      "border-green-500 focus-visible:ring-green-500"
                  )}
                  aria-invalid={errors.confirmPassword ? 'true' : 'false'}
                  aria-describedby={errors.confirmPassword ? 'confirm-password-error' : undefined}
                />
                <div className="absolute inset-y-0 right-0 flex items-center">
                  {getFieldState('confirmPassword') === 'success' && (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                  )}
                  <button
                    type="button"
                    className="flex items-center pr-3 text-secondary-400 hover:text-secondary-300 transition-colors"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    aria-label={showConfirmPassword ? 'Ocultar confirmação de senha' : 'Mostrar confirmação de senha'}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
              {errors.confirmPassword && (
                <p
                  id="confirm-password-error"
                  className="text-sm text-destructive flex items-center mt-1"
                  role="alert"
                >
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>
          </div>

          <div>
            <Button
              type="submit"
              disabled={isLoading || isSubmitting || !isValid}
              className="w-full py-3 text-base font-medium transition-all duration-200 hover:shadow-glow"
              size="lg"
            >
              {isLoading || isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Criando conta...</span>
                </div>
              ) : (
                'Criar Conta'
              )}
            </Button>
          </div>

          <div className="text-center space-y-4">
            <p className="text-sm text-secondary-400">
              Já tem uma conta?{' '}
              <Link
                to="/login"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
              >
                Faça login aqui
              </Link>
            </p>

            <div className="border-t border-secondary-700 pt-4">
              <p className="text-xs text-secondary-500">
                Ao criar uma conta, você concorda com nossos{' '}
                <a href="#" className="text-primary-400 hover:text-primary-300">
                  Termos de Uso
                </a>{' '}
                e{' '}
                <a href="#" className="text-primary-400 hover:text-primary-300">
                  Política de Privacidade
                </a>
              </p>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
