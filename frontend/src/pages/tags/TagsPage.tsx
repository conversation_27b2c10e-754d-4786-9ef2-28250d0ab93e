import { useState } from 'react'
import { Hash, Plus } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useTagStats } from '@/hooks/useTags'
import { TagsList } from '@/components/tags/TagsList'
import { TagModal } from '@/components/tags/TagModal'
import type { TagModalState } from '@/types/tag.types'

export function TagsPage() {
  const [modalState, setModalState] = useState<TagModalState>({
    isOpen: false,
    mode: 'create',
  })

  const { data: stats, isLoading: statsLoading } = useTagStats()

  const handleCreateTag = () => {
    setModalState({
      isOpen: true,
      mode: 'create',
    })
  }

  const handleEditTag = (tag: any) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      tag,
    })
  }

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      mode: 'create',
    })
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Hash className="h-8 w-8" />
              Tags
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Gerencie as tags para organizar suas transações
            </p>
          </div>
          <Button
            onClick={handleCreateTag}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200 gap-2"
          >
            <Plus className="h-5 w-5" />
            Nova Tag
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Total de Tags
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
              <Hash className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-foreground">
              {statsLoading ? '...' : (stats as any)?.data?.total || 0}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Tags cadastradas no sistema
            </p>
          </CardContent>
        </Card>

        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Tags Ativas
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-success shadow-soft">
              <Hash className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-foreground">
              {statsLoading ? '...' : (stats as any)?.data?.active || 0}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Tags disponíveis para uso
            </p>
          </CardContent>
        </Card>

        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Tags Arquivadas
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-warning shadow-soft">
              <Hash className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-foreground">
              {statsLoading ? '...' : (stats as any)?.data?.archived || 0}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Tags removidas do uso
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tags List */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-bold text-gradient">Lista de Tags</CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            Visualize e gerencie todas as suas tags
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TagsList onEditTag={handleEditTag} />
        </CardContent>
      </Card>

      {/* Modal */}
      <TagModal
        isOpen={modalState.isOpen}
        mode={modalState.mode}
        tag={modalState.tag}
        onClose={handleCloseModal}
      />
    </div>
  )
}
