import { useMemo } from 'react'
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  Legend
} from 'recharts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TrendingUp, TrendingDown } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'

interface FinancialTrendsData {
  date: string
  income: number
  expenses: number
  balance: number
  previousIncome?: number
  previousExpenses?: number
  previousBalance?: number
}

interface FinancialTrendsChartProps {
  data: FinancialTrendsData[]
  isLoading?: boolean
  showComparison?: boolean
  className?: string
}

export function FinancialTrendsChart({ 
  data, 
  isLoading, 
  showComparison = false, 
  className 
}: FinancialTrendsChartProps) {
  const chartData = useMemo(() => {
    return data.map(item => ({
      ...item,
      formattedDate: format(new Date(item.date), 'MMM yyyy', { locale: ptBR })
    }))
  }, [data])

  const trends = useMemo(() => {
    if (data.length < 2) return null

    const latest = data[data.length - 1]
    const previous = data[data.length - 2]

    const incomeChange = ((latest.income - previous.income) / previous.income) * 100
    const expensesChange = ((latest.expenses - previous.expenses) / previous.expenses) * 100
    const balanceChange = ((latest.balance - previous.balance) / previous.balance) * 100

    return {
      income: { value: incomeChange, isPositive: incomeChange >= 0 },
      expenses: { value: Math.abs(expensesChange), isPositive: expensesChange <= 0 },
      balance: { value: Math.abs(balanceChange), isPositive: balanceChange >= 0 }
    }
  }, [data])

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="glass-deep p-4 rounded-lg shadow-elegant border">
          <p className="font-semibold text-sm mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-muted-foreground">{entry.name}:</span>
              <span className="font-medium">{formatCurrency(entry.value)}</span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  if (isLoading) {
    return (
      <Card className={`glass-deep border-0 shadow-elegant ${className}`}>
        <CardHeader>
          <CardTitle>Tendências Financeiras</CardTitle>
          <CardDescription>Evolução das receitas, despesas e saldo ao longo do tempo</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`glass-deep border-0 shadow-elegant ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-gradient-deep">Tendências Financeiras</CardTitle>
            <CardDescription>
              Evolução das receitas, despesas e saldo ao longo do tempo
            </CardDescription>
          </div>
          
          {trends && (
            <div className="flex gap-4 text-sm">
              <div className="flex items-center gap-1">
                {trends.income.isPositive ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={trends.income.isPositive ? 'text-green-500' : 'text-red-500'}>
                  {trends.income.value.toFixed(1)}%
                </span>
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-1))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-1))" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="expensesGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-2))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-2))" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="balanceGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-3))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-3))" stopOpacity={0}/>
                </linearGradient>
              </defs>
              
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="formattedDate" 
                axisLine={false}
                tickLine={false}
                className="text-xs"
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                className="text-xs"
                tickFormatter={(value) => formatCurrency(value)}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              <Area
                type="monotone"
                dataKey="income"
                stackId="1"
                stroke="hsl(var(--chart-1))"
                fill="url(#incomeGradient)"
                name="Receitas"
                strokeWidth={2}
              />
              <Area
                type="monotone"
                dataKey="expenses"
                stackId="2"
                stroke="hsl(var(--chart-2))"
                fill="url(#expensesGradient)"
                name="Despesas"
                strokeWidth={2}
              />
              <Area
                type="monotone"
                dataKey="balance"
                stackId="3"
                stroke="hsl(var(--chart-3))"
                fill="url(#balanceGradient)"
                name="Saldo"
                strokeWidth={2}
              />
              
              {showComparison && (
                <>
                  <Area
                    type="monotone"
                    dataKey="previousIncome"
                    stroke="hsl(var(--chart-1))"
                    fill="none"
                    strokeDasharray="5 5"
                    name="Receitas (Anterior)"
                    strokeWidth={1}
                    opacity={0.6}
                  />
                  <Area
                    type="monotone"
                    dataKey="previousExpenses"
                    stroke="hsl(var(--chart-2))"
                    fill="none"
                    strokeDasharray="5 5"
                    name="Despesas (Anterior)"
                    strokeWidth={1}
                    opacity={0.6}
                  />
                </>
              )}
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
