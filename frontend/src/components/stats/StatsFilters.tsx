import { useState } from 'react'
import { Calendar, Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export interface StatsFilters {
  period: 'month' | 'quarter' | 'year' | 'custom'
  startDate?: Date
  endDate?: Date
  compareWithPrevious: boolean
}

interface StatsFiltersProps {
  filters: StatsFilters
  onFiltersChange: (filters: StatsFilters) => void
  className?: string
}

export function StatsFilters({ filters, onFiltersChange, className }: StatsFiltersProps) {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [tempStartDate, setTempStartDate] = useState<Date | undefined>(filters.startDate)
  const [tempEndDate, setTempEndDate] = useState<Date | undefined>(filters.endDate)

  const handlePeriodChange = (period: StatsFilters['period']) => {
    const now = new Date()
    let startDate: Date | undefined
    let endDate: Date | undefined

    switch (period) {
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        break
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3)
        startDate = new Date(now.getFullYear(), quarter * 3, 1)
        endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0)
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31)
        break
      case 'custom':
        // Keep existing dates or set to undefined
        startDate = filters.startDate
        endDate = filters.endDate
        break
    }

    onFiltersChange({
      ...filters,
      period,
      startDate,
      endDate
    })
  }

  const handleCustomDateApply = () => {
    onFiltersChange({
      ...filters,
      period: 'custom',
      startDate: tempStartDate,
      endDate: tempEndDate
    })
    setIsDatePickerOpen(false)
  }

  const handleCompareToggle = () => {
    onFiltersChange({
      ...filters,
      compareWithPrevious: !filters.compareWithPrevious
    })
  }

  const clearFilters = () => {
    const now = new Date()
    onFiltersChange({
      period: 'month',
      startDate: new Date(now.getFullYear(), now.getMonth(), 1),
      endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0),
      compareWithPrevious: false
    })
  }

  const formatDateRange = () => {
    if (!filters.startDate || !filters.endDate) return 'Selecionar período'
    
    return `${format(filters.startDate, 'dd/MM/yyyy', { locale: ptBR })} - ${format(filters.endDate, 'dd/MM/yyyy', { locale: ptBR })}`
  }

  return (
    <Card className={`glass-deep border-0 shadow-elegant ${className}`}>
      <CardContent className="p-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Period Selector */}
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={filters.period} onValueChange={handlePeriodChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Período" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">Este Mês</SelectItem>
                <SelectItem value="quarter">Este Trimestre</SelectItem>
                <SelectItem value="year">Este Ano</SelectItem>
                <SelectItem value="custom">Personalizado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Custom Date Range */}
          {filters.period === 'custom' && (
            <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-64 justify-start text-left font-normal">
                  <Calendar className="mr-2 h-4 w-4" />
                  {formatDateRange()}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-4 space-y-4">
                  <div>
                    <label className="text-sm font-medium">Data Inicial</label>
                    <CalendarComponent
                      mode="single"
                      selected={tempStartDate}
                      onSelect={setTempStartDate}
                      locale={ptBR}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Data Final</label>
                    <CalendarComponent
                      mode="single"
                      selected={tempEndDate}
                      onSelect={setTempEndDate}
                      locale={ptBR}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleCustomDateApply} size="sm">
                      Aplicar
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsDatePickerOpen(false)} 
                      size="sm"
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}

          {/* Compare Toggle */}
          <Button
            variant={filters.compareWithPrevious ? "default" : "outline"}
            onClick={handleCompareToggle}
            size="sm"
          >
            {filters.compareWithPrevious ? "Comparando" : "Comparar"}
          </Button>

          {/* Clear Filters */}
          <Button variant="ghost" onClick={clearFilters} size="sm">
            <X className="h-4 w-4 mr-1" />
            Limpar
          </Button>

          {/* Current Period Display */}
          <div className="text-sm text-muted-foreground">
            {filters.period !== 'custom' && formatDateRange()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
