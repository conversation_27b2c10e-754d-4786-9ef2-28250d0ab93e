import { 
  TrendingUp, 
  TrendingDown, 
  ArrowUpR<PERSON>, 
  ArrowDownRight,
  Minus,
  HelpCircle
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface StatCardData {
  title: string
  value: number | string
  previousValue?: number
  icon: React.ComponentType<{ className?: string }>
  color: string
  textColor: string
  description?: string
  tooltip?: string
  format?: 'currency' | 'number' | 'percentage' | 'text'
  trend?: {
    value: number
    isPositive: boolean
    label: string
  }
}

interface EnhancedStatsCardsProps {
  stats: StatCardData[]
  isLoading?: boolean
  showComparison?: boolean
  className?: string
}

function StatCard({ 
  stat, 
  showComparison = false, 
  isLoading = false 
}: { 
  stat: StatCardData
  showComparison?: boolean
  isLoading?: boolean
}) {
  const formatValue = (value: number | string, format?: string) => {
    if (typeof value === 'string') return value
    
    switch (format) {
      case 'currency':
        return formatCurrency(value)
      case 'number':
        return formatNumber(value)
      case 'percentage':
        return `${value.toFixed(1)}%`
      default:
        return value.toString()
    }
  }

  const calculateTrend = () => {
    if (!showComparison || !stat.previousValue || typeof stat.value !== 'number') {
      return stat.trend
    }

    const change = stat.value - stat.previousValue
    const percentChange = (change / stat.previousValue) * 100
    
    return {
      value: Math.abs(percentChange),
      isPositive: change >= 0,
      label: 'vs período anterior'
    }
  }

  const trend = calculateTrend()

  const TrendIcon = () => {
    if (!trend) return null
    
    if (trend.value === 0) {
      return <Minus className="h-3 w-3 text-muted-foreground" />
    }
    
    return trend.isPositive ? (
      <ArrowUpRight className="h-3 w-3 text-green-500" />
    ) : (
      <ArrowDownRight className="h-3 w-3 text-red-500" />
    )
  }

  if (isLoading) {
    return (
      <Card className="glass-deep border-0 shadow-elegant">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-muted rounded-xl"></div>
              </div>
              <div className="ml-4 flex-1">
                <div className="h-4 bg-muted rounded w-24 mb-2"></div>
                <div className="h-8 bg-muted rounded w-32"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-deep border-0 shadow-elegant hover:shadow-glow transition-all duration-300 group">
      <CardContent className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={cn(
              "flex h-12 w-12 items-center justify-center rounded-xl shadow-soft transition-transform group-hover:scale-105",
              stat.color
            )}>
              <stat.icon className="h-6 w-6 text-white" />
            </div>
          </div>
          
          <div className="ml-4 flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-muted-foreground truncate">
                {stat.title}
              </p>
              {stat.tooltip && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-3 w-3 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-xs">{stat.tooltip}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            
            <div className="flex items-baseline gap-2 mt-1">
              <p className={cn("text-2xl font-bold truncate", stat.textColor)}>
                {formatValue(stat.value, stat.format)}
              </p>
              
              {trend && trend.value > 0 && (
                <Badge 
                  variant="outline" 
                  className={cn(
                    "text-xs px-2 py-0.5 flex items-center gap-1",
                    trend.isPositive 
                      ? "text-green-600 border-green-600 bg-green-50 dark:bg-green-950" 
                      : "text-red-600 border-red-600 bg-red-50 dark:bg-red-950"
                  )}
                >
                  <TrendIcon />
                  {trend.value.toFixed(1)}%
                </Badge>
              )}
            </div>
            
            {stat.description && (
              <p className="text-xs text-muted-foreground mt-1 truncate">
                {stat.description}
              </p>
            )}
            
            {trend && trend.label && (
              <p className="text-xs text-muted-foreground mt-1">
                {trend.label}
              </p>
            )}
          </div>
        </div>
        
        {showComparison && stat.previousValue && typeof stat.value === 'number' && (
          <div className="mt-4 pt-4 border-t border-border/50">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Período anterior:</span>
              <span>{formatValue(stat.previousValue, stat.format)}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function EnhancedStatsCards({ 
  stats, 
  isLoading = false, 
  showComparison = false, 
  className 
}: EnhancedStatsCardsProps) {
  return (
    <div className={cn("grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4", className)}>
      {stats.map((stat, index) => (
        <StatCard 
          key={index} 
          stat={stat} 
          showComparison={showComparison}
          isLoading={isLoading}
        />
      ))}
    </div>
  )
}
