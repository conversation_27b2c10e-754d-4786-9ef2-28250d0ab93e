import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { useQuery } from '@tanstack/react-query'
import { accountsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { useCreateRecurringTransaction } from '@/hooks/useRecurringTransactions'
import { 
  TRANSACTION_TYPE_LABELS,
  RECURRENCE_FREQUENCY_LABELS,
  TRANSACTION_TYPES,
  RECURRENCE_FREQUENCIES
} from '@/types/transaction.types'
import type { RecurringTransaction } from '@/hooks/useRecurringTransactions'

const formSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  fixedAmount: z.string().min(1, 'Valor é obrigatório'),
  type: z.enum(['INCOME', 'EXPENSE', 'TRANSFER'], {
    required_error: 'Tipo é obrigatório',
  }),
  frequency: z.enum(['DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', 'BIANNUAL', 'YEARLY'], {
    required_error: 'Frequência é obrigatória',
  }),
  startDate: z.string().min(1, 'Data de início é obrigatória'),
  endDate: z.string().optional(),
  accountId: z.string().min(1, 'Conta é obrigatória'),
  categoryId: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

interface RecurringTransactionModalProps {
  isOpen: boolean
  onClose: () => void
  transaction?: RecurringTransaction | null
}

export function RecurringTransactionModal({
  isOpen,
  onClose,
  transaction
}: RecurringTransactionModalProps) {
  const isEditing = !!transaction

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: '',
      fixedAmount: '',
      type: 'EXPENSE',
      frequency: 'MONTHLY',
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      accountId: '',
      categoryId: '',
    },
  })

  const { data: accounts, isLoading: accountsLoading } = useQuery({
    queryKey: ['accounts'],
    queryFn: () => accountsApi.getAll(),
    select: (data) => (data as any).data || []
  })

  const { data: categories, isLoading: categoriesLoading } = useCategoriesForForms()

  const createRecurringTransaction = useCreateRecurringTransaction()

  // Reset form when modal opens/closes or transaction changes
  useEffect(() => {
    if (isOpen) {
      if (transaction) {
        form.reset({
          description: transaction.description,
          fixedAmount: transaction.fixedAmount.toString(),
          type: transaction.type as any,
          frequency: transaction.frequency as any,
          startDate: transaction.startDate.split('T')[0],
          endDate: transaction.endDate ? transaction.endDate.split('T')[0] : '',
          accountId: transaction.accountId,
          categoryId: transaction.categoryId || '',
        })
      } else {
        form.reset({
          description: '',
          fixedAmount: '',
          type: 'EXPENSE',
          frequency: 'MONTHLY',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          accountId: '',
          categoryId: '',
        })
      }
    }
  }, [isOpen, transaction, form])

  const onSubmit = async (data: FormData) => {
    try {
      const payload = {
        description: data.description,
        fixedAmount: parseFloat(data.fixedAmount),
        type: data.type,
        frequency: data.frequency,
        startDate: data.startDate,
        endDate: data.endDate || undefined,
        accountId: data.accountId,
        categoryId: data.categoryId === 'no-category' ? undefined : data.categoryId || undefined,
      }

      await createRecurringTransaction.mutateAsync(payload)
      onClose()
    } catch (error) {
      console.error('Error creating recurring transaction:', error)
    }
  }

  const isLoading = accountsLoading || categoriesLoading || createRecurringTransaction.isPending

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] glass-deep border-0">
        <DialogHeader>
          <DialogTitle className="text-gradient-deep">
            {isEditing ? 'Editar Transação Recorrente' : 'Nova Transação Recorrente'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Edite os dados da transação recorrente.'
              : 'Configure uma transação que se repetirá automaticamente.'
            }
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Salário, Aluguel, Conta de luz..."
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Amount */}
                <FormField
                  control={form.control}
                  name="fixedAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          step="0.01"
                          placeholder="0,00"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Type */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(TRANSACTION_TYPE_LABELS).map(([key, label]) => (
                            <SelectItem key={key} value={key}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Frequency */}
                <FormField
                  control={form.control}
                  name="frequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Frequência</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a frequência" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(RECURRENCE_FREQUENCY_LABELS).map(([key, label]) => (
                            <SelectItem key={key} value={key}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Account */}
                <FormField
                  control={form.control}
                  name="accountId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Conta</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a conta" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {accounts?.map((account: any) => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Category */}
              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria (Opcional)</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="no-category">Nenhuma categoria</SelectItem>
                        {(categories as any)?.map((category: any) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Start Date */}
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data de Início</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* End Date */}
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data de Fim (Opcional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormDescription>
                        Deixe em branco para recorrência indefinida
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={createRecurringTransaction.isPending}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={createRecurringTransaction.isPending}
                  className="bg-gradient-deep text-white"
                >
                  {createRecurringTransaction.isPending ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      {isEditing ? 'Salvando...' : 'Criando...'}
                    </>
                  ) : (
                    isEditing ? 'Salvar Alterações' : 'Criar Transação'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
