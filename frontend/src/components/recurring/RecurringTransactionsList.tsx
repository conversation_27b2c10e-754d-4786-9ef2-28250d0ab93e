import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Calendar,
  DollarSign,
  Repeat,
  Building2,
  Tag,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useDeleteRecurringTransaction } from '@/hooks/useRecurringTransactions'
import { 
  TRANSACTION_TYPE_LABELS, 
  RECURRENCE_FREQUENCY_LABELS,
  TRANSACTION_TYPE_COLORS 
} from '@/types/transaction.types'
import type { RecurringTransaction } from '@/hooks/useRecurringTransactions'

interface RecurringTransactionsListProps {
  transactions: RecurringTransaction[]
  onEdit: (transaction: RecurringTransaction) => void
}

export function RecurringTransactionsList({ 
  transactions, 
  onEdit 
}: RecurringTransactionsListProps) {
  const deleteRecurringTransaction = useDeleteRecurringTransaction()

  const handleDelete = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta transação recorrente?')) {
      await deleteRecurringTransaction.mutateAsync(id)
    }
  }

  if (transactions.length === 0) {
    return (
      <div className="glass-deep p-8 rounded-2xl shadow-elegant">
        <div className="py-16 text-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
            <Repeat className="h-10 w-10 text-white" />
          </div>
          <h3 className="mb-4 text-2xl font-bold text-gradient">
            Nenhuma transação recorrente encontrada
          </h3>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            Crie sua primeira transação recorrente para automatizar suas finanças.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {transactions.map((transaction) => (
          <RecurringTransactionCard
            key={transaction.id}
            transaction={transaction}
            onEdit={onEdit}
            onDelete={handleDelete}
          />
        ))}
      </div>
    </div>
  )
}

interface RecurringTransactionCardProps {
  transaction: RecurringTransaction
  onEdit: (transaction: RecurringTransaction) => void
  onDelete: (id: string) => void
}

function RecurringTransactionCard({ 
  transaction, 
  onEdit, 
  onDelete 
}: RecurringTransactionCardProps) {
  const typeColor = TRANSACTION_TYPE_COLORS[transaction.type as keyof typeof TRANSACTION_TYPE_COLORS]
  const typeLabel = TRANSACTION_TYPE_LABELS[transaction.type as keyof typeof TRANSACTION_TYPE_LABELS]
  const frequencyLabel = RECURRENCE_FREQUENCY_LABELS[transaction.frequency as keyof typeof RECURRENCE_FREQUENCY_LABELS]

  return (
    <Card className="glass-deep border-0 shadow-elegant hover:shadow-glow transition-all duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gradient-deep line-clamp-2">
              {transaction.description}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge 
                variant="outline" 
                className={`${typeColor} border-current`}
              >
                {typeLabel}
              </Badge>
              <Badge variant="outline" className="text-muted-foreground">
                <Repeat className="h-3 w-3 mr-1" />
                {frequencyLabel}
              </Badge>
              <Badge 
                variant={transaction.isActive ? "default" : "secondary"}
                className={transaction.isActive ? "bg-green-600 text-white" : ""}
              >
                {transaction.isActive ? 'Ativa' : 'Inativa'}
              </Badge>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(transaction)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete(transaction.id)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Excluir
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Amount */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Valor:</span>
          <span className={`text-lg font-bold ${typeColor}`}>
            {formatCurrency(transaction.fixedAmount)}
          </span>
        </div>

        {/* Account */}
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Conta:</span>
          <span className="text-sm font-medium">{transaction.account.name}</span>
        </div>

        {/* Category */}
        {transaction.category && (
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Categoria:</span>
            <Badge 
              variant="outline" 
              style={{ 
                borderColor: transaction.category.color,
                color: transaction.category.color 
              }}
            >
              {transaction.category.name}
            </Badge>
          </div>
        )}

        {/* Dates */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Início:</span>
            <span className="text-sm">{formatDate(transaction.startDate)}</span>
          </div>
          {transaction.endDate && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Fim:</span>
              <span className="text-sm">{formatDate(transaction.endDate)}</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(transaction)}
            className="flex-1"
          >
            <Edit className="h-4 w-4 mr-2" />
            Editar
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
