import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Repeat, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  PauseCircle,
  Calendar,
  DollarSign
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { TRANSACTION_TYPE_LABELS, RECURRENCE_FREQUENCY_LABELS } from '@/types/transaction.types'
import type { RecurringTransaction } from '@/hooks/useRecurringTransactions'

interface RecurringTransactionsStatsProps {
  transactions: RecurringTransaction[]
}

export function RecurringTransactionsStats({ transactions }: RecurringTransactionsStatsProps) {
  const stats = {
    total: transactions.length,
    active: transactions.filter(t => t.isActive).length,
    inactive: transactions.filter(t => !t.isActive).length,
    income: transactions.filter(t => t.type === 'INCOME').length,
    expense: transactions.filter(t => t.type === 'EXPENSE').length,
    transfer: transactions.filter(t => t.type === 'TRANSFER').length,
    totalMonthlyIncome: transactions
      .filter(t => t.isActive && t.type === 'INCOME')
      .reduce((sum, t) => {
        // Convert frequency to monthly equivalent
        const multiplier = getMonthlyMultiplier(t.frequency)
        return sum + (t.fixedAmount * multiplier)
      }, 0),
    totalMonthlyExpense: transactions
      .filter(t => t.isActive && t.type === 'EXPENSE')
      .reduce((sum, t) => {
        const multiplier = getMonthlyMultiplier(t.frequency)
        return sum + (t.fixedAmount * multiplier)
      }, 0),
  }

  const netMonthlyAmount = stats.totalMonthlyIncome - stats.totalMonthlyExpense

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Transactions */}
      <Card className="glass-deep border-0 shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total de Recorrências
          </CardTitle>
          <Repeat className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gradient-deep">{stats.total}</div>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className="text-green-600 border-green-600">
              <CheckCircle className="h-3 w-3 mr-1" />
              {stats.active} ativas
            </Badge>
            {stats.inactive > 0 && (
              <Badge variant="outline" className="text-gray-500 border-gray-500">
                <PauseCircle className="h-3 w-3 mr-1" />
                {stats.inactive} inativas
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Income */}
      <Card className="glass-deep border-0 shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Receita Mensal Recorrente
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(stats.totalMonthlyIncome)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {stats.income} transação{stats.income !== 1 ? 'ões' : ''} de receita
          </p>
        </CardContent>
      </Card>

      {/* Monthly Expense */}
      <Card className="glass-deep border-0 shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Despesa Mensal Recorrente
          </CardTitle>
          <TrendingDown className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {formatCurrency(stats.totalMonthlyExpense)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {stats.expense} transação{stats.expense !== 1 ? 'ões' : ''} de despesa
          </p>
        </CardContent>
      </Card>

      {/* Net Monthly Amount */}
      <Card className="glass-deep border-0 shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Saldo Mensal Líquido
          </CardTitle>
          <DollarSign className={`h-4 w-4 ${netMonthlyAmount >= 0 ? 'text-green-600' : 'text-red-600'}`} />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${netMonthlyAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatCurrency(netMonthlyAmount)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Diferença entre receitas e despesas
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

// Helper function to convert frequency to monthly multiplier
function getMonthlyMultiplier(frequency: string): number {
  switch (frequency) {
    case 'DAILY':
      return 30 // Approximate days in a month
    case 'WEEKLY':
      return 4.33 // Approximate weeks in a month
    case 'BIWEEKLY':
      return 2.17 // Approximate bi-weeks in a month
    case 'MONTHLY':
      return 1
    case 'BIANNUAL':
      return 1/6 // Every 6 months
    case 'YEARLY':
      return 1/12 // Every 12 months
    default:
      return 1
  }
}
