import { useQuery } from '@tanstack/react-query'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { X, Grid3X3, List } from 'lucide-react'
import { accountsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import {
  TRANSACTION_TYPE_LABELS,
  RECURRENCE_FREQUENCY_LABELS,
  TRANSACTION_TYPES,
  RECURRENCE_FREQUENCIES
} from '@/types/transaction.types'

type ViewMode = 'cards' | 'list'

interface RecurringTransactionsFiltersProps {
  filters: {
    type: string
    frequency: string
    accountId: string
    categoryId: string
    isActive: string
  }
  onFiltersChange: (filters: any) => void
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
}

export function RecurringTransactionsFilters({
  filters,
  onFiltersChange,
  viewMode,
  onViewModeChange
}: RecurringTransactionsFiltersProps) {
  const { data: accounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: () => accountsApi.getAll(),
    select: (data) => data.data || []
  })

  const { data: categories } = useCategoriesForForms()

  const handleFilterChange = (key: string, value: string) => {
    // Convert "all-*" values back to empty strings for filtering logic
    const filterValue = value.startsWith('all-') ? '' : value
    onFiltersChange({
      ...filters,
      [key]: filterValue
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      type: '',
      frequency: '',
      accountId: '',
      categoryId: '',
      isActive: '',
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== '')

  return (
    <div className="space-y-4">
      {/* View Mode Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground">Visualização:</span>
          <ToggleGroup
            type="single"
            value={viewMode}
            onValueChange={(value) => value && onViewModeChange(value as ViewMode)}
            className="border rounded-lg p-1"
          >
            <ToggleGroupItem value="cards" aria-label="Visualização em cards" size="sm">
              <Grid3X3 className="h-4 w-4" />
              <span className="ml-2 hidden sm:inline">Cards</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="Visualização em lista" size="sm">
              <List className="h-4 w-4" />
              <span className="ml-2 hidden sm:inline">Lista</span>
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Limpar Filtros
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Transaction Type Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Tipo
          </label>
          <Select
            value={filters.type}
            onValueChange={(value) => handleFilterChange('type', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os tipos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-types">Todos os tipos</SelectItem>
              {Object.entries(TRANSACTION_TYPE_LABELS).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Frequency Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Frequência
          </label>
          <Select
            value={filters.frequency}
            onValueChange={(value) => handleFilterChange('frequency', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as frequências" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-frequencies">Todas as frequências</SelectItem>
              {Object.entries(RECURRENCE_FREQUENCY_LABELS).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Account Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Conta
          </label>
          <Select
            value={filters.accountId}
            onValueChange={(value) => handleFilterChange('accountId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as contas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-accounts">Todas as contas</SelectItem>
              {(accounts as any[])?.map((account: any) => (
                <SelectItem key={account.id} value={account.id}>
                  {account.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Categoria
          </label>
          <Select
            value={filters.categoryId}
            onValueChange={(value) => handleFilterChange('categoryId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as categorias" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-categories">Todas as categorias</SelectItem>
              {categories?.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Status
          </label>
          <Select
            value={filters.isActive}
            onValueChange={(value) => handleFilterChange('isActive', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-status">Todos os status</SelectItem>
              <SelectItem value="true">Ativas</SelectItem>
              <SelectItem value="false">Inativas</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}
