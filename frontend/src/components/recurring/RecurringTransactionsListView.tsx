import { useState } from 'react'
import { 
  Edit, 
  Trash2, 
  Calendar,
  DollarSign,
  Repeat,
  Building2,
  Tag,
  MoreVertical,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  Play,
  Pause,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useDeleteRecurringTransaction } from '@/hooks/useRecurringTransactions'
import { 
  TRANSACTION_TYPE_LABELS, 
  RECURRENCE_FREQUENCY_LABELS,
  TRANSACTION_TYPE_COLORS 
} from '@/types/transaction.types'
import type { RecurringTransaction } from '@/hooks/useRecurringTransactions'

interface RecurringTransactionsListViewProps {
  transactions: RecurringTransaction[]
  onEdit: (transaction: RecurringTransaction) => void
}

export function RecurringTransactionsListView({ 
  transactions, 
  onEdit 
}: RecurringTransactionsListViewProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const deleteRecurringTransaction = useDeleteRecurringTransaction()

  const handleDelete = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta transação recorrente?')) {
      await deleteRecurringTransaction.mutateAsync(id)
    }
  }

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INCOME':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'EXPENSE':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'TRANSFER':
        return <ArrowRightLeft className="h-4 w-4 text-blue-600" />
      default:
        return <DollarSign className="h-4 w-4 text-muted-foreground" />
    }
  }

  if (transactions.length === 0) {
    return (
      <Card className="glass-deep border-0 shadow-elegant">
        <CardContent className="py-16 text-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
            <Repeat className="h-10 w-10 text-white" />
          </div>
          <h3 className="mb-4 text-2xl font-bold text-gradient">
            Nenhuma transação recorrente encontrada
          </h3>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            Crie sua primeira transação recorrente para automatizar suas finanças.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-deep border-0 shadow-elegant">
      <CardContent className="p-0">
        <div className="divide-y divide-border">
          {transactions.map((transaction) => {
            const isExpanded = expandedItems.has(transaction.id)
            const typeColor = TRANSACTION_TYPE_COLORS[transaction.type as keyof typeof TRANSACTION_TYPE_COLORS]
            const typeLabel = TRANSACTION_TYPE_LABELS[transaction.type as keyof typeof TRANSACTION_TYPE_LABELS]
            const frequencyLabel = RECURRENCE_FREQUENCY_LABELS[transaction.frequency as keyof typeof RECURRENCE_FREQUENCY_LABELS]

            return (
              <div key={transaction.id} className="p-4 hover:bg-muted/30 transition-colors">
                {/* Main Row */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {/* Type Icon */}
                    <div className="flex-shrink-0">
                      {getTypeIcon(transaction.type)}
                    </div>

                    {/* Main Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-foreground truncate">
                          {transaction.description}
                        </h4>
                        <Badge 
                          variant={transaction.isActive ? "default" : "secondary"}
                          className={`text-xs ${transaction.isActive ? "bg-green-600 text-white" : ""}`}
                        >
                          {transaction.isActive ? 'Ativa' : 'Inativa'}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Badge variant="outline" className={`${typeColor} border-current text-xs`}>
                            {typeLabel}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Repeat className="h-3 w-3" />
                          <span>{frequencyLabel}</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(transaction.startDate)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Amount */}
                    <div className="flex-shrink-0 text-right">
                      <div className={`text-lg font-bold ${typeColor}`}>
                        {formatCurrency(transaction.fixedAmount)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {transaction.account.name}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(transaction.id)}
                        className="h-8 w-8 p-0"
                      >
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEdit(transaction)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDelete(transaction.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {isExpanded && (
                  <div className="mt-4 pt-4 border-t border-border">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                      {/* Account Details */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Building2 className="h-4 w-4" />
                          <span className="font-medium">Conta</span>
                        </div>
                        <div className="pl-6">
                          <div className="font-medium">{transaction.account.name}</div>
                          <div className="text-muted-foreground text-xs">
                            {transaction.account.type}
                          </div>
                        </div>
                      </div>

                      {/* Category Details */}
                      {transaction.category && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <Tag className="h-4 w-4" />
                            <span className="font-medium">Categoria</span>
                          </div>
                          <div className="pl-6">
                            <Badge 
                              variant="outline" 
                              style={{ 
                                borderColor: transaction.category.color,
                                color: transaction.category.color 
                              }}
                            >
                              {transaction.category.name}
                            </Badge>
                          </div>
                        </div>
                      )}

                      {/* Date Details */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span className="font-medium">Período</span>
                        </div>
                        <div className="pl-6 space-y-1">
                          <div>
                            <span className="text-muted-foreground">Início: </span>
                            <span>{formatDate(transaction.startDate)}</span>
                          </div>
                          {transaction.endDate && (
                            <div>
                              <span className="text-muted-foreground">Fim: </span>
                              <span>{formatDate(transaction.endDate)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 mt-4 pt-4 border-t border-border">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(transaction)}
                        className="flex items-center gap-2"
                      >
                        <Edit className="h-4 w-4" />
                        Editar
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
