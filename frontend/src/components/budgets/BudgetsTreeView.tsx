import { useState, useMemo } from 'react'
import { 
  ChevronDown, 
  ChevronRight, 
  Calendar, 
  Users, 
  FolderTree, 
  BarChart3,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  User
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import type { Budget, GroupedBudget } from '@/types/budget.types'
import type { TreeHierarchyConfig, TreeHierarchyLevel } from './BudgetsTreeViewConfig'

interface BudgetsTreeViewProps {
  budgets: GroupedBudget[]
  hierarchyConfig: TreeHierarchyConfig
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  className?: string
}

interface TreeNode {
  id: string
  label: string
  level: TreeHierarchyLevel
  budgets: GroupedBudget[]
  children: TreeNode[]
  totalPlanned: number
  totalSpent: number
  progress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
  metadata?: any
}

const MONTH_NAMES = [
  'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
  'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
]

export function BudgetsTreeView({
  budgets,
  hierarchyConfig,
  onEdit,
  onDelete,
  onViewDetails,
  className
}: BudgetsTreeViewProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  const treeData = useMemo(() => {
    return buildTreeStructure(budgets, hierarchyConfig)
  }, [budgets, hierarchyConfig])

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const getNodeIcon = (level: TreeHierarchyLevel, metadata?: any) => {
    switch (level) {
      case 'month':
        return Calendar
      case 'member':
        return Users
      case 'category':
        return FolderTree
      case 'status':
        return BarChart3
      default:
        return Target
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'over_budget':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'on_track':
        return <TrendingUp className="h-4 w-4 text-yellow-600" />
      case 'under_budget':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      default:
        return <Target className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress > 100) return 'bg-red-500'
    if (progress > 80) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getMemberInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const renderTreeNode = (node: TreeNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(node.id)
    const hasChildren = node.children.length > 0
    const Icon = getNodeIcon(node.level, node.metadata)

    return (
      <div key={node.id} className="space-y-2">
        <Collapsible open={isExpanded} onOpenChange={() => toggleNode(node.id)}>
          <div
            className={cn(
              'flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-muted/50',
              depth === 0 && 'bg-card border shadow-sm',
              depth === 1 && 'bg-muted/30 ml-4',
              depth === 2 && 'bg-muted/20 ml-8'
            )}
            style={{ marginLeft: depth * 16 }}
          >
            {/* Expand/Collapse Button */}
            {hasChildren && (
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            )}
            {!hasChildren && <div className="w-6" />}

            {/* Node Icon/Avatar */}
            <div className="flex-shrink-0">
              {node.level === 'member' && node.metadata?.avatar ? (
                <Avatar className="h-8 w-8">
                  <AvatarImage src={node.metadata.avatar} alt={node.label} />
                  <AvatarFallback className="text-xs bg-gradient-deep text-white">
                    {node.metadata?.name ? getMemberInitials(node.metadata.name) : <User className="h-4 w-4" />}
                  </AvatarFallback>
                </Avatar>
              ) : (
                <div className={cn(
                  'flex h-8 w-8 items-center justify-center rounded-lg shadow-sm',
                  node.status === 'over_budget'
                    ? 'bg-gradient-to-br from-red-500 to-red-600'
                    : 'bg-gradient-deep'
                )}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
              )}
            </div>

            {/* Node Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-foreground truncate">{node.label}</h4>
                {getStatusIcon(node.status)}
                <Badge variant="outline" className="text-xs">
                  {node.budgets.length} orçamento{node.budgets.length !== 1 ? 's' : ''}
                </Badge>
              </div>
              
              {/* Progress Bar */}
              <div className="flex items-center gap-2 mb-1">
                <Progress 
                  value={Math.min(node.progress, 100)} 
                  className="flex-1 h-2"
                />
                <span className="text-xs text-muted-foreground min-w-[3rem]">
                  {node.progress.toFixed(1)}%
                </span>
              </div>

              {/* Amount Info */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>
                  R$ {node.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} 
                  {' de '}
                  R$ {node.totalPlanned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </span>
                <span className={cn(
                  'font-medium',
                  node.totalSpent > node.totalPlanned ? 'text-red-600' : 'text-green-600'
                )}>
                  {node.totalSpent > node.totalPlanned ? '+' : ''}
                  R$ {Math.abs(node.totalPlanned - node.totalSpent).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </span>
              </div>
            </div>

            {/* Actions for leaf nodes */}
            {!hasChildren && node.budgets.length === 1 && (
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(node.budgets[0].budget || node.budgets[0] as any)}
                  className="h-8 w-8 p-0"
                >
                  <TrendingUp className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(node.budgets[0].id)}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                >
                  <TrendingDown className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>

          {/* Children */}
          {hasChildren && (
            <CollapsibleContent className="space-y-2">
              {node.children.map(child => renderTreeNode(child, depth + 1))}
            </CollapsibleContent>
          )}
        </Collapsible>
      </div>
    )
  }

  if (treeData.length === 0) {
    return (
      <div className="text-center py-12">
        <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-lg font-medium text-muted-foreground">
          Nenhum orçamento encontrado
        </p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {treeData.map(node => renderTreeNode(node))}
    </div>
  )
}

// Helper function to build tree structure
function buildTreeStructure(budgets: GroupedBudget[], config: TreeHierarchyConfig): TreeNode[] {
  const { level1, level2, level3 } = config

  // Group by first level
  const level1Groups = groupBudgetsByLevel(budgets, level1)

  return level1Groups.map(group => {
    const level2Groups = groupBudgetsByLevel(group.budgets, level2)

    const children = level2Groups.map(subGroup => {
      const level3Groups = groupBudgetsByLevel(subGroup.budgets, level3)

      const grandChildren = level3Groups.map(subSubGroup => ({
        id: `${group.id}-${subGroup.id}-${subSubGroup.id}`,
        label: subSubGroup.label,
        level: level3,
        budgets: subSubGroup.budgets,
        children: [],
        totalPlanned: subSubGroup.totalPlanned,
        totalSpent: subSubGroup.totalSpent,
        progress: subSubGroup.progress,
        status: subSubGroup.status,
        metadata: subSubGroup.metadata
      }))

      return {
        id: `${group.id}-${subGroup.id}`,
        label: subGroup.label,
        level: level2,
        budgets: subGroup.budgets,
        children: grandChildren,
        totalPlanned: subGroup.totalPlanned,
        totalSpent: subGroup.totalSpent,
        progress: subGroup.progress,
        status: subGroup.status,
        metadata: subGroup.metadata
      }
    })

    return {
      id: group.id,
      label: group.label,
      level: level1,
      budgets: group.budgets,
      children,
      totalPlanned: group.totalPlanned,
      totalSpent: group.totalSpent,
      progress: group.progress,
      status: group.status,
      metadata: group.metadata
    }
  })
}

function groupBudgetsByLevel(budgets: GroupedBudget[], level: TreeHierarchyLevel) {
  const groups = new Map<string, {
    id: string
    label: string
    budgets: GroupedBudget[]
    totalPlanned: number
    totalSpent: number
    progress: number
    status: 'under_budget' | 'on_track' | 'over_budget'
    metadata?: any
  }>()

  budgets.forEach(budget => {
    let groupKey: string
    let groupLabel: string
    let metadata: any = {}

    switch (level) {
      case 'month':
        groupKey = `${budget.year}-${budget.month.toString().padStart(2, '0')}`
        groupLabel = `${MONTH_NAMES[budget.month - 1]} ${budget.year}`
        metadata = { year: budget.year, month: budget.month }
        break
      case 'member':
        groupKey = budget.familyMember?.id || 'no-member'
        groupLabel = budget.familyMember?.name || 'Sem Membro Específico'
        metadata = {
          name: budget.familyMember?.name,
          avatar: budget.familyMember?.avatar
        }
        break
      case 'category':
        groupKey = budget.category.parent?.id || budget.category.id
        groupLabel = budget.category.parent?.name || budget.category.name
        metadata = {
          color: budget.category.parent?.color || budget.category.color
        }
        break
      case 'status':
        groupKey = budget.status
        groupLabel = getStatusLabel(budget.status)
        break
      default:
        groupKey = 'unknown'
        groupLabel = 'Desconhecido'
    }

    if (!groups.has(groupKey)) {
      groups.set(groupKey, {
        id: groupKey,
        label: groupLabel,
        budgets: [],
        totalPlanned: 0,
        totalSpent: 0,
        progress: 0,
        status: 'under_budget',
        metadata
      })
    }

    const group = groups.get(groupKey)!
    group.budgets.push(budget)
    group.totalPlanned += budget.plannedAmount
    group.totalSpent += budget.spentAmount
  })

  // Calculate progress and status for each group
  groups.forEach(group => {
    group.progress = group.totalPlanned > 0 ? (group.totalSpent / group.totalPlanned) * 100 : 0

    if (group.progress > 100) {
      group.status = 'over_budget'
    } else if (group.progress > 80) {
      group.status = 'on_track'
    } else {
      group.status = 'under_budget'
    }
  })

  return Array.from(groups.values()).sort((a, b) => {
    // Custom sorting logic based on level
    if (level === 'month') {
      const [yearA, monthA] = a.id.split('-').map(Number)
      const [yearB, monthB] = b.id.split('-').map(Number)
      if (yearA !== yearB) return yearB - yearA // Most recent year first
      return monthB - monthA // Most recent month first
    }
    return a.label.localeCompare(b.label)
  })
}

function getStatusLabel(status: string): string {
  switch (status) {
    case 'over_budget':
      return 'Estourado'
    case 'on_track':
      return 'No Limite'
    case 'under_budget':
      return 'Abaixo do Orçamento'
    default:
      return 'Desconhecido'
  }
}
