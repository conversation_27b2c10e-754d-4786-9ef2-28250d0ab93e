import { useState } from 'react'
import { Settings, Calendar, Users, FolderTree, BarChart3, ArrowUpDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'

export type TreeHierarchyLevel = 'month' | 'member' | 'category' | 'status'

export interface TreeHierarchyConfig {
  level1: TreeHierarchyLevel
  level2: TreeHierarchyLevel
  level3: TreeHierarchyLevel
}

interface BudgetsTreeViewConfigProps {
  config: TreeHierarchyConfig
  onConfigChange: (config: TreeHierarchyConfig) => void
  className?: string
}

const hierarchyOptions = [
  {
    value: 'month' as const,
    label: 'Mês',
    icon: Calendar,
    description: 'Agrupar por período mensal'
  },
  {
    value: 'member' as const,
    label: 'Membro da Família',
    icon: Users,
    description: 'Agrupar por membro da família'
  },
  {
    value: 'category' as const,
    label: 'Categoria',
    icon: FolderTree,
    description: 'Agrupar por categoria'
  },
  {
    value: 'status' as const,
    label: 'Status',
    icon: BarChart3,
    description: 'Agrupar por status do orçamento'
  }
]

const presetConfigs = [
  {
    name: 'Temporal',
    description: 'Mês → Membro → Categoria',
    config: { level1: 'month', level2: 'member', level3: 'category' } as TreeHierarchyConfig
  },
  {
    name: 'Por Pessoa',
    description: 'Membro → Mês → Categoria',
    config: { level1: 'member', level2: 'month', level3: 'category' } as TreeHierarchyConfig
  },
  {
    name: 'Por Categoria',
    description: 'Categoria → Mês → Membro',
    config: { level1: 'category', level2: 'month', level3: 'member' } as TreeHierarchyConfig
  },
  {
    name: 'Por Status',
    description: 'Status → Categoria → Membro',
    config: { level1: 'status', level2: 'category', level3: 'member' } as TreeHierarchyConfig
  }
]

export function BudgetsTreeViewConfig({
  config,
  onConfigChange,
  className
}: BudgetsTreeViewConfigProps) {
  const [isOpen, setIsOpen] = useState(false)

  const getOptionByValue = (value: TreeHierarchyLevel) => {
    return hierarchyOptions.find(option => option.value === value)
  }

  const getAvailableOptions = (currentLevel: keyof TreeHierarchyConfig) => {
    const usedValues = Object.entries(config)
      .filter(([key]) => key !== currentLevel)
      .map(([, value]) => value)
    
    return hierarchyOptions.filter(option => !usedValues.includes(option.value))
  }

  const handleLevelChange = (level: keyof TreeHierarchyConfig, value: TreeHierarchyLevel) => {
    const newConfig = { ...config, [level]: value }
    onConfigChange(newConfig)
  }

  const handlePresetSelect = (presetConfig: TreeHierarchyConfig) => {
    onConfigChange(presetConfig)
    setIsOpen(false)
  }

  const getCurrentConfigName = () => {
    const preset = presetConfigs.find(p => 
      p.config.level1 === config.level1 && 
      p.config.level2 === config.level2 && 
      p.config.level3 === config.level3
    )
    return preset?.name || 'Personalizado'
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn('flex items-center gap-2', className)}
        >
          <Settings className="h-4 w-4" />
          <span className="hidden sm:inline">Hierarquia: {getCurrentConfigName()}</span>
          <span className="sm:hidden">Config</span>
          <ArrowUpDown className="h-3 w-3" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configurar Hierarquia
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Defina como os orçamentos serão organizados na visualização em árvore
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Presets */}
            <div>
              <h4 className="text-sm font-medium mb-3">Configurações Rápidas</h4>
              <div className="grid grid-cols-1 gap-2">
                {presetConfigs.map((preset) => (
                  <Button
                    key={preset.name}
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePresetSelect(preset.config)}
                    className="justify-start h-auto p-3 text-left"
                  >
                    <div>
                      <div className="font-medium">{preset.name}</div>
                      <div className="text-xs text-muted-foreground">{preset.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Configuration */}
            <div>
              <h4 className="text-sm font-medium mb-3">Configuração Personalizada</h4>
              <div className="space-y-3">
                {/* Level 1 */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    1º Nível (Principal)
                  </label>
                  <Select
                    value={config.level1}
                    onValueChange={(value: TreeHierarchyLevel) => handleLevelChange('level1', value)}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableOptions('level1').map((option) => {
                        const Icon = option.icon
                        return (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {option.label}
                            </div>
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                </div>

                {/* Level 2 */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    2º Nível (Secundário)
                  </label>
                  <Select
                    value={config.level2}
                    onValueChange={(value: TreeHierarchyLevel) => handleLevelChange('level2', value)}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableOptions('level2').map((option) => {
                        const Icon = option.icon
                        return (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {option.label}
                            </div>
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                </div>

                {/* Level 3 */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    3º Nível (Detalhes)
                  </label>
                  <Select
                    value={config.level3}
                    onValueChange={(value: TreeHierarchyLevel) => handleLevelChange('level3', value)}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableOptions('level3').map((option) => {
                        const Icon = option.icon
                        return (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {option.label}
                            </div>
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}
