import { useState } from 'react'
import { LayoutGrid, Calendar, Users, FolderTree, GitBranch } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export type BudgetViewType = 'status' | 'category' | 'month' | 'member' | 'tree'

interface BudgetsViewSelectorProps {
  currentView: BudgetViewType
  onViewChange: (view: BudgetViewType) => void
  className?: string
}

const viewOptions = [
  {
    value: 'status' as const,
    label: 'Por Status',
    icon: LayoutGrid,
    description: 'Agrupar por status do orçamento'
  },
  {
    value: 'category' as const,
    label: 'Por Categoria',
    icon: FolderTree,
    description: 'Agrupar por categorias pai/filha'
  },
  {
    value: 'month' as const,
    label: 'Por Mês',
    icon: Calendar,
    description: 'Timeline mensal com accordion'
  },
  {
    value: 'member' as const,
    label: 'Por Membro',
    icon: Users,
    description: 'Agrupar por membro da família'
  },
  {
    value: 'tree' as const,
    label: 'Árvore',
    icon: GitBranch,
    description: 'Visualização hierárquica configurável'
  }
]

export function BudgetsViewSelector({
  currentView,
  onViewChange,
  className
}: BudgetsViewSelectorProps) {
  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {viewOptions.map((option) => {
        const Icon = option.icon
        const isActive = currentView === option.value
        
        return (
          <Button
            key={option.value}
            variant={isActive ? 'default' : 'outline'}
            size="sm"
            onClick={() => onViewChange(option.value)}
            className={cn(
              'flex items-center gap-2 transition-all duration-200',
              isActive && 'bg-gradient-deep text-white shadow-glow'
            )}
            title={option.description}
          >
            <Icon className="h-4 w-4" />
            <span className="hidden sm:inline">{option.label}</span>
          </Button>
        )
      })}
    </div>
  )
}
