import { useState, useMemo } from 'react'
import { FolderOpen, ChevronDown, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { BudgetCard } from './BudgetCard'
import { cn } from '@/lib/utils'
import type { Budget, GroupedBudget } from '@/types/budget.types'

interface BudgetsCategoryViewProps {
  budgets: GroupedBudget[]
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  className?: string
}

interface CategoryGroup {
  categoryId: string
  categoryName: string
  categoryColor?: string
  budgets: GroupedBudget[]
  totalPlanned: number
  totalSpent: number
  totalRemaining: number
  overallProgress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
}

export function BudgetsCategoryView({
  budgets,
  onEdit,
  onDelete,
  onViewDetails,
  className
}: BudgetsCategoryViewProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  // Agrupar orçamentos por categoria pai
  const categoryGroups = useMemo(() => {
    const groups = new Map<string, CategoryGroup>()

    budgets.forEach((budget) => {
      // Para orçamentos agrupados, usar a categoria pai
      const categoryId = budget.type === 'grouped' 
        ? budget.category.id 
        : budget.category.parent?.id || budget.category.id
      
      const categoryName = budget.type === 'grouped'
        ? budget.category.name
        : budget.category.parent?.name || budget.category.name

      const categoryColor = budget.type === 'grouped'
        ? budget.category.color
        : budget.category.parent?.color || budget.category.color

      if (!groups.has(categoryId)) {
        groups.set(categoryId, {
          categoryId,
          categoryName,
          categoryColor,
          budgets: [],
          totalPlanned: 0,
          totalSpent: 0,
          totalRemaining: 0,
          overallProgress: 0,
          status: 'under_budget'
        })
      }

      const group = groups.get(categoryId)!
      group.budgets.push(budget)
      group.totalPlanned += budget.plannedAmount
      group.totalSpent += budget.spentAmount
      group.totalRemaining += budget.remainingAmount
    })

    // Calcular progresso e status para cada grupo
    groups.forEach((group) => {
      group.overallProgress = group.totalPlanned > 0 
        ? (group.totalSpent / group.totalPlanned) * 100 
        : 0

      if (group.overallProgress > 100) {
        group.status = 'over_budget'
      } else if (group.overallProgress > 80) {
        group.status = 'on_track'
      } else {
        group.status = 'under_budget'
      }
    })

    return Array.from(groups.values()).sort((a, b) => a.categoryName.localeCompare(b.categoryName))
  }, [budgets])

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const getStatusColor = (status: CategoryGroup['status']) => {
    switch (status) {
      case 'over_budget':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'on_track':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'under_budget':
        return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  if (categoryGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-lg font-medium text-muted-foreground">
          Nenhum orçamento encontrado
        </p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {categoryGroups.map((group) => {
        const isExpanded = expandedCategories.has(group.categoryId)
        
        return (
          <Collapsible
            key={group.categoryId}
            open={isExpanded}
            onOpenChange={() => toggleCategory(group.categoryId)}
          >
            <div className={cn(
              'glass-deep rounded-xl border-2 transition-all duration-300',
              getStatusColor(group.status)
            )}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full p-6 h-auto justify-between hover:bg-transparent"
                >
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      'flex h-12 w-12 items-center justify-center rounded-xl shadow-soft',
                      group.status === 'over_budget'
                        ? 'bg-gradient-to-br from-red-500 to-red-600'
                        : 'bg-gradient-deep'
                    )}>
                      <FolderOpen className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="text-xl font-bold">{group.categoryName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {group.budgets.length} orçamento{group.budgets.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-lg font-bold">
                        R$ {group.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        de R$ {group.totalPlanned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </div>
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="px-6 pb-6">
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  {group.budgets.map((budget) => (
                    <BudgetCard
                      key={budget.id}
                      budget={budget}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onViewDetails={onViewDetails}
                      className="w-full"
                    />
                  ))}
                </div>
              </CollapsibleContent>
            </div>
          </Collapsible>
        )
      })}
    </div>
  )
}
