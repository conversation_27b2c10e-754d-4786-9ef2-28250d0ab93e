import { useState, useMemo } from 'react'
import { Calendar, ChevronDown, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { BudgetCard } from './BudgetCard'
import { cn } from '@/lib/utils'
import type { Budget, GroupedBudget } from '@/types/budget.types'

interface BudgetsMonthViewProps {
  budgets: GroupedBudget[]
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  className?: string
}

interface MonthGroup {
  monthKey: string
  monthName: string
  year: number
  month: number
  budgets: GroupedBudget[]
  totalPlanned: number
  totalSpent: number
  totalRemaining: number
  overallProgress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
}

const MONTH_NAMES = [
  'Janeiro', 'Fevereiro', 'Março', '<PERSON>bri<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  'Jul<PERSON>', '<PERSON>gosto', 'Set<PERSON>bro', 'Outubro', 'Novembro', 'Dezembro'
]

export function BudgetsMonthView({
  budgets,
  onEdit,
  onDelete,
  onViewDetails,
  className
}: BudgetsMonthViewProps) {
  const [expandedMonths, setExpandedMonths] = useState<Set<string>>(new Set())

  // Agrupar orçamentos por mês/ano
  const monthGroups = useMemo(() => {
    const groups = new Map<string, MonthGroup>()

    budgets.forEach((budget) => {
      const monthKey = `${budget.year}-${budget.month.toString().padStart(2, '0')}`
      
      if (!groups.has(monthKey)) {
        groups.set(monthKey, {
          monthKey,
          monthName: MONTH_NAMES[budget.month - 1],
          year: budget.year,
          month: budget.month,
          budgets: [],
          totalPlanned: 0,
          totalSpent: 0,
          totalRemaining: 0,
          overallProgress: 0,
          status: 'under_budget'
        })
      }

      const group = groups.get(monthKey)!
      group.budgets.push(budget)
      group.totalPlanned += budget.plannedAmount
      group.totalSpent += budget.spentAmount
      group.totalRemaining += budget.remainingAmount
    })

    // Calcular progresso e status para cada grupo
    groups.forEach((group) => {
      group.overallProgress = group.totalPlanned > 0 
        ? (group.totalSpent / group.totalPlanned) * 100 
        : 0

      if (group.overallProgress > 100) {
        group.status = 'over_budget'
      } else if (group.overallProgress > 80) {
        group.status = 'on_track'
      } else {
        group.status = 'under_budget'
      }
    })

    // Ordenar por ano e mês (mais recente primeiro)
    return Array.from(groups.values()).sort((a, b) => {
      if (a.year !== b.year) return b.year - a.year
      return b.month - a.month
    })
  }, [budgets])

  const toggleMonth = (monthKey: string) => {
    const newExpanded = new Set(expandedMonths)
    if (newExpanded.has(monthKey)) {
      newExpanded.delete(monthKey)
    } else {
      newExpanded.add(monthKey)
    }
    setExpandedMonths(newExpanded)
  }

  const getStatusColor = (status: MonthGroup['status']) => {
    switch (status) {
      case 'over_budget':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'on_track':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'under_budget':
        return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  const isCurrentMonth = (year: number, month: number) => {
    const now = new Date()
    return now.getFullYear() === year && now.getMonth() + 1 === month
  }

  if (monthGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-lg font-medium text-muted-foreground">
          Nenhum orçamento encontrado
        </p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {monthGroups.map((group) => {
        const isExpanded = expandedMonths.has(group.monthKey)
        const isCurrent = isCurrentMonth(group.year, group.month)
        
        return (
          <Collapsible
            key={group.monthKey}
            open={isExpanded}
            onOpenChange={() => toggleMonth(group.monthKey)}
          >
            <div className={cn(
              'glass-deep rounded-xl border-2 transition-all duration-300',
              getStatusColor(group.status),
              isCurrent && 'ring-2 ring-blue-500 ring-opacity-50'
            )}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full p-6 h-auto justify-between hover:bg-transparent"
                >
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      'flex h-12 w-12 items-center justify-center rounded-xl shadow-soft',
                      group.status === 'over_budget'
                        ? 'bg-gradient-to-br from-red-500 to-red-600'
                        : isCurrent
                        ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                        : 'bg-gradient-deep'
                    )}>
                      <Calendar className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="text-xl font-bold flex items-center gap-2">
                        {group.monthName} {group.year}
                        {isCurrent && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            Atual
                          </span>
                        )}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {group.budgets.length} orçamento{group.budgets.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-lg font-bold">
                        R$ {group.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        de R$ {group.totalPlanned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        {group.overallProgress.toFixed(1)}% usado
                      </div>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </div>
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="px-6 pb-6">
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  {group.budgets.map((budget) => (
                    <BudgetCard
                      key={budget.id}
                      budget={budget}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onViewDetails={onViewDetails}
                      className="w-full"
                    />
                  ))}
                </div>
              </CollapsibleContent>
            </div>
          </Collapsible>
        )
      })}
    </div>
  )
}
