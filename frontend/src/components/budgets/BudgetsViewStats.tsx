import { useMemo } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Target } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { GroupedBudget } from '@/types/budget.types'
import type { BudgetViewType } from './BudgetsViewSelector'

interface BudgetsViewStatsProps {
  budgets: GroupedBudget[]
  currentView: BudgetViewType
  className?: string
}

export function BudgetsViewStats({
  budgets,
  currentView,
  className
}: BudgetsViewStatsProps) {
  const stats = useMemo(() => {
    const totalPlanned = budgets.reduce((sum, budget) => sum + budget.plannedAmount, 0)
    const totalSpent = budgets.reduce((sum, budget) => sum + budget.spentAmount, 0)
    const totalRemaining = totalPlanned - totalSpent
    const overallProgress = totalPlanned > 0 ? (totalSpent / totalPlanned) * 100 : 0

    const overBudgetCount = budgets.filter(b => b.status === 'over_budget').length
    const onTrackCount = budgets.filter(b => b.status === 'on_track').length
    const underBudgetCount = budgets.filter(b => b.status === 'under_budget').length

    return {
      totalPlanned,
      totalSpent,
      totalRemaining,
      overallProgress,
      overBudgetCount,
      onTrackCount,
      underBudgetCount,
      totalBudgets: budgets.length
    }
  }, [budgets])

  const getViewTitle = () => {
    switch (currentView) {
      case 'status':
        return 'Resumo por Status'
      case 'category':
        return 'Resumo por Categoria'
      case 'month':
        return 'Resumo por Mês'
      case 'member':
        return 'Resumo por Membro'
      default:
        return 'Resumo Geral'
    }
  }

  const getProgressColor = () => {
    if (stats.overallProgress > 100) return 'text-red-600'
    if (stats.overallProgress > 80) return 'text-yellow-600'
    return 'text-green-600'
  }

  if (budgets.length === 0) {
    return null
  }

  return (
    <div className={cn('space-y-4', className)}>
      <h3 className="text-lg font-semibold text-foreground">{getViewTitle()}</h3>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total Planejado */}
        <Card className="glass-deep shadow-elegant">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-soft">
                <Target className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Planejado</p>
                <p className="text-lg font-bold text-foreground">
                  R$ {stats.totalPlanned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Gasto */}
        <Card className="glass-deep shadow-elegant">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-soft">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Gasto</p>
                <p className="text-lg font-bold text-foreground">
                  R$ {stats.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Restante */}
        <Card className="glass-deep shadow-elegant">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className={cn(
                'flex h-10 w-10 items-center justify-center rounded-xl shadow-soft',
                stats.totalRemaining >= 0
                  ? 'bg-gradient-to-br from-green-500 to-green-600'
                  : 'bg-gradient-to-br from-red-500 to-red-600'
              )}>
                {stats.totalRemaining >= 0 ? (
                  <TrendingUp className="h-5 w-5 text-white" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-white" />
                )}
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  {stats.totalRemaining >= 0 ? 'Restante' : 'Excedido'}
                </p>
                <p className={cn(
                  'text-lg font-bold',
                  stats.totalRemaining >= 0 ? 'text-green-600' : 'text-red-600'
                )}>
                  R$ {Math.abs(stats.totalRemaining).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Progresso Geral */}
        <Card className="glass-deep shadow-elegant">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
                <span className={cn('text-sm font-bold text-white', getProgressColor())}>
                  {stats.overallProgress.toFixed(0)}%
                </span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Progresso Geral</p>
                <div className="flex items-center gap-2">
                  <p className={cn('text-lg font-bold', getProgressColor())}>
                    {stats.overallProgress.toFixed(1)}%
                  </p>
                  <span className="text-xs text-muted-foreground">
                    ({stats.totalBudgets} orçamento{stats.totalBudgets !== 1 ? 's' : ''})
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Distribution */}
      {currentView === 'status' && (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card className="glass-deep shadow-elegant border-red-200">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600 mb-1">
                {stats.overBudgetCount}
              </div>
              <p className="text-sm text-red-600">Estourados</p>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant border-yellow-200">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600 mb-1">
                {stats.onTrackCount}
              </div>
              <p className="text-sm text-yellow-600">No Limite</p>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant border-green-200">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {stats.underBudgetCount}
              </div>
              <p className="text-sm text-green-600">Abaixo do Orçamento</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
