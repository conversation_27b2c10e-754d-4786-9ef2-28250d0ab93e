import { useState, useMemo } from 'react'
import { Users, User, ChevronDown, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BudgetCard } from './BudgetCard'
import { cn } from '@/lib/utils'
import type { Budget, GroupedBudget } from '@/types/budget.types'

interface BudgetsMemberViewProps {
  budgets: GroupedBudget[]
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  className?: string
}

interface MemberGroup {
  memberId: string | null
  memberName: string
  memberAvatar?: string
  budgets: GroupedBudget[]
  totalPlanned: number
  totalSpent: number
  totalRemaining: number
  overallProgress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
}

export function BudgetsMemberView({
  budgets,
  onEdit,
  onDelete,
  onViewDetails,
  className
}: BudgetsMemberViewProps) {
  const [expandedMembers, setExpandedMembers] = useState<Set<string>>(new Set())

  // Agrupar orçamentos por membro da família
  const memberGroups = useMemo(() => {
    const groups = new Map<string, MemberGroup>()

    budgets.forEach((budget) => {
      const memberId = budget.familyMember?.id || 'no-member'
      const memberName = budget.familyMember?.name || 'Sem Membro Específico'
      const memberAvatar = budget.familyMember?.avatar
      
      if (!groups.has(memberId)) {
        groups.set(memberId, {
          memberId: memberId === 'no-member' ? null : memberId,
          memberName,
          memberAvatar,
          budgets: [],
          totalPlanned: 0,
          totalSpent: 0,
          totalRemaining: 0,
          overallProgress: 0,
          status: 'under_budget'
        })
      }

      const group = groups.get(memberId)!
      group.budgets.push(budget)
      group.totalPlanned += budget.plannedAmount
      group.totalSpent += budget.spentAmount
      group.totalRemaining += budget.remainingAmount
    })

    // Calcular progresso e status para cada grupo
    groups.forEach((group) => {
      group.overallProgress = group.totalPlanned > 0 
        ? (group.totalSpent / group.totalPlanned) * 100 
        : 0

      if (group.overallProgress > 100) {
        group.status = 'over_budget'
      } else if (group.overallProgress > 80) {
        group.status = 'on_track'
      } else {
        group.status = 'under_budget'
      }
    })

    // Ordenar: membros específicos primeiro, depois "sem membro"
    return Array.from(groups.values()).sort((a, b) => {
      if (a.memberId === null && b.memberId !== null) return 1
      if (a.memberId !== null && b.memberId === null) return -1
      return a.memberName.localeCompare(b.memberName)
    })
  }, [budgets])

  const toggleMember = (memberId: string) => {
    const newExpanded = new Set(expandedMembers)
    if (newExpanded.has(memberId)) {
      newExpanded.delete(memberId)
    } else {
      newExpanded.add(memberId)
    }
    setExpandedMembers(newExpanded)
  }

  const getStatusColor = (status: MemberGroup['status']) => {
    switch (status) {
      case 'over_budget':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'on_track':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'under_budget':
        return 'text-green-600 bg-green-50 border-green-200'
    }
  }

  const getMemberInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (memberGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-lg font-medium text-muted-foreground">
          Nenhum orçamento encontrado
        </p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {memberGroups.map((group) => {
        const memberKey = group.memberId || 'no-member'
        const isExpanded = expandedMembers.has(memberKey)
        
        return (
          <Collapsible
            key={memberKey}
            open={isExpanded}
            onOpenChange={() => toggleMember(memberKey)}
          >
            <div className={cn(
              'glass-deep rounded-xl border-2 transition-all duration-300',
              getStatusColor(group.status)
            )}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full p-6 h-auto justify-between hover:bg-transparent"
                >
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12 shadow-soft">
                      <AvatarImage src={group.memberAvatar} alt={group.memberName} />
                      <AvatarFallback className={cn(
                        'text-white font-bold',
                        group.status === 'over_budget'
                          ? 'bg-gradient-to-br from-red-500 to-red-600'
                          : 'bg-gradient-deep'
                      )}>
                        {group.memberId ? getMemberInitials(group.memberName) : <User className="h-6 w-6" />}
                      </AvatarFallback>
                    </Avatar>
                    <div className="text-left">
                      <h3 className="text-xl font-bold">{group.memberName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {group.budgets.length} orçamento{group.budgets.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-lg font-bold">
                        R$ {group.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        de R$ {group.totalPlanned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        {group.overallProgress.toFixed(1)}% usado
                      </div>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </div>
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="px-6 pb-6">
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  {group.budgets.map((budget) => (
                    <BudgetCard
                      key={budget.id}
                      budget={budget}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onViewDetails={onViewDetails}
                      className="w-full"
                    />
                  ))}
                </div>
              </CollapsibleContent>
            </div>
          </Collapsible>
        )
      })}
    </div>
  )
}
