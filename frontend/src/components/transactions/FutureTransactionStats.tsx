import { Calendar, Clock, AlertTriangle, CheckCircle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface FutureTransactionStatsProps {
  stats?: {
    totalPending: number
    dueToday: number
    dueThisWeek: number
    dueThisMonth: number
    overdue: number
  }
}

export function FutureTransactionStats({ stats }: FutureTransactionStatsProps) {
  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Estatísticas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="mb-2 h-4 rounded bg-muted" />
                <div className="h-8 rounded bg-muted" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const statItems = [
    {
      label: 'Total Pendente',
      value: stats.totalPending,
      icon: Calendar,
      color: 'bg-primary/10',
      iconColor: 'text-primary',
      textColor: 'text-primary',
    },
    {
      label: 'Vence Hoje',
      value: stats.dueToday,
      icon: Clock,
      color: 'bg-yellow-500/10',
      iconColor: 'text-yellow-600',
      textColor: 'text-yellow-600',
    },
    {
      label: 'Esta Semana',
      value: stats.dueThisWeek,
      icon: CheckCircle,
      color: 'bg-green-500/10',
      iconColor: 'text-green-600',
      textColor: 'text-green-600',
    },
    {
      label: 'Em Atraso',
      value: stats.overdue,
      icon: AlertTriangle,
      color: 'bg-red-500/10',
      iconColor: 'text-red-600',
      textColor: 'text-red-600',
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Estatísticas</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {statItems.map((item) => (
            <div key={item.label} className="flex items-center space-x-3">
              <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${item.color}`}>
                <item.icon className={`h-5 w-5 ${item.iconColor}`} />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {item.label}
                </p>
                <p className={`text-2xl font-bold ${item.textColor}`}>
                  {item.value}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
