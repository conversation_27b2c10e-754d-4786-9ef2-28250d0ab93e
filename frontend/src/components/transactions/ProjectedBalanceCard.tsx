import { TrendingUp, TrendingDown, DollarSign, Calendar } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface ProjectedBalanceCardProps {
  projectedBalance?: {
    currentBalance: number
    currentBalanceInBRL: number
    projectedBalance: number
    projectedBalanceInBRL: number
    projectionDate: string
    includeRecurring: boolean
    balanceByType: {
      CHECKING: number
      SAVINGS: number
      INVESTMENT: number
      CREDIT_CARD: number
    }
    balanceByCurrency: Record<string, number>
  }
}

export function ProjectedBalanceCard({
  projectedBalance,
}: ProjectedBalanceCardProps) {
  if (!projectedBalance) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Saldo Projetado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-8 rounded bg-muted" />
            <div className="h-4 w-3/4 rounded bg-muted" />
            <div className="h-4 w-1/2 rounded bg-muted" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const balanceChange =
    projectedBalance.projectedBalanceInBRL -
    projectedBalance.currentBalanceInBRL
  const isPositiveChange = balanceChange >= 0
  const changePercentage =
    projectedBalance.currentBalanceInBRL !== 0
      ? (balanceChange / Math.abs(projectedBalance.currentBalanceInBRL)) * 100
      : 0

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Saldo Projetado
          </CardTitle>
          <Badge variant="outline" className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {projectedBalance.projectionDate !== 'indefinite'
              ? formatDate(projectedBalance.projectionDate)
              : 'Indefinido'}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Current Balance */}
        <div>
          <p className="mb-1 text-sm text-muted-foreground">Saldo Atual</p>
          <p className="text-xl font-semibold">
            {formatCurrency(projectedBalance.currentBalanceInBRL)}
          </p>
        </div>

        {/* Projected Balance */}
        <div>
          <p className="mb-1 text-sm text-muted-foreground">Saldo Projetado</p>
          <p className="text-2xl font-bold">
            {formatCurrency(projectedBalance.projectedBalanceInBRL)}
          </p>
        </div>

        {/* Balance Change */}
        <div className="flex items-center justify-between rounded-lg bg-muted p-3">
          <div className="flex items-center gap-2">
            {isPositiveChange ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm text-muted-foreground">Variação</span>
          </div>
          <div className="text-right">
            <p
              className={`font-medium ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`}
            >
              {isPositiveChange ? '+' : ''}
              {formatCurrency(balanceChange)}
            </p>
            <p
              className={`text-xs ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`}
            >
              {isPositiveChange ? '+' : ''}
              {changePercentage.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Balance by Account Type */}
        {projectedBalance.balanceByType && (
          <div>
            <p className="mb-2 text-sm text-muted-foreground">
              Saldo por Tipo de Conta
            </p>
            <div className="space-y-2">
              {Object.entries(projectedBalance.balanceByType).map(
                ([type, balance]) => {
                const typeLabels: Record<string, string> = {
                  CHECKING: 'Conta Corrente',
                  SAVINGS: 'Poupança',
                  INVESTMENT: 'Investimentos',
                  CREDIT_CARD: 'Cartão de Crédito',
                }

                const typeColors: Record<string, string> = {
                  CHECKING: 'text-primary',
                  SAVINGS: 'text-green-600',
                  INVESTMENT: 'text-yellow-600',
                  CREDIT_CARD: 'text-red-600',
                }

                if (balance === 0) {
                  return null
                }

                return (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {typeLabels[type] || type}
                    </span>
                    <span
                      className={`text-sm font-medium ${typeColors[type] || 'text-foreground'}`}
                    >
                      {formatCurrency(balance)}
                    </span>
                  </div>
                )
                }
              )}
            </div>
          </div>
        )}

        {/* Multi-currency balances */}
        {projectedBalance.balanceByCurrency && Object.keys(projectedBalance.balanceByCurrency).length > 1 && (
          <div>
            <p className="mb-2 text-sm text-muted-foreground">Saldo por Moeda</p>
            <div className="space-y-2">
              {Object.entries(projectedBalance.balanceByCurrency).map(
                ([currency, balance]) => {
                  if (balance === 0) {
                    return null
                  }

                  return (
                    <div
                      key={currency}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm text-muted-foreground">
                        {currency}
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(balance, currency)}
                      </span>
                    </div>
                  )
                }
              )}
            </div>
          </div>
        )}

        {/* Info about recurring transactions */}
        {projectedBalance.includeRecurring && (
          <Badge variant="secondary" className="w-full justify-center">
            * Inclui transações recorrentes na projeção
          </Badge>
        )}
      </CardContent>
    </Card>
  )
}
