import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { Calendar, DollarSign, Clock, CheckCircle, Edit3, Save, X, AlertTriangle, Info } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useUpdateInstallmentStatus } from '@/hooks/useTransactions'
import { toast } from 'react-hot-toast'

interface Installment {
  id: string
  description: string
  amount: number
  transactionDate: string
  installmentNumber?: number
  isFuture: boolean
}

interface InstallmentsListProps {
  installments: Installment[]
  currentTransactionId: string
  totalAmount: number
  onInstallmentChange?: (installments: Installment[], totalInstallments: number) => void
  onSave?: () => void
}

export const InstallmentsList: React.FC<InstallmentsListProps> = ({
  installments,
  currentTransactionId,
  totalAmount,
  onInstallmentChange,
  onSave
}) => {
  const [editableInstallments, setEditableInstallments] = useState<Installment[]>(installments)
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [newTotalInstallments, setNewTotalInstallments] = useState<number>(installments.length)
  const [isEditingTotal, setIsEditingTotal] = useState<boolean>(false)
  const [hasChanges, setHasChanges] = useState<boolean>(false)


  const updateInstallmentsMutation = useUpdateInstallmentStatus()

  useEffect(() => {
    setEditableInstallments(installments)
    setNewTotalInstallments(installments.length)
    setHasChanges(false)
  }, [installments])
  // Calculate current sum and validation
  const currentSum = editableInstallments.reduce((sum, inst) => sum + inst.amount, 0)
  const difference = currentSum - totalAmount
  const isValidSum = Math.abs(difference) < 0.01

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const formatDateForInput = (dateString: string) => {
    return new Date(dateString).toISOString().split('T')[0]
  }

  // Calculate average interval between installments
  const calculateAverageInterval = (installments: Installment[]): number => {
    if (installments.length < 2) return 30 // Default to monthly

    const intervals = []
    for (let i = 1; i < installments.length; i++) {
      const prevDate = new Date(installments[i - 1].transactionDate)
      const currDate = new Date(installments[i].transactionDate)
      const diffTime = currDate.getTime() - prevDate.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      intervals.push(diffDays)
    }

    return Math.round(intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length)
  }

  // Recalculate subsequent installments when user edits one
  const recalculateSubsequentInstallments = (editedIndex: number, newAmount: number) => {
    const updatedInstallments = [...editableInstallments]
    updatedInstallments[editedIndex].amount = newAmount

    // Calculate remaining amount for subsequent installments
    const sumUpToEdited = updatedInstallments.slice(0, editedIndex + 1).reduce((sum, inst) => sum + inst.amount, 0)
    const remainingAmount = totalAmount - sumUpToEdited
    const remainingInstallments = updatedInstallments.length - editedIndex - 1

    if (remainingInstallments > 0) {
      const amountPerRemaining = remainingAmount / remainingInstallments

      // Update subsequent installments
      for (let i = editedIndex + 1; i < updatedInstallments.length; i++) {
        updatedInstallments[i].amount = Number(amountPerRemaining.toFixed(2))
      }
    }

    setEditableInstallments(updatedInstallments)
    setHasChanges(true)

    if (onInstallmentChange) {
      onInstallmentChange(updatedInstallments, updatedInstallments.length)
    }
  }

  const getStatusBadge = (installment: Installment, index: number) => {
    const isEditing = editingIndex === index
    const isPast = new Date(installment.transactionDate) < new Date()
    const isFuture = installment.isFuture

    if (isEditing) {
      return <Badge variant="default" className="bg-blue-500">Editando</Badge>
    }

    if (isPast && !isFuture) {
      return <Badge variant="default" className="bg-green-500">Paga</Badge>
    }

    return <Badge variant="outline">Futura</Badge>
  }

  const handleEdit = (index: number) => {
    setEditingIndex(index)
  }

  const handleSave = () => {
    setEditingIndex(null)
  }

  const handleCancel = () => {
    // Restore original values
    setEditableInstallments(installments)
    setEditingIndex(null)
    setHasChanges(false)
  }

  const handleAmountChange = (index: number, value: string) => {
    const newAmount = parseFloat(value) || 0
    recalculateSubsequentInstallments(index, newAmount)
  }

  const handleDateChange = (index: number, value: string) => {
    const updatedInstallments = [...editableInstallments]
    updatedInstallments[index].transactionDate = value
    setEditableInstallments(updatedInstallments)
    setHasChanges(true)

    if (onInstallmentChange) {
      onInstallmentChange(updatedInstallments, updatedInstallments.length)
    }
  }

  const handleTotalInstallmentsChange = (newTotal: number) => {
    if (newTotal < 2 || newTotal > 60) return

    const avgInterval = calculateAverageInterval(editableInstallments)
    const baseDate = new Date(editableInstallments[0]?.transactionDate || new Date())

    const newInstallments: Installment[] = []

    // Calculate amount per installment
    const amountPerInstallment = Number((totalAmount / newTotal).toFixed(2))
    let remainingAmount = totalAmount

    for (let i = 0; i < newTotal; i++) {
      let installmentDate: Date

      if (i < editableInstallments.length) {
        // Keep existing dates for existing installments
        installmentDate = new Date(editableInstallments[i].transactionDate)
      } else {
        // Calculate new dates based on average interval
        installmentDate = new Date(baseDate)
        installmentDate.setDate(installmentDate.getDate() + (avgInterval * i))
      }

      // For the last installment, use remaining amount to avoid rounding issues
      const amount = i === newTotal - 1 ? remainingAmount : amountPerInstallment
      remainingAmount -= amount

      newInstallments.push({
        id: i < editableInstallments.length ? editableInstallments[i].id : `temp-${i}`,
        description: i < editableInstallments.length ?
          editableInstallments[i].description :
          `Parcela ${i + 1}/${newTotal}`,
        amount: Number(amount.toFixed(2)),
        transactionDate: installmentDate.toISOString(),
        installmentNumber: i + 1,
        isFuture: installmentDate > new Date()
      })
    }

    setEditableInstallments(newInstallments)
    setNewTotalInstallments(newTotal)
    setHasChanges(true)

    if (onInstallmentChange) {
      onInstallmentChange(newInstallments, newTotal)
    }
  }

  const handleSaveTotalInstallments = () => {
    // The installments have already been updated by handleTotalInstallmentsChange
    // Just close the editing mode
    setIsEditingTotal(false)
  }

  const handleCancelTotalInstallments = () => {
    setNewTotalInstallments(installments.length)
    setEditableInstallments(installments)
    setIsEditingTotal(false)
  }

  // Save all changes to backend
  const handleSaveChanges = async () => {
    if (!hasChanges || !isValidSum) {
      toast.error('Não há alterações válidas para salvar')
      return
    }

    try {
      const installmentsData = editableInstallments.map(inst => ({
        amount: inst.amount,
        transactionDate: inst.transactionDate,
        description: inst.description || `Parcela ${inst.installmentNumber}/${editableInstallments.length}`
      }))



      await updateInstallmentsMutation.mutateAsync({
        transactionId: currentTransactionId,
        installmentNumber: 1,
        isPaid: true,
        data: {
          installments: installmentsData,
          totalAmount: totalAmount
        }
      } as any)

      setHasChanges(false)
      if (onSave) {
        onSave()
      }
    } catch (error) {
      console.error('Error saving installments:', error)
    }
  }

  // Reset changes
  const handleResetChanges = () => {
    setEditableInstallments(installments)
    setNewTotalInstallments(installments.length)
    setHasChanges(false)
    setEditingIndex(null)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Parcelas da Transação
        </CardTitle>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">
              Total: {formatCurrency(currentSum)}
            </div>
            {!isValidSum && (
              <div className="flex items-center gap-1 text-xs">
                <AlertTriangle className="h-3 w-3 text-amber-500" />
                <span className="text-amber-600">
                  {difference > 0
                    ? `Acima: ${formatCurrency(difference)}`
                    : `Faltam: ${formatCurrency(Math.abs(difference))}`
                  }
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            {isEditingTotal ? (
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="1"
                  max="60"
                  value={newTotalInstallments}
                  onChange={(e) => handleTotalInstallmentsChange(parseInt(e.target.value) || 1)}
                  className="w-16 h-7"
                />
                <span className="text-sm text-muted-foreground">parcela(s)</span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSaveTotalInstallments}
                  className="h-7 w-7 p-0"
                >
                  <Save className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancelTotalInstallments}
                  className="h-7 w-7 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {editableInstallments.length} parcela(s)
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditingTotal(true)}
                  className="h-7 w-7 p-0"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {editableInstallments.map((installment, index) => (
            <div
              key={installment.id}
              className={`p-4 rounded-lg border ${
                editingIndex === index
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                  : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {installment.isFuture ? (
                    <Clock className="h-4 w-4 text-orange-500" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  <span className="font-medium">
                    Parcela {index + 1}/{editableInstallments.length}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  {getStatusBadge(installment, index)}
                  {editingIndex === index ? (
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleSave}
                        className="h-7 w-7 p-0"
                      >
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                        className="h-7 w-7 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(index)}
                      className="h-7 w-7 p-0"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-xs font-medium text-muted-foreground">Data</label>
                  {editingIndex === index ? (
                    <Input
                      type="date"
                      value={formatDateForInput(installment.transactionDate)}
                      onChange={(e) => handleDateChange(index, e.target.value)}
                      className="h-8"
                    />
                  ) : (
                    <div className="flex items-center gap-2 h-8 px-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <Calendar className="h-3 w-3" />
                      <span className="text-sm">{formatDate(installment.transactionDate)}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-1">
                  <label className="text-xs font-medium text-muted-foreground">Valor</label>
                  {editingIndex === index ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={installment.amount}
                      onChange={(e) => handleAmountChange(index, e.target.value)}
                      className="h-8"
                    />
                  ) : (
                    <div className="flex items-center gap-2 h-8 px-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <DollarSign className="h-3 w-3" />
                      <span className="text-sm font-semibold">{formatCurrency(installment.amount)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Action buttons */}
        {hasChanges && (
          <div className="mt-4 flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-blue-700 dark:text-blue-300">
                Você tem alterações não salvas
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleResetChanges}
                disabled={updateInstallmentsMutation.isPending}
              >
                Cancelar
              </Button>
              <Button
                size="sm"
                onClick={handleSaveChanges}
                disabled={!isValidSum || updateInstallmentsMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {updateInstallmentsMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </div>
        )}

        {installments.length > 1 && (
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-sm text-muted-foreground">
              <strong>Dica:</strong> Ao editar uma parcela, o valor das parcelas subsequentes será
              recalculado automaticamente para manter o equilíbrio do parcelamento.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
