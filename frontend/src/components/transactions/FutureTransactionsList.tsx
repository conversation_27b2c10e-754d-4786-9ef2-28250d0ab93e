import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { futureTransactionsApi } from '@/lib/api'
import { formatCurrency, formatDate, formatRelativeTime } from '@/lib/utils'
import {
  Calendar,
  Edit,
  Trash2,
  AlertTriangle,
  Clock,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
} from 'lucide-react'
import toast from 'react-hot-toast'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Transaction {
  id: string
  description: string
  totalAmount: string
  transactionDate: string
  type: 'INCOME' | 'EXPENSE' | 'TRANSFER'
  account: {
    id: string
    name: string
    currency: string
  }
  destinationAccount?: {
    id: string
    name: string
    currency: string
  }
  category?: {
    id: string
    name: string
  }
  isFuture: boolean
  createdAt: string
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

interface FutureTransactionsListProps {
  transactions: Transaction[]
  pagination?: Pagination
  onPageChange: (_page: number) => void
  onRefresh: () => void
  onEdit?: (transaction: Transaction) => void
}

export function FutureTransactionsList({
  transactions,
  pagination,
  onPageChange,
  onRefresh,
  onEdit,
}: FutureTransactionsListProps) {
  const [selectedTransaction, setSelectedTransaction] = useState<string | null>(
    null
  )
  const queryClient = useQueryClient()

  const cancelMutation = useMutation({
    mutationFn: futureTransactionsApi.cancel,
    onSuccess: () => {
      toast.success('Transação futura cancelada com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['future-transactions'] })
      onRefresh()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao cancelar transação')
    },
  })

  const handleCancelTransaction = (id: string) => {
    if (
      // eslint-disable-next-line no-alert
      window.confirm('Tem certeza que deseja cancelar esta transação futura?')
    ) {
      cancelMutation.mutate(id)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'INCOME':
        return <TrendingUp className="h-4 w-4 text-success-400" />
      case 'EXPENSE':
        return <TrendingDown className="h-4 w-4 text-error-400" />
      case 'TRANSFER':
        return <ArrowRightLeft className="h-4 w-4 text-primary-400" />
      default:
        return <Calendar className="h-4 w-4 text-secondary-400" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'INCOME':
        return 'text-success-400'
      case 'EXPENSE':
        return 'text-error-400'
      case 'TRANSFER':
        return 'text-primary-400'
      default:
        return 'text-white'
    }
  }

  const isOverdue = (transactionDate: string) => {
    return new Date(transactionDate) < new Date()
  }

  const isDueToday = (transactionDate: string) => {
    const today = new Date()
    const transDate = new Date(transactionDate)
    return (
      transDate.getDate() === today.getDate() &&
      transDate.getMonth() === today.getMonth() &&
      transDate.getFullYear() === today.getFullYear()
    )
  }

  if (transactions.length === 0) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Calendar className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
          <CardTitle className="mb-2 text-lg">
            Nenhuma transação futura encontrada
          </CardTitle>
          <p className="text-muted-foreground">
            Crie sua primeira transação futura para começar a planejar suas
            finanças.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Transactions List */}
      <Card>
        <CardHeader>
          <CardTitle>Transações Futuras</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {transactions.map((transaction) => (
            <Card
              key={transaction.id}
              className={`transition-colors ${
                selectedTransaction === transaction.id
                  ? 'border-primary bg-primary/5'
                  : 'hover:bg-muted/50'
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Transaction Icon */}
                    <div className="flex-shrink-0">
                      {getTransactionIcon(transaction.type)}
                    </div>

                    {/* Transaction Details */}
                    <div className="min-w-0 flex-1">
                      <div className="mb-1 flex items-center gap-2">
                        <h4 className="truncate text-sm font-medium">
                          {transaction.description}
                        </h4>

                        {/* Status Badges */}
                        {isOverdue(transaction.transactionDate) && (
                          <Badge variant="destructive" className="text-xs">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Em atraso
                          </Badge>
                        )}

                        {isDueToday(transaction.transactionDate) && (
                          <Badge variant="secondary" className="text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            Vence hoje
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{transaction.account.name}</span>
                        {transaction.destinationAccount && (
                          <>
                            <ArrowRightLeft className="h-3 w-3" />
                            <span>{transaction.destinationAccount.name}</span>
                          </>
                        )}
                        {transaction.category && (
                          <Badge variant="outline" className="text-xs">
                            {transaction.category.name}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Amount and Date */}
                  <div className="text-right">
                    <p
                      className={`text-lg font-semibold ${getTransactionColor(transaction.type)}`}
                    >
                      {transaction.type === 'INCOME'
                        ? '+'
                        : transaction.type === 'EXPENSE'
                          ? '-'
                          : ''}
                      {formatCurrency(
                        parseFloat(transaction.totalAmount),
                        transaction.account.currency
                      )}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(transaction.transactionDate)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatRelativeTime(transaction.transactionDate)}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="ml-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            setSelectedTransaction(
                              selectedTransaction === transaction.id
                                ? null
                                : transaction.id
                            )
                          }
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Ver Detalhes
                        </DropdownMenuItem>
                        {onEdit && (
                          <DropdownMenuItem
                            onClick={() => onEdit(transaction)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                        )}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem
                              onSelect={(e) => e.preventDefault()}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Cancelar
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Cancelar Transação</AlertDialogTitle>
                              <AlertDialogDescription>
                                Tem certeza que deseja cancelar esta transação futura?
                                Esta ação não pode ser desfeita.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleCancelTransaction(transaction.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Confirmar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Expanded Details */}
                {selectedTransaction === transaction.id && (
                  <div className="mt-4 border-t pt-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Criado em:</span>
                        <span className="ml-2 font-medium">
                          {formatDate(transaction.createdAt)}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Tipo:</span>
                        <span className="ml-2 font-medium">
                          {transaction.type === 'INCOME'
                            ? 'Receita'
                            : transaction.type === 'EXPENSE'
                              ? 'Despesa'
                              : 'Transferência'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Mostrando {(pagination.page - 1) * pagination.limit + 1} a{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
                {pagination.total} transações
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Anterior
                </Button>

                <span className="text-sm text-muted-foreground px-2">
                  Página {pagination.page} de {pagination.totalPages}
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Próxima
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
