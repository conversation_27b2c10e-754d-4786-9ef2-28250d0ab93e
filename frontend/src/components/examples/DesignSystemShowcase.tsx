import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useTheme } from '@/contexts/ThemeProvider'

export function DesignSystemShowcase() {
  const { theme, setTheme, isDark } = useTheme()

  return (
    <div className="min-h-screen bg-gradient-deep">
      <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gradient-deep">
          Sistema de Design Dark Ocean
        </h1>
        <p className="text-lg text-primary-100 max-w-2xl mx-auto">
          Demonstração completa dos componentes e tokens do sistema de design oceânico
          com foco em sofisticação, modernidade e elegância.
        </p>
        
        {/* Theme Toggle */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant={theme === 'light' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('light')}
          >
            Claro
          </Button>
          <Button
            variant={theme === 'dark' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('dark')}
          >
            Escuro
          </Button>
          <Button
            variant={theme === 'system' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('system')}
          >
            Sistema
          </Button>
        </div>
      </div>

      {/* Color Palette */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Paleta de Cores</CardTitle>
          <CardDescription>
            Dark Ocean como cor primária com tons oceânicos complementares
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Colors */}
          <div>
            <h3 className="text-sm font-medium mb-3">Cores Primárias</h3>
            <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
              {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                <div key={shade} className="text-center">
                  <div
                    className={`w-12 h-12 rounded-lg border shadow-soft bg-primary-${shade}`}
                  />
                  <span className="text-xs text-muted-foreground mt-1 block">
                    {shade}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Semantic Colors */}
          <div>
            <h3 className="text-sm font-medium mb-3">Cores Semânticas</h3>
            <div className="grid grid-cols-4 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 rounded-lg bg-success shadow-soft mx-auto" />
                <span className="text-xs text-muted-foreground mt-1 block">Sucesso</span>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-lg bg-warning shadow-soft mx-auto" />
                <span className="text-xs text-muted-foreground mt-1 block">Aviso</span>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-lg bg-destructive shadow-soft mx-auto" />
                <span className="text-xs text-muted-foreground mt-1 block">Erro</span>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-lg bg-info shadow-soft mx-auto" />
                <span className="text-xs text-muted-foreground mt-1 block">Info</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Tipografia</CardTitle>
          <CardDescription>
            Hierarquia tipográfica com Inter como fonte principal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-4xl font-bold text-foreground">Display - 64px</div>
          <div className="text-3xl font-bold text-foreground">H1 - 48px</div>
          <div className="text-2xl font-semibold text-foreground">H2 - 36px</div>
          <div className="text-xl font-semibold text-foreground">H3 - 30px</div>
          <div className="text-lg font-medium text-foreground">H4 - 24px</div>
          <div className="text-base font-medium text-foreground">H5 - 20px</div>
          <div className="text-sm font-medium text-foreground">H6 - 18px</div>
          <div className="text-base text-foreground">Body - 16px</div>
          <div className="text-sm text-muted-foreground">Small - 14px</div>
          <div className="text-xs text-muted-foreground">Caption - 12px</div>
        </CardContent>
      </Card>

      {/* Buttons */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Botões</CardTitle>
          <CardDescription>
            Variações de botões com estados interativos
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Variants */}
          <div>
            <h3 className="text-sm font-medium mb-3">Variações</h3>
            <div className="flex flex-wrap gap-3">
              <Button variant="default">Primário</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="secondary">Secundário</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
              <Button variant="destructive">Destrutivo</Button>
              <Button variant="success">Sucesso</Button>
              <Button variant="warning">Aviso</Button>
              <Button variant="info">Info</Button>
            </div>
          </div>

          {/* Sizes */}
          <div>
            <h3 className="text-sm font-medium mb-3">Tamanhos</h3>
            <div className="flex flex-wrap items-center gap-3">
              <Button size="sm">Pequeno</Button>
              <Button size="default">Padrão</Button>
              <Button size="lg">Grande</Button>
              <Button size="icon">🎨</Button>
            </div>
          </div>

          {/* States */}
          <div>
            <h3 className="text-sm font-medium mb-3">Estados</h3>
            <div className="flex flex-wrap gap-3">
              <Button>Normal</Button>
              <Button disabled>Desabilitado</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cards */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Cards</CardTitle>
          <CardDescription>
            Diferentes variações de cards com elevações
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card variant="default">
              <CardHeader>
                <CardTitle className="text-base">Padrão</CardTitle>
                <CardDescription>Card com sombra sutil</CardDescription>
              </CardHeader>
            </Card>

            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="text-base">Elevado</CardTitle>
                <CardDescription>Card com mais elevação</CardDescription>
              </CardHeader>
            </Card>

            <Card variant="outlined">
              <CardHeader>
                <CardTitle className="text-base">Outlined</CardTitle>
                <CardDescription>Card apenas com borda</CardDescription>
              </CardHeader>
            </Card>

            <Card variant="glass">
              <CardHeader>
                <CardTitle className="text-base">Glass</CardTitle>
                <CardDescription>Card com efeito glass</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Form Elements */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Elementos de Formulário</CardTitle>
          <CardDescription>
            Inputs e componentes de formulário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Input Padrão</label>
              <Input placeholder="Digite algo..." />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Input Erro</label>
              <Input variant="error" placeholder="Campo obrigatório" />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Input Sucesso</label>
              <Input variant="success" placeholder="Campo válido" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Badges */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Badges</CardTitle>
          <CardDescription>
            Indicadores e etiquetas coloridas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-3">Variações</h3>
            <div className="flex flex-wrap gap-2">
              <Badge variant="default">Padrão</Badge>
              <Badge variant="secondary">Secundário</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Erro</Badge>
              <Badge variant="success">Sucesso</Badge>
              <Badge variant="warning">Aviso</Badge>
              <Badge variant="info">Info</Badge>
              <Badge variant="ghost">Ghost</Badge>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-3">Tamanhos</h3>
            <div className="flex flex-wrap items-center gap-2">
              <Badge size="sm">Pequeno</Badge>
              <Badge size="default">Padrão</Badge>
              <Badge size="lg">Grande</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shadows and Effects */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle>Sombras e Efeitos</CardTitle>
          <CardDescription>
            Sistema de elevação e efeitos visuais
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 rounded-lg bg-card shadow-soft">
              <div className="text-sm font-medium">Soft</div>
              <div className="text-xs text-muted-foreground">shadow-soft</div>
            </div>
            
            <div className="p-4 rounded-lg bg-card shadow-elegant">
              <div className="text-sm font-medium">Elegant</div>
              <div className="text-xs text-muted-foreground">shadow-elegant</div>
            </div>
            
            <div className="p-4 rounded-lg bg-card shadow-glow">
              <div className="text-sm font-medium">Glow</div>
              <div className="text-xs text-muted-foreground">shadow-glow</div>
            </div>
            
            <div className="p-4 rounded-lg bg-card shadow-deep">
              <div className="text-sm font-medium">Deep</div>
              <div className="text-xs text-muted-foreground">shadow-deep</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status */}
      <Card variant="glass" className="text-center">
        <CardContent className="pt-6">
          <div className="space-y-2">
            <Badge variant="success" size="lg">
              Sistema de Design Implementado
            </Badge>
            <p className="text-sm text-muted-foreground">
              Modo atual: <span className="font-medium">{isDark ? 'Escuro' : 'Claro'}</span>
            </p>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}