import { NavLink } from 'react-router-dom'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  CreditCard,
  Calendar,
  PiggyBank,
  Target,
  Calculator,
  Tags,
  Hash,
  Repeat,
  Users,
  Settings,
  BarChart3,
  X,
  TrendingUp,
  Palette,
} from 'lucide-react'

interface SidebarProps {
  open: boolean
  onClose: () => void
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Transações',
    href: '/transactions',
    icon: CreditCard,
  },
  {
    name: 'Transações Futuras',
    href: '/future-transactions',
    icon: Calendar,
  },
  {
    name: '<PERSON><PERSON>',
    href: '/accounts',
    icon: PiggyBank,
  },
  {
    name: 'Metas Financeiras',
    href: '/goals',
    icon: Target,
  },
  {
    name: 'Orçamentos',
    href: '/budgets',
    icon: Calculator,
  },
  {
    name: 'Categorias',
    href: '/categories',
    icon: Tags,
  },
  {
    name: 'Tags',
    href: '/tags',
    icon: Hash,
  },
  {
    name: 'Recorrent<PERSON>',
    href: '/recurring',
    icon: Repeat,
  },
  {
    name: '<PERSON><PERSON><PERSON> da Família',
    href: '/family-members',
    icon: Users,
  },
  {
    name: 'Estatísticas',
    href: '/stats',
    icon: BarChart3,
  },
  {
    name: 'Configurações',
    href: '/settings',
    icon: Settings,
  },
  {
    name: 'Componentes',
    href: '/components',
    icon: Palette,
  },
]

export function Sidebar({ open, onClose }: SidebarProps) {
  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-border bg-card/95 backdrop-blur-md px-6 pb-4 shadow-elegant">
          <div className="flex h-16 shrink-0 items-center">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-glow">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-gradient-deep">
                  Finance Manager
                </span>
                <span className="text-xs text-muted-foreground">
                  Dark Ocean
                </span>
              </div>
            </div>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          cn(
                            'group flex gap-x-3 rounded-lg p-3 text-sm font-medium leading-6 transition-all duration-200',
                            isActive
                              ? 'bg-gradient-deep text-white shadow-glow'
                              : 'text-muted-foreground hover:bg-accent hover:text-foreground hover:shadow-soft'
                          )
                        }
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 z-50 flex w-64 flex-col transition-transform duration-300 ease-in-out lg:hidden',
          open ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-border bg-card/95 backdrop-blur-md px-6 pb-4 shadow-elegant">
          <div className="flex h-16 shrink-0 items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-glow">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-gradient-deep">
                  Finance Manager
                </span>
                <span className="text-xs text-muted-foreground">
                  Dark Ocean
                </span>
              </div>
            </div>
            <button
              type="button"
              className="text-muted-foreground hover:text-foreground transition-colors p-1 rounded-lg hover:bg-accent"
              onClick={onClose}
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <NavLink
                        to={item.href}
                        onClick={onClose}
                        className={({ isActive }) =>
                          cn(
                            'group flex gap-x-3 rounded-lg p-3 text-sm font-medium leading-6 transition-all duration-200',
                            isActive
                              ? 'bg-gradient-deep text-white shadow-glow'
                              : 'text-muted-foreground hover:bg-accent hover:text-foreground hover:shadow-soft'
                          )
                        }
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </>
  )
}
