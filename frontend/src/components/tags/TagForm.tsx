import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateTag, useUpdateTag } from '@/hooks/useTags'
import type { Tag } from '@/types/tag.types'

// Validation schema
const tagSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(50, 'Nome deve ter no máximo 50 caracteres')
    .trim(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
})

type TagFormData = z.infer<typeof tagSchema>

interface TagFormProps {
  mode: 'create' | 'edit'
  tag?: Tag
  onSuccess: () => void
  onCancel: () => void
}

// Predefined color options
const colorOptions = [
  '#ef4444', // red
  '#f97316', // orange
  '#eab308', // yellow
  '#22c55e', // green
  '#06b6d4', // cyan
  '#3b82f6', // blue
  '#8b5cf6', // violet
  '#ec4899', // pink
  '#6b7280', // gray
  '#1f2937', // dark gray
]

export function TagForm({ mode, tag, onSuccess, onCancel }: TagFormProps) {
  const createTagMutation = useCreateTag()
  const updateTagMutation = useUpdateTag()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<TagFormData>({
    resolver: zodResolver(tagSchema),
    defaultValues: {
      name: tag?.name || '',
      color: tag?.color || '#3b82f6',
    }
  })

  const watchedColor = watch('color')

  const onSubmit = async (data: TagFormData) => {
    try {
      if (mode === 'create') {
        await createTagMutation.mutateAsync(data as any)
      } else if (tag) {
        await updateTagMutation.mutateAsync({
          id: tag.id,
          data: data as any
        })
      }
      onSuccess()
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  }

  const isLoading = isSubmitting || createTagMutation.isPending || updateTagMutation.isPending

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Name Field */}
      <div className="space-y-2">
        <Label htmlFor="name" className="text-foreground">
          Nome da Tag
        </Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Digite o nome da tag"
          className="bg-background border-border text-foreground placeholder:text-muted-foreground"
          disabled={isLoading}
        />
        {errors.name && (
          <p className="text-sm text-destructive">{errors.name.message}</p>
        )}
      </div>

      {/* Color Field */}
      <div className="space-y-2">
        <Label htmlFor="color" className="text-foreground">
          Cor da Tag
        </Label>

        {/* Color Input */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <input
              id="color"
              type="color"
              {...register('color')}
              className="h-10 w-16 rounded border border-border bg-background cursor-pointer"
              disabled={isLoading}
            />
          </div>
          <Input
            {...register('color')}
            placeholder="#3b82f6"
            className="bg-background border-border text-foreground placeholder:text-muted-foreground font-mono"
            disabled={isLoading}
          />
        </div>

        {/* Predefined Colors */}
        <div className="space-y-2">
          <Label className="text-sm text-muted-foreground">
            Cores sugeridas:
          </Label>
          <div className="flex flex-wrap gap-2">
            {colorOptions.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => setValue('color', color)}
                className={`h-8 w-8 rounded border-2 transition-all hover:scale-110 ${
                  watchedColor === color
                    ? 'border-foreground shadow-lg'
                    : 'border-border hover:border-muted-foreground'
                }`}
                style={{ backgroundColor: color }}
                disabled={isLoading}
                title={color}
              />
            ))}
          </div>
        </div>

        {errors.color && (
          <p className="text-sm text-destructive">{errors.color.message}</p>
        )}
      </div>

      {/* Preview */}
      <div className="space-y-2">
        <Label className="text-foreground">Preview</Label>
        <div className="flex items-center gap-2 p-3 rounded-lg bg-muted border border-border">
          <div
            className="h-4 w-4 rounded-full border border-border"
            style={{ backgroundColor: watchedColor }}
          />
          <span className="text-foreground">
            {watch('name') || 'Nome da tag'}
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-primary-600 hover:bg-primary-700"
        >
          {isLoading ? 'Salvando...' : mode === 'create' ? 'Criar Tag' : 'Salvar Alterações'}
        </Button>
      </div>
    </form>
  )
}
