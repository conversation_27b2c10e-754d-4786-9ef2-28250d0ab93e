import { useState } from 'react'
import {
  Filter,
  X,
  Search,
  Archive,
  Hash
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import type { TagFilters } from '@/types/tag.types'

interface TagFiltersProps {
  filters: TagFilters
  onFiltersChange: (filters: Partial<TagFilters>) => void
}

export function TagsFilters({ filters, onFiltersChange }: TagFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchValue, setSearchValue] = useState(filters.search || '')

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onFiltersChange({ search: searchValue || undefined })
  }

  const handleClearFilters = () => {
    setSearchValue('')
    onFiltersChange({
      search: undefined,
      includeArchived: false,
      sortBy: 'name',
      sortOrder: 'asc',
    })
  }

  const hasActiveFilters = !!(
    filters.search ||
    filters.includeArchived ||
    filters.sortBy !== 'name' ||
    filters.sortOrder !== 'asc'
  )

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Buscar tags por nome..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10 bg-background border-border text-foreground placeholder:text-muted-foreground"
          />
        </div>
        <Button type="submit" variant="outline" size="icon">
          <Search className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={() => setIsExpanded(!isExpanded)}
          className={isExpanded ? 'bg-primary-600 text-white' : ''}
        >
          <Filter className="h-4 w-4" />
        </Button>
        {hasActiveFilters && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={handleClearFilters}
            className="text-destructive hover:text-destructive/80"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </form>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="rounded-lg border border-border bg-muted/50 p-4">
          <div className="grid gap-4 md:grid-cols-3">
            {/* Include Archived */}
            <div className="space-y-2">
              <Label className="text-foreground">Status</Label>
              <Select
                value={filters.includeArchived ? 'all' : 'active'}
                onValueChange={(value) =>
                  onFiltersChange({ includeArchived: value === 'all' })
                }
              >
                <SelectTrigger className="bg-background border-border text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-background border-border">
                  <SelectItem value="active" className="text-foreground hover:bg-muted" textValue="Apenas Ativas">
                    # Apenas Ativas
                  </SelectItem>
                  <SelectItem value="all" className="text-foreground hover:bg-muted" textValue="Todas (incluindo arquivadas)">
                    📦 Todas (incluindo arquivadas)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="space-y-2">
              <Label className="text-foreground">Ordenar por</Label>
              <Select
                value={filters.sortBy || 'name'}
                onValueChange={(value) =>
                  onFiltersChange({ sortBy: value as TagFilters['sortBy'] })
                }
              >
                <SelectTrigger className="bg-background border-border text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-background border-border">
                  <SelectItem value="name" className="text-foreground hover:bg-muted">
                    Nome
                  </SelectItem>
                  <SelectItem value="usageCount" className="text-foreground hover:bg-muted">
                    Uso
                  </SelectItem>
                  <SelectItem value="createdAt" className="text-foreground hover:bg-muted">
                    Data de Criação
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort Order */}
            <div className="space-y-2">
              <Label className="text-foreground">Ordem</Label>
              <Select
                value={filters.sortOrder || 'asc'}
                onValueChange={(value) =>
                  onFiltersChange({ sortOrder: value as TagFilters['sortOrder'] })
                }
              >
                <SelectTrigger className="bg-background border-border text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-background border-border">
                  <SelectItem value="asc" className="text-foreground hover:bg-muted">
                    Crescente
                  </SelectItem>
                  <SelectItem value="desc" className="text-foreground hover:bg-muted">
                    Decrescente
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="mt-4 pt-4 border-t border-border">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Filtros ativos:</span>
                {filters.search && (
                  <span className="px-2 py-1 bg-primary/20 text-primary rounded">
                    Busca: "{filters.search}"
                  </span>
                )}
                {filters.includeArchived && (
                  <span className="px-2 py-1 bg-muted text-foreground rounded">
                    Incluindo arquivadas
                  </span>
                )}
                {filters.sortBy !== 'name' && (
                  <span className="px-2 py-1 bg-muted text-foreground rounded">
                    Ordenação: {filters.sortBy}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
