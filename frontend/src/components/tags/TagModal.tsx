import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { TagForm } from './TagForm'
import type { Tag } from '@/types/tag.types'

interface TagModalProps {
  isOpen: boolean
  mode: 'create' | 'edit'
  tag?: Tag
  onClose: () => void
}

export function TagModal({ isOpen, mode, tag, onClose }: TagModalProps) {
  const title = mode === 'create' ? 'Nova Tag' : 'Editar Tag'
  const description = mode === 'create'
    ? 'Crie uma nova tag para organizar suas transações'
    : 'Edite as informações da tag'

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-deep sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-foreground">{title}</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {description}
          </DialogDescription>
        </DialogHeader>

        <TagForm
          mode={mode}
          tag={tag}
          onSuccess={onClose}
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  )
}
