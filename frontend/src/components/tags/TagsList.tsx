import { useState } from 'react'
import {
  MoreHorizontal,
  Edit,
  Archive,
  Trash2,
  Hash,
  ArchiveRestore
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { TagsFilters } from '@/components/tags/TagFilters'
import { useTags, useArchiveTag, useDeleteTag } from '@/hooks/useTags'
import type { Tag as TagType, TagFilters as TagFiltersType } from '@/types/tag.types'
import { formatDate } from '@/lib/utils'

interface TagsListProps {
  onEditTag: (tag: TagType) => void
}

export function TagsList({ onEditTag }: TagsListProps) {
  const [filters, setFilters] = useState<TagFiltersType>({
    includeArchived: false,
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortOrder: 'asc',
  })

  const { data: response, isLoading, error } = useTags(filters)
  const archiveTagMutation = useArchiveTag()
  const deleteTagMutation = useDeleteTag()

  const tags = (response as any)?.data || []
  const pagination = (response as any)?.pagination

  const handleArchiveTag = (tag: TagType) => {
    archiveTagMutation.mutate({
      id: tag.id,
      archived: !tag.isArchived,
    })
  }

  const handleDeleteTag = (tag: TagType) => {
    if (window.confirm(`Tem certeza que deseja excluir a tag "${tag.name}"? Esta ação não pode ser desfeita.`)) {
      deleteTagMutation.mutate(tag.id)
    }
  }

  const handleFiltersChange = (newFilters: Partial<TagFiltersType>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }))
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="py-12 text-center">
        <Hash className="mx-auto mb-4 h-12 w-12 text-error-400" />
        <h3 className="mb-2 text-lg font-medium text-white">
          Erro ao carregar tags
        </h3>
        <p className="text-secondary-400">
          Ocorreu um erro ao carregar as tags. Tente novamente.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <TagsFilters filters={filters} onFiltersChange={handleFiltersChange} />

      {/* Table */}
      {!tags || (tags as any).length === 0 ? (
        <div className="py-12 text-center">
          <Hash className="mx-auto mb-4 h-12 w-12 text-secondary-600" />
          <h3 className="mb-2 text-lg font-medium text-white">
            {filters.includeArchived ? 'Nenhuma tag encontrada' : 'Nenhuma tag ativa'}
          </h3>
          <p className="text-secondary-400">
            {filters.includeArchived
              ? 'Não há tags cadastradas no sistema.'
              : 'Comece criando sua primeira tag.'
            }
          </p>
        </div>
      ) : (
        <div className="rounded-md border border-secondary-700">
          <Table>
            <TableHeader>
              <TableRow className="border-secondary-700 hover:bg-secondary-800">
                <TableHead className="text-secondary-300">Nome</TableHead>
                <TableHead className="text-secondary-300">Cor</TableHead>
                <TableHead className="text-secondary-300">Uso</TableHead>
                <TableHead className="text-secondary-300">Status</TableHead>
                <TableHead className="text-secondary-300">Criada em</TableHead>
                <TableHead className="text-secondary-300 w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(tags as any).map((tag: any) => (
                <TableRow
                  key={tag.id}
                  className="border-border hover:bg-muted/50"
                >
                  <TableCell className="font-medium text-foreground">
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full border border-border"
                        style={{ backgroundColor: tag.color }}
                      />
                      {tag.name}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-6 w-6 rounded border border-border"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="text-muted-foreground font-mono text-sm">
                        {tag.color}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {tag.usageCount} transações
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={tag.isArchived ? "secondary" : "default"}
                      className={tag.isArchived ? "text-muted-foreground" : "text-success"}
                    >
                      {tag.isArchived ? 'Arquivada' : 'Ativa'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {formatDate(tag.createdAt)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-secondary-800 border-secondary-700">
                        <DropdownMenuItem 
                          onClick={() => onEditTag(tag)}
                          className="text-secondary-300 hover:text-white hover:bg-secondary-700"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleArchiveTag(tag)}
                          className="text-secondary-300 hover:text-white hover:bg-secondary-700"
                        >
                          {tag.isArchived ? (
                            <>
                              <ArchiveRestore className="mr-2 h-4 w-4" />
                              Restaurar
                            </>
                          ) : (
                            <>
                              <Archive className="mr-2 h-4 w-4" />
                              Arquivar
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-secondary-700" />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteTag(tag)}
                          className="text-error-400 hover:text-error-300 hover:bg-error-900/20"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-secondary-400">
            Mostrando {((pagination.page - 1) * pagination.limit) + 1} a{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
            {pagination.total} tags
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page! - 1 }))}
              disabled={pagination.page <= 1}
            >
              Anterior
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, page: prev.page! + 1 }))}
              disabled={pagination.page >= pagination.totalPages}
            >
              Próxima
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
