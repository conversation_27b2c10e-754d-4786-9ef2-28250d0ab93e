import { useState, memo } from 'react'
import {
  Target,
  Edit,
  Trash2,
  TrendingUp,
  TrendingDown,
  Calendar,
  Users,
  MoreHorizontal,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { formatCurrency, formatDate, cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { UpdateProgressModal } from './UpdateProgressModal'
import { MilestonesList } from './MilestonesList'
import { GoalProgressHistory } from './GoalProgressHistory'
import type { FinancialGoal, GoalWithProgress } from '@/types/goal.types'

interface GoalCardProps {
  goal: GoalWithProgress
  onEdit: (goal: FinancialGoal) => void
  onDelete: (goalId: string) => void
  onUpdateProgress?: (goalId: string, amount: number, operation?: any, description?: string) => void
  onAddMilestone?: (goalId: string) => void
  onEditMilestone?: (milestone: any) => void
  onDeleteMilestone?: (milestoneId: string) => void
}

export const GoalCard = memo(function GoalCard({
  goal,
  onEdit,
  onDelete,
  onUpdateProgress,
  onAddMilestone,
  onEditMilestone,
  onDeleteMilestone
}: GoalCardProps) {
  const [isUpdateProgressOpen, setIsUpdateProgressOpen] = useState(false)
  const [isMilestonesExpanded, setIsMilestonesExpanded] = useState(false)

  const progress = goal.progress || {
    percentage: 0,
    remainingAmount: goal.targetAmount,
    isCompleted: false,
    isOverdue: false,
    status: 'not_started' as const
  }

  const getStatusIcon = () => {
    switch (progress.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-destructive" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />
      default:
        return <Target className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStatusBadge = () => {
    switch (progress.status) {
      case 'completed':
        return <Badge variant="success">Concluída</Badge>
      case 'overdue':
        return <Badge variant="destructive">Vencida</Badge>
      case 'in_progress':
        return <Badge variant="default">Em Progresso</Badge>
      default:
        return <Badge variant="secondary">Não Iniciada</Badge>
    }
  }

  const getProgressColor = () => {
    if (progress.isCompleted) return 'bg-success'
    if (progress.isOverdue) return 'bg-destructive'

    // Para metas de redução, usar cores vermelhas
    if (goal.goalType === 'REDUCTION') {
      if (progress.percentage >= 75) return 'bg-destructive'
      if (progress.percentage >= 50) return 'bg-destructive/80'
      if (progress.percentage >= 25) return 'bg-destructive/60'
      return 'bg-destructive/40'
    }

    // Para metas de acumulação, usar cores verdes/azuis
    if (progress.percentage >= 75) return 'bg-success'
    if (progress.percentage >= 50) return 'bg-warning'
    if (progress.percentage >= 25) return 'bg-info'
    return 'bg-muted-foreground'
  }

  const handleUpdateProgress = (amount: number, operation: any, description?: string) => {
    if (onUpdateProgress) {
      onUpdateProgress(goal.id, amount, operation, description)
    }
    setIsUpdateProgressOpen(false)
  }

  return (
    <>
      <div className="glass-deep p-4 sm:p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300 w-full max-w-full overflow-hidden">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className={`flex h-10 w-10 items-center justify-center rounded-xl shadow-soft flex-shrink-0 ${
              goal.goalType === 'REDUCTION'
                ? 'bg-gradient-to-r from-red-500 to-red-600'
                : 'bg-gradient-deep'
            }`}>
              {goal.goalType === 'REDUCTION' ? (
                <TrendingDown className="h-5 w-5 text-white" />
              ) : (
                <TrendingUp className="h-5 w-5 text-white" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-semibold text-foreground truncate">{goal.name}</h3>
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    goal.goalType === 'REDUCTION'
                      ? 'border-red-500 text-red-500'
                      : 'border-green-500 text-green-500'
                  }`}
                >
                  {goal.goalType === 'REDUCTION' ? 'Redução' : 'Acumulação'}
                </Badge>
              </div>
              <div className="flex items-center gap-1">
                {getStatusIcon()}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-shrink-0">
            <div className="hidden sm:block">
              {getStatusBadge()}
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(goal)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setIsUpdateProgressOpen(true)}>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Atualizar Progresso
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onAddMilestone?.(goal.id)}>
                  <Target className="h-4 w-4 mr-2" />
                  Adicionar Marco
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(goal.id)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Status Badge for mobile */}
        <div className="block sm:hidden mb-4">
          {getStatusBadge()}
        </div>

        {/* Progress Section */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {goal.goalType === 'REDUCTION' ? 'Progresso de Redução' : 'Progresso'}
            </span>
            <span className="text-sm font-bold text-foreground">
              {progress.percentage.toFixed(1)}%
            </span>
          </div>
          <Progress
            value={progress.percentage}
            className="h-2 mb-2"
            indicatorClassName={getProgressColor()}
          />
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 text-sm">
            {goal.goalType === 'REDUCTION' ? (
              <>
                <span className="text-muted-foreground">
                  {formatCurrency(goal.initialAmount || 0)} → {formatCurrency(goal.currentAmount)}
                </span>
                <span className="text-muted-foreground">
                  Restam {formatCurrency(progress.remainingAmount)}
                </span>
              </>
            ) : (
              <>
                <span className="text-muted-foreground">
                  {formatCurrency(goal.currentAmount)} de {formatCurrency(goal.targetAmount)}
                </span>
                <span className="text-muted-foreground">
                  Faltam {formatCurrency(progress.remainingAmount)}
                </span>
              </>
            )}
          </div>
        </div>

        {/* Details */}
        <div className="space-y-3">
          {/* Target Date */}
          {goal.targetDate && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Meta para:</span>
                <span className={cn(
                  "font-medium",
                  progress.isOverdue ? "text-destructive" : "text-foreground"
                )}>
                  {formatDate(goal.targetDate)}
                </span>
              </div>
              {progress.daysRemaining !== undefined && (
                <span className="text-muted-foreground text-xs sm:text-sm ml-6 sm:ml-0">
                  ({progress.daysRemaining > 0
                    ? `${progress.daysRemaining} dias restantes`
                    : `${Math.abs(progress.daysRemaining)} dias em atraso`
                  })
                </span>
              )}
            </div>
          )}

          {/* Family Members */}
          {goal.members && goal.members.length > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Membros:</span>
              <div className="flex flex-wrap gap-1">
                {goal.members.map((member: any) => (
                  <Badge
                    key={member.id}
                    variant="outline"
                    className="text-xs"
                    style={{
                      borderColor: member.color || member.familyMember?.color,
                      color: member.color || member.familyMember?.color
                    }}
                  >
                    {member.name || member.familyMember?.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Milestones Count */}
          {goal.milestones && goal.milestones.length > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                {goal.milestones.filter(m => m.isCompleted).length} de {goal.milestones.length} marcos concluídos
              </span>
            </div>
          )}
        </div>

        {/* Milestones Section */}
        {(goal.milestones || onAddMilestone) && (
          <MilestonesList
            milestones={goal.milestones || []}
            isExpanded={isMilestonesExpanded}
            onToggleExpanded={() => setIsMilestonesExpanded(!isMilestonesExpanded)}
            onAddMilestone={() => onAddMilestone?.(goal.id)}
            onEditMilestone={(milestone) => onEditMilestone?.(milestone)}
            onDeleteMilestone={(milestoneId) => onDeleteMilestone?.(milestoneId)}
          />
        )}

        {/* Progress History Section */}
        <GoalProgressHistory goalId={goal.id} />
      </div>

      {/* Update Progress Modal */}
      <UpdateProgressModal
        isOpen={isUpdateProgressOpen}
        onClose={() => setIsUpdateProgressOpen(false)}
        onSubmit={handleUpdateProgress}
        goal={goal}
      />
    </>
  )
})
