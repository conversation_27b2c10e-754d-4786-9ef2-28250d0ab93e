"use client"

import { useCallback } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal, Edit, Archive, Trash2, RotateCcw, Eye, Loader2 } from "lucide-react"
import { <PERSON> } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { FamilyMember } from "@/types/family-member.types"
import { formatDistanceToNow } from "date-fns"
import { ptBR } from "date-fns/locale"

interface FamilyMemberActionsProps {
  member: FamilyMember
  onEdit: (member: FamilyMember) => void
  onArchive: (id: string) => void
  onRestore: (id: string) => void
  onDelete: (id: string) => void
  isLoading?: boolean
  loadingAction?: string
}

function FamilyMemberActions({
  member,
  onEdit,
  onArchive,
  onRestore,
  onDelete,
  isLoading = false,
  loadingAction
}: FamilyMemberActionsProps) {
  const isArchived = member.archived

  // Memoize handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(member), [onEdit, member])
  const handleArchive = useCallback(() => onArchive(member.id), [onArchive, member.id])
  const handleRestore = useCallback(() => onRestore(member.id), [onRestore, member.id])
  const handleDelete = useCallback(() => onDelete(member.id), [onDelete, member.id])
  const handleCopyId = useCallback(() => {
    navigator.clipboard.writeText(member.id)
  }, [member.id])

  const isActionLoading = (action: string) => isLoading && loadingAction === action

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0"
          disabled={isLoading}
          aria-label={`Ações para ${member.name}`}
        >
          <span className="sr-only">Abrir menu de ações para {member.name}</span>
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <MoreHorizontal className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Ações para {member.name}</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={handleCopyId}
          disabled={isLoading}
        >
          Copiar ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link
            to={`/family-members/${member.id}`}
            className={isLoading ? "pointer-events-none opacity-50" : ""}
          >
            <Eye className="mr-2 h-4 w-4" />
            Ver Perfil
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleEdit}
          disabled={isLoading}
        >
          {isActionLoading('edit') && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {!isActionLoading('edit') && <Edit className="mr-2 h-4 w-4" />}
          Editar
        </DropdownMenuItem>
        {!isArchived ? (
          <DropdownMenuItem
            onClick={handleArchive}
            disabled={isLoading}
          >
            {isActionLoading('archive') && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {!isActionLoading('archive') && <Archive className="mr-2 h-4 w-4" />}
            Arquivar
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem
            onClick={handleRestore}
            disabled={isLoading}
          >
            {isActionLoading('restore') && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {!isActionLoading('restore') && <RotateCcw className="mr-2 h-4 w-4" />}
            Restaurar
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          className="text-destructive focus:text-destructive"
          disabled={isLoading}
        >
          {isActionLoading('delete') && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {!isActionLoading('delete') && <Trash2 className="mr-2 h-4 w-4" />}
          Excluir permanentemente
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export function createFamilyMemberColumns(
  onEdit: (member: FamilyMember) => void,
  onArchive: (id: string) => void,
  onRestore: (id: string) => void,
  onDelete: (id: string) => void,
  loadingStates?: Record<string, { isLoading: boolean; action?: string }>
): ColumnDef<FamilyMember>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() ? "indeterminate" : false)
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Selecionar todos"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Selecionar linha"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Nome
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const member = row.original
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={member.avatar} alt={member.name} />
              <AvatarFallback style={{ backgroundColor: member.color }}>
                {member.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <Link
                to={`/family-members/${member.id}`}
                className="font-medium hover:underline"
              >
                {member.name}
              </Link>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "color",
      header: "Cor",
      cell: ({ row }) => {
        const color = row.getValue("color") as string
        return (
          <div className="flex items-center space-x-2">
            <div
              className="h-4 w-4 rounded-full border border-gray-300"
              style={{ backgroundColor: color }}
            />
            <span className="text-sm text-muted-foreground">{color}</span>
          </div>
        )
      },
    },
    {
      accessorKey: "archived",
      header: "Status",
      cell: ({ row }) => {
        const archived = row.getValue("archived") as boolean
        return (
          <Badge variant={archived ? "secondary" : "default"}>
            {archived ? "Arquivado" : "Ativo"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Criado em
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"))
        return (
          <div className="text-sm">
            {formatDistanceToNow(date, { addSuffix: true, locale: ptBR })}
          </div>
        )
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const member = row.original
        const memberLoadingState = loadingStates?.[member.id]
        return (
          <FamilyMemberActions
            member={member}
            onEdit={onEdit}
            onArchive={onArchive}
            onRestore={onRestore}
            onDelete={onDelete}
            isLoading={memberLoadingState?.isLoading || false}
            loadingAction={memberLoadingState?.action}
          />
        )
      },
    },
  ]
}
