import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Archive,
  RotateCcw,
  Eye,
  Users,
  Loader2,
  ArrowUpDown
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { FamilyMembersFilters, type FamilyMemberFilters } from './FamilyMembersFilters'
import { FamilyMembersTableSkeleton } from './FamilyMembersTableSkeleton'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import { FamilyMember } from '@/types/family-member.types'
import { formatDistanceToNow } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Link } from 'react-router-dom'

interface FamilyMembersListProps {
  onEdit?: (member: FamilyMember) => void
  onDelete?: (member: FamilyMember) => void
  onArchive?: (member: FamilyMember) => void
  onRestore?: (member: FamilyMember) => void
}

export function FamilyMembersList({
  onEdit,
  onDelete,
  onArchive,
  onRestore
}: FamilyMembersListProps) {
  const [filters, setFilters] = useState<FamilyMemberFilters>({
    includeArchived: true,
    includeDeleted: false
  })

  // Convert frontend filters to backend filters
  const backendFilters = useMemo(() => {
    const baseFilters: any = {
      includeDeleted: false,
      includeArchived: true // Always include archived to let frontend filter
    }

    if (filters.search) {
      baseFilters.name = filters.search
    }

    return baseFilters as FamilyMemberFilters
  }, [filters.search])

  const { data: familyMembers, isLoading } = useFamilyMembers(backendFilters)

  const filteredAndSortedMembers = useMemo(() => {
    let filtered = familyMembers?.data || []

    // Apply status filter (frontend-side)
    if (filters.status === 'active') {
      filtered = filtered.filter(member => !member.archived)
    } else if (filters.status === 'archived') {
      filtered = filtered.filter(member => member.archived)
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      const sortBy = filters.sortBy || 'name'
      const sortOrder = filters.sortOrder || 'asc'

      let aValue: any = a[sortBy as keyof FamilyMember]
      let bValue: any = b[sortBy as keyof FamilyMember]

      // Handle date sorting
      if (sortBy === 'createdAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })
  }, [familyMembers?.data, filters.status, filters.sortBy, filters.sortOrder])

  const handleClearFilters = () => {
    setFilters({
      includeArchived: true,
      includeDeleted: false,
      search: undefined,
      status: undefined,
      sortBy: 'name',
      sortOrder: 'asc'
    })
  }

  const handleSort = (column: 'name' | 'createdAt') => {
    const currentSortBy = filters.sortBy || 'name'
    const currentSortOrder = filters.sortOrder || 'asc'

    if (currentSortBy === column) {
      // Toggle sort order if same column
      setFilters({
        ...filters,
        sortOrder: currentSortOrder === 'asc' ? 'desc' : 'asc'
      })
    } else {
      // Set new column with ascending order
      setFilters({
        ...filters,
        sortBy: column,
        sortOrder: 'asc'
      })
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <FamilyMembersFilters
          filters={filters}
          onFiltersChange={setFilters}
          onClearFilters={handleClearFilters}
        />
        <FamilyMembersTableSkeleton />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <FamilyMembersFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={handleClearFilters}
      />

      <div className="card">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('name')}
                  className="h-auto p-0 font-medium"
                >
                  Membro
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Cor</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('createdAt')}
                  className="h-auto p-0 font-medium"
                >
                  Criado em
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Users className="h-8 w-8 text-secondary-500" />
                    <div className="text-sm text-secondary-400">
                      {filters.search ? 'Nenhum membro encontrado com os filtros aplicados.' : 'Nenhum membro cadastrado.'}
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback style={{ backgroundColor: member.color }}>
                          {member.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <Link
                          to={`/family-members/${member.id}`}
                          className="font-medium hover:underline text-white"
                        >
                          {member.name}
                        </Link>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-4 w-4 rounded-full border border-secondary-600"
                        style={{ backgroundColor: member.color }}
                      />
                      <span className="text-sm text-secondary-400">{member.color}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={member.archived ? "secondary" : "default"}>
                      {member.archived ? "Arquivado" : "Ativo"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-secondary-400">
                      {formatDistanceToNow(new Date(member.createdAt), { 
                        addSuffix: true, 
                        locale: ptBR 
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link to={`/family-members/${member.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver Perfil
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEdit?.(member)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Editar
                        </DropdownMenuItem>
                        {!member.archived ? (
                          <DropdownMenuItem onClick={() => onArchive?.(member)}>
                            <Archive className="mr-2 h-4 w-4" />
                            Arquivar
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => onRestore?.(member)}>
                            <RotateCcw className="mr-2 h-4 w-4" />
                            Restaurar
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete?.(member)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
