import { Transaction, ExpandedTransactionRow } from '@/types/transaction.types'

/**
 * Expands transactions into individual installment rows for table display
 * Each installment becomes a separate row in the table
 */
export function expandTransactionsToInstallments(transactions: Transaction[]): ExpandedTransactionRow[] {
  return transactions.flatMap(transaction => 
    transaction.installments.map(installment => ({
      // Unique row ID combining transaction and installment
      id: `${transaction.id}-${installment.installmentNumber}`,
      
      // Original transaction data
      transactionId: transaction.id,
      description: transaction.description,
      totalAmount: transaction.totalAmount,
      totalInstallments: transaction.totalInstallments,
      transactionDate: transaction.transactionDate,
      type: transaction.type,
      accountId: transaction.accountId,
      categoryId: transaction.categoryId,
      destinationAccountId: transaction.destinationAccountId,
      exchangeRate: transaction.exchangeRate,
      isFuture: transaction.isFuture,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      
      // Specific installment data
      installmentId: installment.id,
      installmentNumber: installment.installmentNumber,
      installmentAmount: installment.amount,
      installmentDueDate: installment.dueDate,
      installmentIsPaid: installment.isPaid,
      installmentPaidAt: installment.paidAt,
      installmentDescription: installment.description,
      
      // Related data (same as transaction)
      account: transaction.account,
      category: transaction.category,
      destinationAccount: transaction.destinationAccount,
      tags: transaction.tags,
      familyMembers: transaction.familyMembers
    }))
  )
}

/**
 * Calculates installment progress for a transaction
 */
export function calculateInstallmentProgress(transaction: Transaction) {
  const totalInstallments = transaction.installments.length
  const paidInstallments = transaction.installments.filter(i => i.isPaid).length
  const paidAmount = transaction.installments
    .filter(i => i.isPaid)
    .reduce((sum, i) => sum + i.amount, 0)
  const remainingAmount = transaction.totalAmount - paidAmount
  const percentage = totalInstallments > 0 ? (paidInstallments / totalInstallments) * 100 : 0

  return {
    totalInstallments,
    paidInstallments,
    paidAmount,
    remainingAmount,
    percentage
  }
}

/**
 * Gets the status of an installment
 */
export function getInstallmentStatus(installment: { isPaid: boolean; dueDate: string }) {
  if (installment.isPaid) {
    return 'paid'
  }
  
  const dueDate = new Date(installment.dueDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  dueDate.setHours(0, 0, 0, 0)
  
  if (dueDate < today) {
    return 'overdue'
  } else if (dueDate.getTime() === today.getTime()) {
    return 'due_today'
  } else {
    return 'pending'
  }
}

/**
 * Gets the appropriate color class for installment status
 */
export function getInstallmentStatusColor(status: string) {
  switch (status) {
    case 'paid':
      return 'text-success bg-success/10 border-success/20'
    case 'overdue':
      return 'text-destructive bg-destructive/10 border-destructive/20'
    case 'due_today':
      return 'text-warning bg-warning/10 border-warning/20'
    case 'pending':
      return 'text-info bg-info/10 border-info/20'
    default:
      return 'text-muted-foreground bg-muted border-border'
  }
}

/**
 * Gets the appropriate label for installment status
 */
export function getInstallmentStatusLabel(status: string) {
  switch (status) {
    case 'paid':
      return 'Pago'
    case 'overdue':
      return 'Vencido'
    case 'due_today':
      return 'Vence Hoje'
    case 'pending':
      return 'Pendente'
    default:
      return 'Desconhecido'
  }
}
