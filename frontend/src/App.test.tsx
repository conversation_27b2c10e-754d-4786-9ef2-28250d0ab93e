import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import App from './App'

// Mock the auth store
vi.mock('@/stores/auth.store', () => ({
  useAuthStore: () => ({
    isAuthenticated: false,
    user: null,
    login: vi.fn(),
    logout: vi.fn(),
  }),
}))

// Mock all page components
vi.mock('@/pages/auth/LoginPage', () => ({
  LoginPage: () => <div data-testid="login-page">Login Page</div>,
}))

vi.mock('@/pages/dashboard/DashboardPage', () => ({
  DashboardPage: () => <div data-testid="dashboard-page">Dashboard Page</div>,
}))

vi.mock('@/components/layout/Layout', () => ({
  Layout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="layout">{children}</div>
  ),
}))

vi.mock('@/components/auth/ProtectedRoute', () => ({
  ProtectedRoute: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="protected-route">{children}</div>
  ),
}))

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>)
}

describe('App', () => {
  it('should render without crashing', () => {
    renderWithRouter(<App />)
  })

  it('should apply correct background classes', () => {
    const { container } = renderWithRouter(<App />)
    const appDiv = container.firstChild as HTMLElement
    expect(appDiv).toHaveClass('min-h-screen', 'bg-background')
  })
})
