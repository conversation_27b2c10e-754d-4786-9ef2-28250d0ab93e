import { Routes, Route } from 'react-router-dom'
import { Layout } from '@/components/layout/Layout'
import { LoginPage } from '@/pages/auth/LoginPage'
import { RegisterPage } from '@/pages/auth/RegisterPage'
import { DashboardPage } from '@/pages/dashboard/DashboardPage'
import { TransactionsPage } from '@/pages/transactions/TransactionsPage'
import { FutureTransactionsPage } from '@/pages/transactions/FutureTransactionsPage'
import { AccountsPage } from '@/pages/accounts/AccountsPage'
import { GoalsPage } from '@/pages/goals/GoalsPage'
import { BudgetsPage } from '@/pages/budgets/BudgetsPage'
import { CategoriesPage } from '@/pages/categories/CategoriesPage'
import { TagsPage } from '@/pages/tags/TagsPage'
import { RecurringTransactionsPage } from '@/pages/recurring/RecurringTransactionsPage'
import { FamilyMembersPage } from '@/pages/family/FamilyMembersPage'
import { FamilyMemberProfilePage } from '@/pages/family/FamilyMemberProfilePage'
import { SettingsPage } from '@/pages/settings/SettingsPage'
import { StatsPage } from '@/pages/stats/StatsPage'
import { ComponentShowcase } from '@/components/examples/ComponentShowcase'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'

function App() {
  return (
    <div className="min-h-screen bg-background">
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />

        {/* Protected Routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/" element={<DashboardPage />} />
                  <Route path="/dashboard" element={<DashboardPage />} />
                  <Route path="/transactions" element={<TransactionsPage />} />
                  <Route
                    path="/future-transactions"
                    element={<FutureTransactionsPage />}
                  />
                  <Route path="/accounts" element={<AccountsPage />} />
                  <Route path="/goals" element={<GoalsPage />} />
                  <Route path="/budgets" element={<BudgetsPage />} />
                  <Route path="/categories" element={<CategoriesPage />} />
                  <Route path="/tags" element={<TagsPage />} />
                  <Route
                    path="/recurring"
                    element={<RecurringTransactionsPage />}
                  />
                  <Route path="/family-members" element={<FamilyMembersPage />} />
                  <Route path="/family-members/:id" element={<FamilyMemberProfilePage />} />
                  <Route path="/stats" element={<StatsPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                  <Route path="/components" element={<ComponentShowcase />} />
                  {/* Fallback route - redirect unknown routes to dashboard */}
                  <Route path="*" element={<DashboardPage />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

export default App
