# Personal Finance Manager - Frontend

Frontend da aplicação Personal Finance Manager construí<PERSON> com React, TypeScript, Vite e shadcn/ui.

## 🚀 Tecnologias

- **React 18** - Biblioteca para interfaces de usuário
- **TypeScript** - Superset tipado do JavaScript
- **Vite** - Build tool e dev server
- **Tailwind CSS** - Framework CSS utilitário
- **shadcn/ui** - Componentes UI reutilizáveis
- **Zustand** - Gerenciamento de estado
- **React Query** - Gerenciamento de estado do servidor
- **React Router** - Roteamento
- **React Hook Form** - Gerenciamento de formulários
- **Zod** - Validação de schemas
- **Vitest** - Framework de testes
- **Axios** - Cliente HTTP

## 📁 Estrutura do Projeto

```
frontend/
├── public/                 # Arquivos estáticos
├── src/
│   ├── components/        # Componentes React
│   │   ├── ui/           # Componentes base (shadcn/ui)
│   │   ├── examples/     # Exemplos de componentes
│   │   └── ...           # Componentes específicos
│   ├── contexts/         # Context providers
│   ├── hooks/            # Custom hooks
│   ├── lib/              # Utilitários e configurações
│   ├── pages/            # Páginas da aplicação
│   ├── stores/           # Stores Zustand
│   ├── types/            # Definições de tipos TypeScript
│   └── main.tsx          # Ponto de entrada
├── components.json       # Configuração shadcn/ui
├── tailwind.config.js    # Configuração Tailwind
├── vite.config.ts        # Configuração Vite
└── vitest.config.ts      # Configuração Vitest
```

## 🎨 Sistema de Design

O projeto utiliza a paleta **"Dark Ocean"** com as seguintes características:

- **Cores primárias**: Dark Ocean sofisticado (#1e2a3a) com tons oceânicos
- **Cores secundárias**: Blue-gray oceânico elegante para máximo contraste
- **Cores de apoio**: Success, warning, info e destructive harmonizadas ao tema oceânico
- **Componentes**: shadcn/ui com customizações avançadas
- **Tipografia**: Inter (via Google Fonts) com pesos otimizados
- **Ícones**: Lucide React
- **Responsividade**: Mobile-first
- **Acessibilidade**: WCAG AA compliant

### Paleta Dark Ocean

```css
:root {
  --primary: 235 100% 69%;        /* Deep blue elegante */
  --secondary: 210 40% 96%;       /* Blue-gray claro */
  --background: 0 0% 100%;        /* Branco puro */
  --foreground: 235 65% 8%;       /* Deep blue escuro */
  --success: 158 64% 52%;         /* Emerald */
  --warning: 43 96% 56%;          /* Amber */
  --info: 217 91% 60%;            /* Blue */
  /* ... */
}

.dark {
  --primary: 235 100% 65%;        /* Deep blue vibrante */
  --secondary: 235 40% 15%;       /* Deep blue escuro */
  --background: 235 65% 8%;       /* Deep blue ultra escuro */
  --foreground: 210 40% 98%;      /* Branco quase puro */
  --card: 235 60% 10%;            /* Deep blue card */
  --border: 235 40% 15%;          /* Deep blue border */
  /* ... */
}
```

## 🧩 Componentes Base

### Componentes shadcn/ui Disponíveis

- **Button** - Botões com variantes (primary, secondary, outline, ghost)
- **Input** - Campos de entrada com validação
- **Card** - Cartões para organizar conteúdo
- **Dialog** - Modais e diálogos
- **Select** - Seletores dropdown
- **Textarea** - Campos de texto multilinha
- **Label** - Rótulos para formulários

### Exemplo de Uso

```tsx
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

function ExampleForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Exemplo</CardTitle>
      </CardHeader>
      <CardContent>
        <Input placeholder="Digite algo..." />
        <Button>Enviar</Button>
      </CardContent>
    </Card>
  )
}
```

## 🔧 Configuração e Desenvolvimento

### Pré-requisitos

- Node.js 18+
- npm ou yarn

### Instalação

```bash
# Instalar dependências
npm install

# Iniciar servidor de desenvolvimento
npm run dev

# Build para produção
npm run build

# Executar testes
npm test

# Executar testes com coverage
npm run test:coverage

# Linting
npm run lint
```

### Variáveis de Ambiente

Crie um arquivo `.env.local`:

```env
VITE_API_URL=http://localhost:3001/api/v1
VITE_APP_NAME=Personal Finance Manager
```

## 🏪 Gerenciamento de Estado

### Stores Zustand

- **authStore** - Autenticação e dados do usuário logado
- **userStore** - Perfil e preferências do usuário
- **settingsStore** - Configurações da aplicação (tema, idioma, etc.)

### Exemplo de Store

```tsx
import { useAuthStore } from '@/stores/auth.store'
import { useSettings } from '@/hooks/useSettings'

function Component() {
  const { user, isAuthenticated } = useAuthStore()
  const { theme, setTheme } = useSettings()
  
  return (
    <div>
      {isAuthenticated && <p>Olá, {user?.name}</p>}
      <button onClick={() => setTheme('dark')}>
        Tema Escuro
      </button>
    </div>
  )
}
```

## 🧪 Testes

### Estrutura de Testes

- **Unit Tests** - Funções utilitárias e hooks
- **Component Tests** - Componentes React
- **Integration Tests** - Fluxos completos

### Executar Testes

```bash
# Todos os testes
npm test

# Modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

### Coverage Atual

- **Statements**: 31.3%
- **Branches**: 90.47%
- **Functions**: 11.86%
- **Lines**: 31.3%

## 📝 Convenções de Código

### Estrutura de Arquivos

- Componentes em PascalCase: `ComponentName.tsx`
- Hooks em camelCase: `useCustomHook.ts`
- Utilitários em camelCase: `utilityFunction.ts`
- Tipos em PascalCase: `TypeName.ts`

### Imports

```tsx
// Bibliotecas externas
import React from 'react'
import { useState } from 'react'

// Componentes UI
import { Button } from '@/components/ui/button'

// Componentes internos
import { CustomComponent } from '@/components/CustomComponent'

// Hooks e utilitários
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'

// Tipos
import type { User } from '@/types/user'
```

## 🚀 Deploy

### Build de Produção

```bash
npm run build
```

Os arquivos serão gerados na pasta `dist/`.

### Análise do Bundle

```bash
npm run build -- --analyze
```

## 📚 Recursos Adicionais

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Vite Documentation](https://vitejs.dev/)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Zustand Documentation](https://github.com/pmndrs/zustand)

## 🤝 Contribuição

1. Siga as convenções de código estabelecidas
2. Escreva testes para novas funcionalidades
3. Mantenha a documentação atualizada
4. Use commits semânticos

## 📄 Licença

Este projeto é privado e proprietário.
