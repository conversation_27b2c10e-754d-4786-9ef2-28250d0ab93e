#!/bin/bash

# =============================================================================
# SCRIPT DE VALIDAÇÃO DE DEPENDÊNCIAS
# Personal Finance Manager - Sistema de Finanças Pessoais
# =============================================================================

set -e  # Exit on any error

echo "🔍 Iniciando validação de dependências..."
echo "📅 Data: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir com cores
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Verificar versões do Node.js e npm
echo "🔍 Verificando versões do ambiente..."
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)

print_status "info" "Node.js: $NODE_VERSION"
print_status "info" "npm: $NPM_VERSION"

# Verificar se as versões atendem aos requisitos
NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [ "$NODE_MAJOR" -ge 18 ]; then
    print_status "success" "Versão do Node.js compatível (>=18.0.0)"
else
    print_status "error" "Versão do Node.js incompatível. Requerido: >=18.0.0"
    exit 1
fi

echo ""

# Função para verificar dependências em um diretório
check_dependencies() {
    local dir=$1
    local name=$2
    
    echo "📦 Verificando dependências: $name"
    
    if [ ! -f "$dir/package.json" ]; then
        print_status "warning" "package.json não encontrado em $dir"
        return
    fi
    
    cd "$dir"
    
    # Verificar se node_modules existe
    if [ ! -d "node_modules" ]; then
        print_status "warning" "node_modules não encontrado em $dir - execute npm install"
        cd - > /dev/null
        return
    fi
    
    # Verificar conflitos de dependências
    CONFLICTS=$(npm ls --depth=0 2>&1 | grep -E "(WARN|ERR)" || true)
    
    if [ -z "$CONFLICTS" ]; then
        print_status "success" "Sem conflitos detectados em $name"
    else
        print_status "warning" "Conflitos detectados em $name:"
        echo "$CONFLICTS" | while read line; do
            echo "    $line"
        done
    fi
    
    cd - > /dev/null
}

# Verificar dependências do workspace raiz
check_dependencies "." "Workspace Raiz"

# Verificar dependências do backend
if [ -d "backend" ]; then
    check_dependencies "backend" "Backend"
else
    print_status "warning" "Diretório backend não encontrado"
fi

# Verificar dependências do frontend
if [ -d "frontend" ]; then
    check_dependencies "frontend" "Frontend"
else
    print_status "warning" "Diretório frontend não encontrado"
fi

# Verificar dependências do shared
if [ -d "shared" ]; then
    check_dependencies "shared" "Shared"
else
    print_status "warning" "Diretório shared não encontrado"
fi

echo ""

# Verificar problemas específicos identificados
echo "🔍 Verificando problemas específicos identificados..."

# 1. Conflito bcrypt vs bcryptjs
echo ""
print_status "info" "Verificando conflito bcrypt..."
BCRYPT_ISSUES=""

if [ -f "backend/package.json" ]; then
    HAS_BCRYPT=$(grep -c '"bcrypt"' backend/package.json || true)
    HAS_BCRYPTJS=$(grep -c '"bcryptjs"' backend/package.json || true)
    
    if [ "$HAS_BCRYPT" -gt 0 ] && [ "$HAS_BCRYPTJS" -gt 0 ]; then
        print_status "error" "CONFLITO: bcrypt E bcryptjs encontrados no backend"
        BCRYPT_ISSUES="true"
    elif [ "$HAS_BCRYPT" -gt 0 ]; then
        print_status "success" "Usando bcrypt (recomendado)"
    elif [ "$HAS_BCRYPTJS" -gt 0 ]; then
        print_status "warning" "Usando bcryptjs (deve migrar para bcrypt)"
        BCRYPT_ISSUES="true"
    else
        print_status "warning" "Nenhuma biblioteca bcrypt encontrada"
    fi
fi

# 2. React Query duplicado
echo ""
print_status "info" "Verificando React Query..."
REACT_QUERY_ISSUES=""

if [ -f "frontend/package.json" ]; then
    HAS_TANSTACK=$(grep -c '"@tanstack/react-query"' frontend/package.json || true)
    HAS_OLD_QUERY=$(grep -c '"react-query"' frontend/package.json || true)
    
    if [ "$HAS_TANSTACK" -gt 0 ] && [ "$HAS_OLD_QUERY" -gt 0 ]; then
        print_status "error" "CONFLITO: @tanstack/react-query E react-query encontrados"
        REACT_QUERY_ISSUES="true"
    elif [ "$HAS_TANSTACK" -gt 0 ]; then
        print_status "success" "Usando @tanstack/react-query v5 (recomendado)"
    elif [ "$HAS_OLD_QUERY" -gt 0 ]; then
        print_status "warning" "Usando react-query v3 (deve migrar para @tanstack/react-query v5)"
        REACT_QUERY_ISSUES="true"
    fi
fi

# 3. Scripts de fix
echo ""
print_status "info" "Verificando scripts de fix..."
FIX_SCRIPTS=$(find . -name "*fix*.ts" -o -name "*fix*.js" | grep -v node_modules || true)

if [ -n "$FIX_SCRIPTS" ]; then
    print_status "warning" "Scripts de fix encontrados (devem ser removidos após refatoração):"
    echo "$FIX_SCRIPTS" | while read script; do
        echo "    $script"
    done
else
    print_status "success" "Nenhum script de fix encontrado"
fi

# Resumo final
echo ""
echo "📊 RESUMO DA VALIDAÇÃO"
echo "======================"

if [ -n "$BCRYPT_ISSUES" ] || [ -n "$REACT_QUERY_ISSUES" ]; then
    print_status "warning" "Problemas críticos identificados - refatoração necessária"
    echo ""
    echo "🔧 Próximos passos:"
    [ -n "$BCRYPT_ISSUES" ] && echo "   1. Resolver conflito bcrypt/bcryptjs"
    [ -n "$REACT_QUERY_ISSUES" ] && echo "   2. Migrar para @tanstack/react-query v5"
    echo "   3. Executar refatoração conforme REFACTOR_ARCHITECTURE_PLAN.md"
else
    print_status "success" "Sistema validado - pronto para desenvolvimento"
fi

echo ""
print_status "info" "Validação concluída em $(date '+%Y-%m-%d %H:%M:%S')"