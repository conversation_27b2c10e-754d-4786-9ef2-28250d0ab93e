#!/bin/bash

# =============================================================================
# SCRIPT DE BACKUP PRÉ-REFATORAÇÃO
# Personal Finance Manager - Sistema de Finanças Pessoais
# =============================================================================

set -e  # Exit on any error

echo "🔄 Iniciando backup completo do sistema..."
echo "📅 Data: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# Verificar se DATABASE_URL está definida
if [ -z "$DATABASE_URL" ]; then
    echo "❌ Erro: DATABASE_URL não está definida"
    echo "💡 Configure a variável de ambiente DATABASE_URL antes de executar o backup"
    exit 1
fi

# Criar diretório de backups se não existir
BACKUP_DIR="backups"
mkdir -p "$BACKUP_DIR"

# Gerar timestamp para o backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_pre_refactor_$TIMESTAMP.sql"

echo "🗄️  Criando backup do banco de dados..."
echo "📁 Arquivo: $BACKUP_FILE"

# Executar backup do PostgreSQL
if pg_dump "$DATABASE_URL" > "$BACKUP_FILE"; then
    echo "✅ Backup do banco criado com sucesso"
    echo "📊 Tamanho: $(du -h "$BACKUP_FILE" | cut -f1)"
else
    echo "❌ Erro ao criar backup do banco de dados"
    exit 1
fi

# Criar tag Git para marcar o ponto de backup
echo ""
echo "🏷️  Criando tag Git..."
GIT_TAG="pre-refactor-$(date +%Y%m%d)"

if git tag -a "$GIT_TAG" -m "Backup antes da refatoração estrutural - $TIMESTAMP"; then
    echo "✅ Tag Git criada: $GIT_TAG"
    
    # Tentar fazer push da tag (não falha se não conseguir)
    if git push origin --tags 2>/dev/null; then
        echo "✅ Tag enviada para o repositório remoto"
    else
        echo "⚠️  Aviso: Não foi possível enviar a tag para o repositório remoto"
        echo "   Execute manualmente: git push origin --tags"
    fi
else
    echo "❌ Erro ao criar tag Git"
    exit 1
fi

# Backup dos arquivos de configuração importantes
echo ""
echo "📋 Criando backup dos arquivos de configuração..."
CONFIG_BACKUP="$BACKUP_DIR/config_backup_$TIMESTAMP.tar.gz"

tar -czf "$CONFIG_BACKUP" \
    package.json \
    backend/package.json \
    frontend/package.json \
    shared/package.json \
    .env.example \
    backend/prisma/schema.prisma \
    2>/dev/null || echo "⚠️  Alguns arquivos de configuração podem não ter sido incluídos"

echo "✅ Backup de configuração criado: $CONFIG_BACKUP"

# Resumo final
echo ""
echo "🎉 Backup completo finalizado com sucesso!"
echo "📁 Arquivos criados:"
echo "   - Banco de dados: $BACKUP_FILE"
echo "   - Configurações: $CONFIG_BACKUP"
echo "   - Tag Git: $GIT_TAG"
echo ""
echo "🔄 Sistema pronto para refatoração!"
echo "💡 Para restaurar: psql \$DATABASE_URL < $BACKUP_FILE"