#!/bin/bash

# =============================================================================
# SCRIPT DE ROLLBACK
# Personal Finance Manager - Sistema de Finanças Pessoais
# =============================================================================

set -e  # Exit on any error

echo "🔄 Iniciando processo de rollback..."
echo "📅 Data: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir com cores
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Função para confirmar ação
confirm_action() {
    local message=$1
    echo -e "${YELLOW}⚠️  $message${NC}"
    read -p "Deseja continuar? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "info" "Operação cancelada pelo usuário"
        exit 0
    fi
}

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "REFACTOR_ARCHITECTURE_PLAN.md" ]; then
    print_status "error" "Execute este script a partir do diretório raiz do projeto"
    exit 1
fi

# Confirmar rollback
confirm_action "ATENÇÃO: Este processo irá reverter todas as mudanças da refatoração!"

echo ""
print_status "info" "Iniciando rollback..."

# 1. Encontrar o backup mais recente
echo ""
print_status "info" "Procurando backups disponíveis..."

BACKUP_DIR="backups"
if [ ! -d "$BACKUP_DIR" ]; then
    print_status "error" "Diretório de backups não encontrado: $BACKUP_DIR"
    exit 1
fi

# Encontrar o backup SQL mais recente
LATEST_BACKUP=$(find "$BACKUP_DIR" -name "backup_pre_refactor_*.sql" -type f | sort -r | head -n 1)

if [ -z "$LATEST_BACKUP" ]; then
    print_status "error" "Nenhum backup de banco encontrado em $BACKUP_DIR"
    exit 1
fi

print_status "success" "Backup encontrado: $LATEST_BACKUP"

# 2. Verificar DATABASE_URL
if [ -z "$DATABASE_URL" ]; then
    print_status "error" "DATABASE_URL não está definida"
    print_status "info" "Configure a variável de ambiente DATABASE_URL"
    exit 1
fi

# 3. Confirmar restauração do banco
confirm_action "Restaurar banco de dados a partir de: $LATEST_BACKUP"

print_status "info" "Restaurando banco de dados..."

# Fazer backup do estado atual antes do rollback
CURRENT_BACKUP="$BACKUP_DIR/backup_before_rollback_$(date +%Y%m%d_%H%M%S).sql"
print_status "info" "Criando backup do estado atual: $CURRENT_BACKUP"

if pg_dump "$DATABASE_URL" > "$CURRENT_BACKUP"; then
    print_status "success" "Backup do estado atual criado"
else
    print_status "error" "Falha ao criar backup do estado atual"
    exit 1
fi

# Restaurar backup
print_status "info" "Restaurando backup do banco..."
if psql "$DATABASE_URL" < "$LATEST_BACKUP" > /dev/null 2>&1; then
    print_status "success" "Banco de dados restaurado com sucesso"
else
    print_status "error" "Falha ao restaurar banco de dados"
    print_status "info" "Backup do estado atual disponível em: $CURRENT_BACKUP"
    exit 1
fi

# 4. Reverter código para tag de backup
echo ""
print_status "info" "Procurando tags de backup..."

# Encontrar a tag mais recente de pre-refactor
BACKUP_TAG=$(git tag -l "pre-refactor-*" | sort -r | head -n 1)

if [ -n "$BACKUP_TAG" ]; then
    print_status "success" "Tag de backup encontrada: $BACKUP_TAG"
    confirm_action "Reverter código para a tag: $BACKUP_TAG"
    
    # Verificar se há mudanças não commitadas
    if ! git diff-index --quiet HEAD --; then
        print_status "warning" "Há mudanças não commitadas"
        confirm_action "Descartar todas as mudanças não commitadas?"
        git reset --hard HEAD
    fi
    
    # Fazer checkout da tag
    if git checkout "$BACKUP_TAG"; then
        print_status "success" "Código revertido para: $BACKUP_TAG"
    else
        print_status "error" "Falha ao reverter para a tag: $BACKUP_TAG"
        exit 1
    fi
else
    print_status "warning" "Nenhuma tag de backup encontrada"
    print_status "info" "Pulando reversão do código"
fi

# 5. Restaurar configurações
echo ""
print_status "info" "Procurando backup de configurações..."

CONFIG_BACKUP=$(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" -type f | sort -r | head -n 1)

if [ -n "$CONFIG_BACKUP" ]; then
    print_status "success" "Backup de configuração encontrado: $CONFIG_BACKUP"
    confirm_action "Restaurar configurações a partir de: $CONFIG_BACKUP"
    
    # Extrair backup de configurações
    if tar -xzf "$CONFIG_BACKUP"; then
        print_status "success" "Configurações restauradas"
    else
        print_status "error" "Falha ao restaurar configurações"
    fi
else
    print_status "warning" "Backup de configurações não encontrado"
fi

# 6. Reinstalar dependências
echo ""
print_status "info" "Reinstalando dependências..."

# Limpar node_modules
print_status "info" "Limpando node_modules..."
rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules 2>/dev/null || true

# Reinstalar dependências
if npm ci; then
    print_status "success" "Dependências do workspace raiz instaladas"
else
    print_status "error" "Falha ao instalar dependências do workspace raiz"
fi

# Instalar dependências dos subprojetos
for dir in backend frontend shared; do
    if [ -d "$dir" ] && [ -f "$dir/package.json" ]; then
        print_status "info" "Instalando dependências: $dir"
        if (cd "$dir" && npm ci); then
            print_status "success" "Dependências instaladas: $dir"
        else
            print_status "warning" "Falha ao instalar dependências: $dir"
        fi
    fi
done

# 7. Validar rollback
echo ""
print_status "info" "Validando rollback..."

# Executar validação básica
if [ -f "scripts/validate-dependencies.sh" ]; then
    print_status "info" "Executando validação de dependências..."
    if bash scripts/validate-dependencies.sh; then
        print_status "success" "Validação concluída"
    else
        print_status "warning" "Validação apresentou avisos - verifique os logs"
    fi
fi

# Resumo final
echo ""
echo "📊 RESUMO DO ROLLBACK"
echo "===================="
print_status "success" "Rollback concluído com sucesso!"
echo ""
print_status "info" "Ações executadas:"
echo "   ✅ Banco de dados restaurado: $LATEST_BACKUP"
[ -n "$BACKUP_TAG" ] && echo "   ✅ Código revertido para: $BACKUP_TAG"
[ -n "$CONFIG_BACKUP" ] && echo "   ✅ Configurações restauradas: $CONFIG_BACKUP"
echo "   ✅ Dependências reinstaladas"
echo ""
print_status "info" "Backup do estado anterior salvo em: $CURRENT_BACKUP"
echo ""
print_status "warning" "IMPORTANTE: Verifique se o sistema está funcionando corretamente"
print_status "info" "Execute os testes: npm run test"
print_status "info" "Inicie o desenvolvimento: npm run dev"