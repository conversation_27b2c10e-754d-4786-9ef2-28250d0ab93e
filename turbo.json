{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["package.json", "turbo.json", ".env.local", ".env"], "globalEnv": ["NODE_ENV", "DATABASE_URL", "JWT_SECRET", "BCRYPT_ROUNDS", "FRONTEND_URL", "PORT"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"], "env": ["NODE_ENV"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "test/**/*.ts", "**/*.test.ts", "**/*.spec.ts"]}, "test:unit": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "**/*.test.ts"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "**/*.integration.test.ts"]}, "test:e2e": {"dependsOn": ["build"], "cache": false, "inputs": ["src/**/*.ts", "src/**/*.tsx", "cypress/**/*", "**/*.cy.ts"]}, "lint": {"outputs": [], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"]}, "lint:fix": {"outputs": [], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"]}, "type-check": {"dependsOn": ["^build"], "outputs": [], "inputs": ["src/**/*.ts", "src/**/*.tsx", "tsconfig.json"]}, "db:migrate": {"cache": false, "inputs": ["prisma/**/*"]}, "db:seed": {"cache": false, "dependsOn": ["db:migrate"], "inputs": ["prisma/**/*", "src/scripts/seed.ts"]}, "db:generate": {"cache": false, "inputs": ["prisma/schema.prisma"]}, "clean": {"cache": false}}}