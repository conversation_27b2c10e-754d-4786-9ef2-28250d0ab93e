{"name": "personal-finance-manager", "version": "1.0.0", "description": "Personal Finance Manager - Webapp para controle financeiro pessoal e familiar", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "turbo run dev", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "turbo run build", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "test": "turbo run test", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:headed": "cypress run --headed", "test:e2e:chrome": "cypress run --browser chrome", "test:e2e:firefox": "cypress run --browser firefox", "test:component": "cypress run --component", "test:component:open": "cypress open --component", "test:all": "npm run test:backend && npm run test:frontend && npm run test:e2e && npm run test:component", "test:coverage": "./scripts/test-coverage.sh", "test:coverage:frontend": "cd frontend && npm run test:coverage", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:combine": "./scripts/test-coverage.sh combine", "test:coverage:thresholds": "./scripts/test-coverage.sh thresholds", "test:coverage:badge": "./scripts/test-coverage.sh badge", "test:backend:unit": "cd backend && npm run test:unit", "test:backend:integration": "cd backend && npm run test:integration", "test:frontend:unit": "cd frontend && npm run test:unit", "test:frontend:components": "cd frontend && npm run test:components", "test:performance:gates": "cd backend && npm run test:performance:gates", "test:smoke": "cd backend && npm run test:smoke", "test:pre-deploy": "npm run test:smoke && npm run test:health", "test:health": "cd backend && npm run test:health", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "format:check": "prettier --check \"**/*.{js,ts,tsx,json,md}\"", "security:scan": "npm audit --audit-level high && cd backend && npm run security:scan", "cypress:install": "cypress install", "cypress:verify": "cypress verify", "lint": "turbo run lint", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "type-check": "turbo run type-check", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:shared", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:shared": "cd shared && npm install", "backup": "bash scripts/backup-pre-refactor.sh", "validate": "bash scripts/validate-dependencies.sh", "rollback": "bash scripts/rollback.sh", "optimize:schema": "cd backend && npm run optimize:schema"}, "devDependencies": {"turbo": "^1.13.4", "concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0", "cypress": "^13.6.0", "@cypress/code-coverage": "^3.12.0", "cypress-real-events": "^1.11.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["finance", "personal-finance", "budget", "money-management", "react", "nodejs", "postgresql", "prisma"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "."}}