2025-07-09T00:56:19.887728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/controllers/transaction-old.controller.ts")}
2025-07-09T00:56:19.887822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T00:56:20.408318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/daemon/f0c6a2d2de7a3381-turbo.log.2025-07-09"), AnchoredSystemPathBuf(".turbo/daemon/f0c6a2d2de7a3381-turbo.log.2025-07-08")}
2025-07-09T00:56:20.408335Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-09T00:56:45.317789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/schemas/transaction.schemas.ts")}
2025-07-09T00:56:45.318068Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T00:56:46.173244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/schemas/transaction.schemas.ts")}
2025-07-09T00:56:46.173258Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T00:56:46.174300Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-09T00:57:10.374067Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/controllers/goal-progress-history.controller.ts")}
2025-07-09T00:57:10.374084Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T00:57:27.170453Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/controllers/goal-progress-history.controller.ts")}
2025-07-09T00:57:27.170476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T00:57:53.169086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/controllers/goal-progress-history.controller.ts")}
2025-07-09T00:57:53.169104Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:10:01.462690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/middleware/validation.middleware.ts")}
2025-07-09T03:10:01.468584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:11:43.144073Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/prisma/schema.prisma")}
2025-07-09T03:11:43.144149Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:13:24.839818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/routes/recurring-transaction.routes.ts")}
2025-07-09T03:13:24.839914Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:14:42.239306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/routes/recurring-transaction.routes.ts")}
2025-07-09T03:14:42.239421Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:18:59.501687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/services/installment.service.ts")}
2025-07-09T03:18:59.501841Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:18:59.823041Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/src/services/installment.service.ts")}
2025-07-09T03:18:59.823057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-09T03:18:59.824017Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
