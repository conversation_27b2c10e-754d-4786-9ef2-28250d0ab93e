2025-08-05T13:37:45.810811Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-08-05T13:37:45.810845Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-08-05T13:37:48.110061Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/vite.config.ts.timestamp-1754401068084-c2e572d0aec9f8.mjs")}
2025-08-05T13:37:48.110087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:37:48.211715Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/vite.config.ts.timestamp-1754401068084-c2e572d0aec9f8.mjs")}
2025-08-05T13:37:48.211731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:37:48.211869Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:40:18.747683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/ui/alert.tsx")}
2025-08-05T13:40:18.754968Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:40:49.452419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/ui/dialog.tsx")}
2025-08-05T13:40:49.452480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:40:49.848542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/ui/dialog.tsx")}
2025-08-05T13:40:49.848555Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:40:49.848726Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:41:04.216475Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/ui/dialog.tsx")}
2025-08-05T13:41:04.216633Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:42:01.427987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/transactions/TransactionsStatsCards.tsx")}
2025-08-05T13:42:01.429266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:42:01.689854Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/transactions/TransactionsStatsCards.tsx")}
2025-08-05T13:42:01.689873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:42:01.690081Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:42:20.419090Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/budgets/BudgetProgressBar.tsx")}
2025-08-05T13:42:20.419110Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:42:39.219347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/goals/GoalCard.tsx")}
2025-08-05T13:42:39.219580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:43:16.320118Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/lib/transaction-utils.ts")}
2025-08-05T13:43:16.320182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:43:29.826582Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:43:29.826596Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:43:30.036740Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:43:30.036759Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:43:30.037482Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:44:08.344581Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/design-system/tokens.md")}
2025-08-05T13:44:08.344637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:44:08.872795Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/design-system/tokens.md")}
2025-08-05T13:44:08.872813Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:44:08.872998Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:45:35.724910Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/layout/Sidebar.tsx")}
2025-08-05T13:45:35.724976Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:45:52.426278Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/layout/Sidebar.tsx")}
2025-08-05T13:45:52.426292Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:46:22.626264Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/index.css")}
2025-08-05T13:46:22.626328Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:46:51.225857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/index.css")}
2025-08-05T13:46:51.226311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:47:20.529264Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/README.md")}
2025-08-05T13:47:20.529565Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:47:20.711415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/README.md")}
2025-08-05T13:47:20.711577Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:47:20.771661Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:47:43.829196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/PALETA_APLICADA.md")}
2025-08-05T13:47:43.829216Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:48:59.957905Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:48:59.959638Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:49:00.403469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:49:00.403488Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:49:00.404416Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:49:59.433018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:49:59.433093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:50:37.761554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:50:37.761615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:50:37.914009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardCharts.tsx")}
2025-08-05T13:50:37.914024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:50:37.915574Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:51:06.233488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardStatsCards.tsx")}
2025-08-05T13:51:06.233548Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:51:06.334794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardStatsCards.tsx")}
2025-08-05T13:51:06.334808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:51:06.334973Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:51:38.936488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardStatsCards.tsx")}
2025-08-05T13:51:38.936569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:51:39.114025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/dashboard/DashboardStatsCards.tsx")}
2025-08-05T13:51:39.114039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:51:39.114170Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T13:53:42.442703Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("DARK_OCEAN_IMPLEMENTATION_SUMMARY.md")}
2025-08-05T13:53:42.443274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-08-05T13:54:57.547166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/2.cookie")}
2025-08-05T13:54:57.548621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-08-05T13:55:00.842139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/vite.config.ts.timestamp-1754402100754-ebb6c244e9e02.mjs")}
2025-08-05T13:55:00.842238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T13:55:00.869518Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:03:03.258196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:03:03.258628Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:03:32.358555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:03:32.358616Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:03:45.859843Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:03:45.859860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:03:58.057039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:03:58.057057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:04:19.865463Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:04:19.865576Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:04:42.559752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:04:42.559984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:04:57.962710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:04:57.962728Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:24.360591Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:24.360668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:24.659666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:24.659686Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:24.659898Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:05:35.460163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:35.460197Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:35.580170Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:35.580184Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:35.664069Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:05:46.563465Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:46.563489Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:46.666946Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/ComponentShowcase.tsx")}
2025-08-05T14:05:46.666981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:05:46.668065Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:06:05.681071Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:06:05.681093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:06:40.972353Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:06:40.972464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:06:41.249756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:06:41.250322Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:06:41.251701Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:07:04.370666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:07:04.370705Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:07:34.867265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:07:34.867372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:07:58.270156Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:07:58.270174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:08:10.865798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:08:10.865819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:08:33.365901Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:08:33.366233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:08:34.100883Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:08:34.100928Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:08:34.103233Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-05T14:08:49.067523Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:08:49.067550Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:09:01.577638Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:09:01.577657Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:09:14.371491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:09:14.372071Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:09:44.568761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/src/components/examples/DesignSystemShowcase.tsx")}
2025-08-05T14:09:44.568904Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:14:24.076757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/3.cookie")}
2025-08-05T14:14:24.076816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-08-05T14:14:26.276937Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/vite.config.ts.timestamp-1754403266206-bd16ffe78f193.mjs")}
2025-08-05T14:14:26.276968Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-08-05T14:14:26.323074Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
