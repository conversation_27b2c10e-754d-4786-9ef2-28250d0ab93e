2025-07-07T07:42:00.118834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-07-07T07:42:00.119736Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T07:42:00.419501Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/3.cookie"), AnchoredSystemPathBuf(".turbo/cookies/2.cookie"), AnchoredSystemPathBuf(".turbo/cookies/4.cookie")}
2025-07-07T07:42:00.419515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T07:42:00.518586Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/.turbo"), AnchoredSystemPathBuf("frontend/.turbo")}
2025-07-07T07:42:00.518603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }, WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-07T07:42:12.116654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/5.cookie")}
2025-07-07T07:42:12.116750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T07:59:14.894190Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("shared/package.json")}
2025-07-07T07:59:14.894243Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-shared"), path: AnchoredSystemPathBuf("shared") }}))
2025-07-07T07:59:44.996092Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("frontend/package.json")}
2025-07-07T07:59:44.996106Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-frontend"), path: AnchoredSystemPathBuf("frontend") }}))
2025-07-07T08:01:56.490641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/prisma/schema.prisma")}
2025-07-07T08:01:56.490664Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-07T08:05:49.785210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("backend/prisma/schema.prisma")}
2025-07-07T08:05:49.785227Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-backend"), path: AnchoredSystemPathBuf("backend") }}))
2025-07-07T08:38:06.214578Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("shared/node_modules")}
2025-07-07T08:38:06.214888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-shared"), path: AnchoredSystemPathBuf("shared") }}))
2025-07-07T08:38:07.309060Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T08:41:01.595788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("shared/package-lock.json")}
2025-07-07T08:41:01.595897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("personal-finance-shared"), path: AnchoredSystemPathBuf("shared") }}))
2025-07-07T08:42:27.807188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/6.cookie")}
2025-07-07T08:42:27.807246Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T08:42:28.415349Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/7.cookie")}
2025-07-07T08:42:28.415407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T08:42:37.493005Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/8.cookie"), AnchoredSystemPathBuf(".turbo/cookies/10.cookie"), AnchoredSystemPathBuf(".turbo/cookies/9.cookie")}
2025-07-07T08:42:37.493021Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
