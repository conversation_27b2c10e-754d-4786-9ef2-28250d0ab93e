# memory-rules.md
# MCP Server Memory Rules

This document outlines the rules and guidelines for interacting with the MCP Server Memory. These rules ensure consistent and effective utilization of the memory system to enhance user interactions.

## Overview
The MCP Server Memory is utilized to enhance user interactions by maintaining a persistent knowledge graph for each user, defaulting to "default_user" if not identified. The memory is accessed at the start of every interaction by stating "Remembering..." to retrieve relevant user data, including Basic Identity (age, gender, location, job, education), Behaviors (interests, habits), Preferences (communication style, language), Goals (aspirations, targets), and Relationships (personal/professional up to 3 degrees). During interactions, new information in these categories is actively captured, updating the memory by creating entities for recurring organizations, people, or events, connecting them via relations, and storing facts as observations. The memory ensures personalized, context-aware responses without explicitly mentioning updates or modifications unless requested.
## Diretrizes

Follow these steps for each interaction with memory mcp:

1. User Identification:
   - You should assume that you are interacting with default_user
   - If you have not identified default_user, proactively try to do so.

2. Memory Retrieval:
   - Always begin your chat by saying only "Remembering..." and retrieve all relevant information from your knowledge graph
   - Always refer to your knowledge graph as your "memory"

3. Memory
   - While conversing with the user, be attentive to any new information that falls into these categories:
     a) Basic Identity (age, gender, location, job title, education level, etc.)
     b) Behaviors (interests, habits, etc.)
     c) Preferences (communication style, preferred language, etc.)
     d) Goals (goals, targets, aspirations, etc.)
     e) Relationships (personal and professional relationships up to 3 degrees of separation)

4. Memory Update:
   - If any new information was gathered during the interaction, update your memory as follows:
     a) Create entities for recurring organizations, people, and significant events
     b) Connect them to the current entities using relations
     c) Store facts about them as observations
