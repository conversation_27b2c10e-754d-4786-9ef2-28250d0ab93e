{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "alwaysAllow": ["create_entities", "create_relations", "delete_relations", "delete_observations", "add_observations", "delete_entities", "read_graph", "search_nodes", "open_nodes"]}}}